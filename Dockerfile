# syntax=docker/dockerfile:1
# Build
FROM --platform=$BUILDPLATFORM public.ecr.aws/docker/library/golang:1.23-alpine AS build

ENV GOPRIVATE=github.com/foxcorp-product* \
  CGO_ENABLED=0

WORKDIR /opt/app

RUN apk --no-cache update && \
      apk --no-cache add git ca-certificates && \
      rm -rf /var/cache/apk/*

RUN --mount=type=secret,id=ghuser \
  --mount=type=secret,id=ghtoken \
  echo "machine github.com login $(cat /run/secrets/ghuser) password $(cat /run/secrets/ghtoken)" >> /root/.netrc

COPY go.mod ./
COPY go.sum ./
RUN go mod download

COPY . ./

RUN go build -o app .

# Deploy
FROM public.ecr.aws/docker/library/alpine:latest

EXPOSE 80

WORKDIR /opt/run

COPY --from=build /opt/app/app /usr/local/bin/app

ENTRYPOINT ["/usr/local/bin/app"]
