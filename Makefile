.PHONY: all clean test build fmt lint
.EXPORT_ALL_VARIABLES:

# You can change these values to be more suitable to what you need.
APP_NAME=receiptverify
SERVICE_VER=v1

# GOPRIVATE bypasses verifying the checksum of private repos against https://sum.golang.org (1.13 update)
GOPRIVATE=github.com/foxcorp-product*

# This really should never change since it's in the dockerfile, but just in case
PORT=8080
MAINFILES = main.go
SHELL=/bin/bash
CHECK_COVERAGE = ./_scripts/check_coverage.sh
#This is sets the config profiders to run locally

all: test

deps:
	@echo "===> Vendoring dependencies"
	@go get -u=patch

test: lint
	@echo "===> Testing"
	go test -race -count=1 -coverprofile=coverage.out -covermode=atomic ./...
	@echo "===> Checking coverage"
	chmod +x $(CHECK_COVERAGE) && $(CHECK_COVERAGE)

clean:
	@echo "===> Cleaning"
	@go clean ./...

fmt:
	@echo "===> Formatting"
	@go fmt ./...

lint:
	@echo "===> Linting with vet"
	@go vet ./...

run-local:
	@echo "===>  Running $(APP_NAME) locally"
	@echo '[]' > apikeys.json
	@ENABLED_CONFIG_PROVIDERS="local,env,default" USE_LOCAL_APIKEYS="apikeys.json" SERVICE_ENV="local" go run -race $(MAINFILES) && rm apikeys.json

run-localstack:
	@echo "===>  Running $(APP_NAME) locally with localstack"
	@echo '[]' > apikeys.json
	@AWS_LOCALSTACK="true" AWS_REGION="us-east-1" SERVICE_BU="comm" SERVICE_ENV="local" USE_LOCAL_APIKEYS="apikeys.json" go run -race $(MAINFILES) && rm apikeys.json
