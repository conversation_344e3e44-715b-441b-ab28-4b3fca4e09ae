# receiptverify

## Local development
### Requirements
- Go 1.21
- AWS Access
- Akamai EAA Access

### Installation
1. Clone the repository
2. Run `make deps` to install vendor dependencies.

### Run locally
1. Get and set AWS Credentials. See [AWS CLI with Ok<PERSON>](https://teamfox.atlassian.net/wiki/spaces/CMSPSTAGE/pages/********/AWS+CLI+with+<PERSON>ta)

   1.1 Make sure to set the AWS region, either in `.aws/credentials` or in the environment
    ````
    export AWS_REGION=us-east-1
    ```` 

   1.2 If AWS profiles are being used, make sure to set it as well.
2. Login into Akamai EAA if needed
3. Configuration will be automatically loaded from AWS and default providers, according to the account set the `.aws/credentials`. If there are values that need to be overwritten locally, add those to `localValues` in `config/config.go`

4. Run `make run-local`

### Run locally with localstack
1. Ensure the `awslocal` is set correctly to use the localstack credentials
```
# If you don't have it, install AWS CLI, then
# on your preferable profile file (.bash_profile, .zprofile, ...) set the following alias
alias awslocal="AWS_ACCESS_KEY_ID=test AWS_SECRET_ACCESS_KEY=test AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION:-us-east-1} aws --endpoint-url=http://${LOCALSTACK_HOST:-localhost}:4566"
```
2. Ensure in localstack terraform your `provider "aws"` is pointing to the correct localstack urls, credentials and configurations. See more details here https://docs.localstack.cloud/user-guide/integrations/terraform/#endpoint-configuration
3. With localstack running, initilize terraform resources:
   3.1. `cd deploy/localstack && terraform init`
   3.2. `cd deploy/localstack && terraform apply`
4. Run `make run-localstack`
5. (Optional) Running with VSCode or an IDE, reuse the same env vars passed in the `run-localstack`, in order to take advantage of debugging.
   5.1. Example for VSCode
   ```json
   {
      "version": "0.2.0",
      "configurations": [
         {
               "name": "Launch Package",
               "type": "go",
               "request": "launch",
               "mode": "auto",
               "program": "${workspaceFolder}",
               "env": {
                  "AWS_LOCALSTACK": "true",
                  "AWS_REGION": "us-east-1",
                  "SERVICE_ENV": "local",
                  "SERVICE_BU": "comm"
               }
         }
      ]
   }   
   ```

### Testing
To run unit tests, run `make test`. Test coverage should be at least 80%


## Contributing
### Add new configuration properties

To add new a configuration property:
1. Add it to the `Config` struct in `config/config.go`
2. Set the value in any of the available origins:
   - Environment
   - AWS Parameter Store
   - AWS Secrets Manager
   - Default (aka `defaultValues` in `config/config.go`)

**IMPORTANT**
You can chose which provider you will use setting this on the ENABLED_CONFIG_PROVIDERS environment variable.
The value should looks like: ENABLED_CONFIG_PROVIDERS=local,env,parametersStore,secretsManager,default. That means this is a comma separated values. you can see all option in config.go

### Add new handlers
To add a new handler,
1. Create the handler function in a new file
2. Add the route to the service routes pointing it to the handler

## Building the docker image

If you want to build the docker image, you will need a personal access token
from your Github account. This personal access token will have to be logged in
with the foxcorp-product Github org SSO.

The personal access token is used to have access to the private repositories on
which your service depends on during build time. To build it, you'll run this
command substituting the values that need to be substituted:

```
GH_USER=your-github-user \
  GH_TOKEN=your-personal-access-token \
  docker build --build-arg GH_USER --build-arg GH_TOKEN \
  service .
```

You'll have your service available from the `service` image in docker.

