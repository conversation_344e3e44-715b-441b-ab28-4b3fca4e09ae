#/bin/bash

declare COVE<PERSON><PERSON>_AMOUNT
declare COVERAGE_THRESHOLD

# this amount equals to 50% according to our current aim.
COVERAGE_THRESHOLD=50
COVERAGE_FILE_PATH=coverage.out

# we're extracting just the integer part of total coverage value, which is a float.
COVERAGE_AMOUNT=$(go tool cover -func=$COVERAGE_FILE_PATH | grep total | awk '{print substr($3, 1, length($3)-3)}')

if [ $COVERAGE_AMOUNT -lt $COVERAGE_THRESHOLD ]; then
    echo "Coverage Check: Coverage ($COVERAGE_AMOUNT%) did not reach the $COVERAGE_THRESHOLD% threshold."
    exit 1
fi

echo "Coverage Check: Coverage is fine at $COVERAGE_AMOUNT%."
