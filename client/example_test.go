package client

import (
	"context"
	"time"

	"github.com/foxcorp-product/entitlement-sdk/circuitbreaker"
	"github.com/foxcorp-product/entitlement-sdk/stats"
	log "github.com/sirupsen/logrus"
)

func ExampleNew() {
	s, _ := stats.New("service_name", "v1", stats.WithDevMode())

	// usually comes from configuration
	const apiURL = "http://localhost"
	const defaultTimeout = time.Second * 3

	// creates a receiptverify client
	client, err := New(
		WithApiVersion("v1"),
		WithCircuitOptions(
			circuitbreaker.WithBaseURLPath(true),
			circuitbreaker.WithURL(apiURL),
			circuitbreaker.WithTimeout(defaultTimeout),
			circuitbreaker.WithStatsdClient(s),
			circuitbreaker.WithAPMTracer(),
		),
	)
	if err != nil {
		log.Fatal(err.Error())
	}

	_, err = client.Verify(context.Background(), V1ReceiptVerifyRequestInput{
		AppServiceID: "serviceID",
		Receipt:      "receipt",
		AllowExpired: true,
	}, "requestApiKeyOrOther")

	if err != nil {
		log.Fatal(err.Error())
	}
}
