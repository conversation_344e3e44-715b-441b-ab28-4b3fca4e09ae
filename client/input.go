package client

import (
	"encoding/json"
	"errors"
)

var (
	ErrUnableToMarshalInput  = errors.New("unable to marshal input")
	errAppServiceIdRequired  = errors.New("appServiceId is required")
	errTransactionIdRequired = errors.New("transactionId is required")
)

type V1ReceiptVerifyRequestInput struct {
	AppServiceID   string `json:"appServiceId"`
	Receipt        string `json:"receipt"`
	PlatformUserID string `json:"platformUserId"`
	AllowExpired   bool   `json:"allowExpired"`
}

// Body returns the byte array expected to send on body request to Verify endpoint
func (in V1ReceiptVerifyRequestInput) Body() ([]byte, error) {
	return parseBody(in)
}

type V1ValidateRokuTransactionInput struct {
	AppServiceID  string `json:"appServiceId"`
	TransactionID string `json:"transactionId"`
}

// Body returns the byte array expected to send on body request to ValidateRokuTransaction endpoint
func (in V1ValidateRokuTransactionInput) Body() ([]byte, error) {
	if in.AppServiceID == "" {
		return nil, errAppServiceIdRequired
	}

	if in.TransactionID == "" {
		return nil, errTransactionIdRequired
	}

	return parseBody(in)
}

type V1ValidateGoogleTransactionInput struct {
	PackageName   string `json:"packageName"`
	ProductID     string `json:"productId"`
	PurchaseToken string `json:"purchaseToken"`
}

// Body returns the byte array expected to send on body request to ValidateGoogleTransaction endpoint
func (in V1ValidateGoogleTransactionInput) Body() ([]byte, error) {
	return parseBody(in)
}

type V1ValidateAmazonTransactionInput struct {
	AppServiceId string `json:"appServiceId"`
	Receipt      string `json:"receipt"`
	AllowExpired bool   `json:"allowExpired"`
	AmazonUserId string `json:"amazonUserId"`
}

// Body returns the byte array expected to send on body request to ValidateAmazonTransaction endpoint
func (in V1ValidateAmazonTransactionInput) Body() ([]byte, error) {
	return parseBody(in)
}

type V1ValidateStripeTransactionInput struct {
	AppServiceID    string `json:"appServiceId"`
	PaymentIntentID string `json:"paymentIntentId"`
}

// Body returns the byte array expected to send on body request to ValidateStripeTransaction endpoint
func (in V1ValidateStripeTransactionInput) Body() ([]byte, error) {
	if in.AppServiceID == "" {
		return nil, errAppServiceIdRequired
	}

	return parseBody(in)
}

type V1ValidateSamsungTransactionInput struct {
	AppServiceID   string `json:"appServiceId"`
	InvoiceID      string `json:"invoiceId"`
	PlatformUserID string `json:"platformUserId"`
}

// Body returns the byte array expected to send on body request to ValidateSamsungTransaction endpoint
func (in V1ValidateSamsungTransactionInput) Body() ([]byte, error) {
	if in.AppServiceID == "" {
		return nil, errAppServiceIdRequired
	}

	return parseBody(in)
}

type V1ValidateAppleTransactionInput struct {
	AppServiceID   string `json:"appServiceId"`
	JWSTransaction string `json:"receipt"`
	AllowExpired   bool   `json:"allowExpired"`
}

// Body returns the byte array expected to send on body request to ValidateAppleTransaction endpoint
func (in V1ValidateAppleTransactionInput) Body() ([]byte, error) {
	return parseBody(in)
}

type V1AppleSubscriptionStatusInput struct {
	AppServiceID string `json:"appServiceId"`
	Receipt      string `json:"receipt"`
	TxID         string `json:"originalTransactionId"`
	BundleID     string `json:"bundleId"`
}

// Body returns the byte array expected to send on body request to Verify Apple Subscription Status endpoint
func (in V1AppleSubscriptionStatusInput) Body() ([]byte, error) {
	return parseBody(in)
}

func parseBody(v any) ([]byte, error) {
	b, err := json.Marshal(v)
	if err != nil {
		return nil, ErrUnableToMarshalInput
	}

	return b, nil
}
