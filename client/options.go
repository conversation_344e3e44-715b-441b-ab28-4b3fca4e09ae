package client

import (
	"fmt"

	"github.com/foxcorp-product/entitlement-sdk/circuitbreaker"
)

// Options a function that can take a *Client a change its values
type Options func(c *Client) error

// WithApiVersion by default, all service endpoints are prefixed with `/v1`.
// This option allows caller to change that behavior by overriding using the v string passed.
func WithApiVersion(v string) Options {
	return func(c *Client) error {
		// skip for a empty value
		if v == "" {
			return nil
		}

		if v[0] != '/' {
			v = fmt.Sprintf("/%s", v)
		}

		c.apiVer = v
		return nil
	}
}

func WithCircuitOptions(opts ...circuitbreaker.Option) Options {
	return func(c *Client) error {
		c.cbOpts = opts
		return nil
	}
}
