package client

import (
	"github.com/awa/go-iap/amazon"
	"time"

	planclient "github.com/foxcorp-product/commerce-plan/client"
	"github.com/foxcorp-product/commerce-receiptverify/iap"
)

type V1VerifyResponse struct {
	Valid                  bool             `json:"valid"`
	StartDate              string           `json:"startDate,omitempty"`
	EndDate                string           `json:"endDate,omitempty"`
	FreeTrial              bool             `json:"freeTrial"`
	NextPaymentDate        string           `json:"nextPaymentDate,omitempty"`
	TransactionID          string           `json:"transactionId,omitempty"`
	Store                  iap.Store        `json:"store,omitempty"`
	Plan                   *planclient.Plan `json:"plan,omitempty"`
	LastPaymentTime        string           `json:"lastPaymentTime,omitempty"`
	FlexibleOfferPrice     string           `json:"flexibleOfferPrice,omitempty"`
	IsFlexibleOfferApplied bool             `json:"isFlexibleOfferApplied,omitempty"`
}

type V1Charged struct {
	Amount   float64    `json:"amount"`
	Currency string     `json:"currency"`
	Date     *time.Time `json:"date"`
}

type V1VerifyResponseRoku struct {
	CancelledTransactionIds []string   `json:"cancelledTransactionIds,omitempty"`
	Valid                   bool       `json:"valid"`
	StartDate               string     `json:"startDate,omitempty"`
	EndDate                 string     `json:"endDate,omitempty"`
	TransactionID           string     `json:"transactionId,omitempty"`
	Store                   iap.Store  `json:"store,omitempty"`
	ErrorMessage            string     `json:"errorMessage,omitempty"`
	ErrorType               string     `json:"errorType,omitempty"`
	Charged                 *V1Charged `json:"charged,omitempty"`
}

type V1PlayStoreResponse struct {
	OrderId                   string     `json:"orderId,omitempty"`
	PurchaseDate              string     `json:"productDate,omitempty"`
	ExpiryDate                string     `json:"expiryDate,omitempty"`
	AutoRenewing              bool       `json:"autoRenewing,omitempty"`
	OneTime                   bool       `json:"oneTime,omitempty"`
	UserInitiatedCancellation bool       `json:"userInitiatedCancellation,omitempty"`
	LinkedPurchaseToken       string     `json:"linkedPurchaseToken,omitempty"`
	ResumeAtDate              *string    `json:"resumeAtDate,omitempty"`
	Charged                   *V1Charged `json:"charged,omitempty"`
}

type V1IAPResponse struct {
	AutoRenewing       bool               `json:"autoRenewing"`
	ReceiptID          string             `json:"receiptId"`
	ProductType        string             `json:"productType"`
	ProductID          string             `json:"productId"`
	PurchaseDate       int64              `json:"purchaseDate"`
	RenewalDate        int64              `json:"renewalDate"`
	FreeTrialEndDate   int64              `json:"freeTrialEndDate"`
	GracePeriodEndDate int64              `json:"gracePeriodEndDate"`
	CancelDate         int64              `json:"cancelDate"`
	CancelReason       int64              `json:"cancelReason"`
	TestTransaction    bool               `json:"testTransaction"`
	BetaProduct        bool               `json:"betaProduct"`
	ParentProductID    string             `json:"parentProductId"`
	Quantity           int64              `json:"quantity"`
	Term               string             `json:"term"`
	TermSku            string             `json:"termSku"`
	Promotions         []amazon.Promotion `json:"promotions"`
	ActivePromotion    bool               `json:"activePromotion"`
	Charged            *V1Charged         `json:"charged,omitempty"`
}

type V1VerifyResponseStripe struct {
	Valid           bool      `json:"valid"`
	StartDate       string    `json:"startDate,omitempty"`
	EndDate         string    `json:"endDate,omitempty"`
	PaymentIntentID string    `json:"paymentIntentID,omitempty"`
	Store           iap.Store `json:"store,omitempty"`
}

type V1VerifyResponseSamsung struct {
	Valid                  bool       `json:"valid"`
	InvoiceID              string     `json:"invoiceID,omitempty"`
	StartDate              string     `json:"startDate,omitempty"`
	EndDate                string     `json:"endDate,omitempty"`
	NextPaymentDate        string     `json:"nextPaymentDate,omitempty"`
	FreeTrial              bool       `json:"freeTrial"`
	Store                  iap.Store  `json:"store,omitempty"`
	Charged                *V1Charged `json:"charged,omitempty"`
	LastPaymentTime        string     `json:"lastPaymentTime,omitempty"`
	FlexibleOfferPrice     string     `json:"flexibleOfferPrice,omitempty"`
	IsFlexibleOfferApplied bool       `json:"isFlexibleOfferApplied,omitempty"`
}

type V1AppleStatusRes struct {
	HasUsedIntro bool `json:"hasUsedIntro"`
	HasUsedTrial bool `json:"hasUsedTrial"`
	HasUsedPromo bool `json:"hasUsedPromo"`
	IsSubscribed bool `json:"isSubscribed"`
	IsChurned    bool `json:"isChurned"`
}
