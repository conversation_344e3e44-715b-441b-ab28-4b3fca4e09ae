package client

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"

	"github.com/foxcorp-product/commerce-receiptverify/internal/response"
	"github.com/foxcorp-product/entitlement-sdk/circuitbreaker"
)

var (
	ErrInvalidResponse          = errors.New("request returned an invalid response")
	ErrEmptyResults             = errors.New("request returned empty results")
	ErrUnmarshalling            = errors.New("unable to unmarshal the response")
	ErrUnableToExecuteOperation = errors.New("unable to execute operation")
	ErrUnableToCreateClient     = errors.New("unable to create instance of client")
	ErrInvalidRequest           = errors.New("unable to create request")
	ErrUnableToExecuteRequest   = errors.New("unable to execute request")
	// ErrReadFullBody is returned when the io.ReadAll to []byte to ensure a
	// resp.Body is read until EOF, receives an error.
	ErrReadFullBody = errors.New("unable to copy bytes from resp.Body")
)

const (
	postReceiptVerify                 = "/receiptverify"
	postVerifyAppleSubscriptionStatus = "/receiptverify/apple-status"
	postValidateRokuTransaction       = "/receiptverify/validate-roku-transaction"
	postValidateGoogleTransaction     = "/receiptverify/validate-google-transaction"
	postValidateAmazonTransaction     = "/receiptverify/validate-amazon-transaction"
	postValidateStripeTransaction     = "/receiptverify/validate-stripe-transaction"
	postValidateSamsungTransaction    = "/receiptverify/validate-samsung-transaction"
	postValidateAppleTransaction      = "/receiptverify/validate-appstore-transaction"
	defaultApiVer                     = "/v1"
	serviceName                       = "receiptverify"
	apiKeyHeader                      = "x-api-key"
)

type CircuitAPI interface {
	DoWithContext(ctx context.Context, req *http.Request) (resp *http.Response, err error)
}

// Client holds a set operations that can be performed against plan service
type Client struct {
	apiVer string
	cb     CircuitAPI
	cbOpts []circuitbreaker.Option
}

// New creates an instance of a plan service Client
func New(opts ...Options) (*Client, error) {
	c := &Client{}
	c.ensureDefaultValues()

	for _, opt := range opts {
		if err := opt(c); err != nil {
			return nil, fmt.Errorf("%w: %w", ErrUnableToCreateClient, err)
		}
	}

	// creates instance of circuit breaker
	// adding the correct command mapping and service name
	cb, err := circuitbreaker.New(
		append(c.cbOpts, circuitbreaker.WithServiceName(serviceName))...,
	)
	if err != nil {
		return nil, fmt.Errorf("%w: %w", ErrUnableToCreateClient, err)
	}
	c.cb = cb

	return c, nil
}

func (c *Client) ensureDefaultValues() {
	if c.apiVer == "" {
		c.apiVer = defaultApiVer
	}
}

// Verify given the in as V1ReceiptVerifyRequestInput returns a V1VerifyResponse
// behind the scenes the operation POST /receiptverify will be executed.
func (c *Client) Verify(ctx context.Context, in V1ReceiptVerifyRequestInput, apiKey string) (V1VerifyResponse, error) {
	addlHeaders := http.Header{}
	addlHeaders.Add(apiKeyHeader, apiKey)
	body, err := in.Body()
	if err != nil {
		return V1VerifyResponse{}, fmt.Errorf("%w: %w", ErrUnableToExecuteOperation, err)
	}

	path := fmt.Sprintf("%s%s", c.apiVer, postReceiptVerify)

	// create request
	req, err := http.NewRequest(http.MethodPost, path, io.NopCloser(bytes.NewBuffer(body)))
	if err != nil {
		return V1VerifyResponse{}, fmt.Errorf("%w: %w", ErrInvalidRequest, err)
	}
	req.Header = addlHeaders
	// send request
	res, err := c.cb.DoWithContext(ctx, req)
	if err != nil {
		return V1VerifyResponse{}, fmt.Errorf("%w: %w", ErrUnableToExecuteRequest, err)
	}

	// make sure to read the full body from stream
	if res.Body != nil {
		defer func() {
			_ = res.Body.Close()
		}()

		var body []byte
		body, err = io.ReadAll(res.Body)
		if err != nil {
			return V1VerifyResponse{}, fmt.Errorf("%w: %v", ErrReadFullBody, err)
		}
		res.Body = io.NopCloser(bytes.NewBuffer(body))
	}

	// when status is valid tries to unmarshal de response
	if res.StatusCode == http.StatusOK {
		if res.Body == nil {
			return V1VerifyResponse{}, nil
		}

		var out V1VerifyResponse
		// unmarshal the results
		err = circuitbreaker.Unmarshal(res, &out)
		if err != nil {
			return V1VerifyResponse{}, fmt.Errorf("%w: %w: %w", ErrInvalidResponse, ErrUnmarshalling, err)
		}

		return out, nil
	}

	// status is invalid, tries to unmarshal the errors
	// when is 404, no need to unmarshal, just return error
	if res.StatusCode == http.StatusNotFound {
		return V1VerifyResponse{}, ErrEmptyResults
	}

	// tries to unmarshal the errors
	err = errorUnmarshal(res)
	return V1VerifyResponse{}, fmt.Errorf("%w: %w", ErrInvalidResponse, err)
}

// ValidateRokuTransaction given the in as V1ValidateRokuTransactionInput validates the transaction and returns V1VerifyResponseRoku
// behind the scenes the operation POST /receiptverify/validate-roku-transaction will be executed.
func (c *Client) ValidateRokuTransaction(ctx context.Context, in V1ValidateRokuTransactionInput) (V1VerifyResponseRoku, error) {
	body, err := in.Body()
	if err != nil {
		return V1VerifyResponseRoku{}, fmt.Errorf("%w: %w", ErrUnableToExecuteOperation, err)
	}

	path := fmt.Sprintf("%s%s", c.apiVer, postValidateRokuTransaction)

	// create request
	req, err := http.NewRequest(http.MethodPost, path, io.NopCloser(bytes.NewBuffer(body)))
	if err != nil {
		return V1VerifyResponseRoku{}, fmt.Errorf("%w: %w", ErrInvalidRequest, err)
	}
	// send request
	res, err := c.cb.DoWithContext(ctx, req)
	if err != nil {
		return V1VerifyResponseRoku{}, fmt.Errorf("%w: %w", ErrUnableToExecuteRequest, err)
	}

	// make sure to read the full body from stream
	if res.Body != nil {
		defer func() {
			_ = res.Body.Close()
		}()

		var body []byte
		body, err = io.ReadAll(res.Body)
		if err != nil {
			return V1VerifyResponseRoku{}, fmt.Errorf("%w: %v", ErrReadFullBody, err)
		}
		res.Body = io.NopCloser(bytes.NewBuffer(body))
	}

	// when status is valid tries to unmarshal de response
	if res.StatusCode == http.StatusOK {
		if res.Body == nil {
			return V1VerifyResponseRoku{}, nil
		}

		var out V1VerifyResponseRoku
		// unmarshal the results
		err = circuitbreaker.Unmarshal(res, &out)
		if err != nil {
			return V1VerifyResponseRoku{}, fmt.Errorf("%w: %w: %w", ErrInvalidResponse, ErrUnmarshalling, err)
		}

		return out, nil
	}

	// status is invalid, tries to unmarshal the errors
	// when is 404, no need to unmarshal, just return error
	if res.StatusCode == http.StatusNotFound {
		return V1VerifyResponseRoku{}, ErrEmptyResults
	}

	// tries to unmarshal the errors
	err = errorUnmarshal(res)
	return V1VerifyResponseRoku{}, fmt.Errorf("%w: %w", ErrInvalidResponse, err)
}

// ValidateGoogleTransaction given the in as V1ValidateGoogleTransactionInput validates the transaction and returns V1PlayStoreResponse
// behind the scenes the operation POST /receiptverify/validate-google-transaction will be executed.
func (c *Client) ValidateGoogleTransaction(ctx context.Context, in V1ValidateGoogleTransactionInput) (V1PlayStoreResponse, error) {
	body, err := in.Body()
	if err != nil {
		return V1PlayStoreResponse{}, fmt.Errorf("%w: %w", ErrUnableToExecuteOperation, err)
	}

	path := fmt.Sprintf("%s%s", c.apiVer, postValidateGoogleTransaction)

	// create request
	req, err := http.NewRequest(http.MethodPost, path, io.NopCloser(bytes.NewBuffer(body)))
	if err != nil {
		return V1PlayStoreResponse{}, fmt.Errorf("%w: %w", ErrInvalidRequest, err)
	}
	// send request
	res, err := c.cb.DoWithContext(ctx, req)
	if err != nil {
		return V1PlayStoreResponse{}, fmt.Errorf("%w: %w", ErrUnableToExecuteRequest, err)
	}

	// make sure to read the full body from stream
	if res.Body != nil {
		defer func() {
			_ = res.Body.Close()
		}()

		var body []byte
		body, err = io.ReadAll(res.Body)
		if err != nil {
			return V1PlayStoreResponse{}, fmt.Errorf("%w: %v", ErrReadFullBody, err)
		}
		res.Body = io.NopCloser(bytes.NewBuffer(body))
	}

	// when status is valid tries to unmarshal de response
	if res.StatusCode == http.StatusOK {
		if res.Body == nil {
			return V1PlayStoreResponse{}, nil
		}

		var out V1PlayStoreResponse
		// unmarshal the results
		err = circuitbreaker.Unmarshal(res, &out)
		if err != nil {
			return V1PlayStoreResponse{}, fmt.Errorf("%w: %w: %w", ErrInvalidResponse, ErrUnmarshalling, err)
		}

		return out, nil
	}

	// status is invalid, tries to unmarshal the errors
	// when is 404, no need to unmarshal, just return error
	if res.StatusCode == http.StatusNotFound {
		return V1PlayStoreResponse{}, ErrEmptyResults
	}

	// tries to unmarshal the errors
	err = errorUnmarshal(res)
	return V1PlayStoreResponse{}, fmt.Errorf("%w: %w", ErrInvalidResponse, err)
}

// ValidateAmazonTransaction given the in as V1ValidateAmazonTransactionInput validates the transaction and returns
// behind the scenes the operation POST /receiptverify/validate-amazon-transaction will be executed.
func (c *Client) ValidateAmazonTransaction(ctx context.Context, in V1ValidateAmazonTransactionInput) (V1IAPResponse, error) {
	body, err := in.Body()
	if err != nil {
		return V1IAPResponse{}, fmt.Errorf("%w: %w", ErrUnableToExecuteOperation, err)
	}

	path := fmt.Sprintf("%s%s", c.apiVer, postValidateAmazonTransaction)

	// create request
	req, err := http.NewRequest(http.MethodPost, path, io.NopCloser(bytes.NewBuffer(body)))
	if err != nil {
		return V1IAPResponse{}, fmt.Errorf("%w: %w", ErrInvalidRequest, err)
	}
	// send request
	res, err := c.cb.DoWithContext(ctx, req)
	if err != nil {
		return V1IAPResponse{}, fmt.Errorf("%w: %w", ErrUnableToExecuteRequest, err)
	}

	// make sure to read the full body from stream
	if res.Body != nil {
		defer func() {
			_ = res.Body.Close()
		}()

		var body []byte
		body, err = io.ReadAll(res.Body)
		if err != nil {
			return V1IAPResponse{}, fmt.Errorf("%w: %v", ErrReadFullBody, err)
		}
		res.Body = io.NopCloser(bytes.NewBuffer(body))
	}

	// when status is valid tries to unmarshal de response
	if res.StatusCode == http.StatusOK {
		if res.Body == nil {
			return V1IAPResponse{}, nil
		}

		var out V1IAPResponse
		// unmarshal the results
		err = circuitbreaker.Unmarshal(res, &out)
		if err != nil {
			return V1IAPResponse{}, fmt.Errorf("%w: %w: %w", ErrInvalidResponse, ErrUnmarshalling, err)
		}

		return out, nil
	}

	// status is invalid, tries to unmarshal the errors
	// when is 404, no need to unmarshal, just return error
	if res.StatusCode == http.StatusNotFound {
		return V1IAPResponse{}, ErrEmptyResults
	}

	// tries to unmarshal the errors
	err = errorUnmarshal(res)
	return V1IAPResponse{}, fmt.Errorf("%w: %w", ErrInvalidResponse, err)
}

// ValidateStripeTransaction given the in as V1ValidateStripeTransactionInput validates the transaction and returns V1VerifyResponseSamsung
// behind the scenes the operation POST /receiptverify/validate-stripe-transaction will be executed.
func (c *Client) ValidateStripeTransaction(ctx context.Context, in V1ValidateStripeTransactionInput) (V1VerifyResponseStripe, error) {
	body, err := in.Body()
	if err != nil {
		return V1VerifyResponseStripe{}, fmt.Errorf("%w: %w", ErrUnableToExecuteOperation, err)
	}

	path := fmt.Sprintf("%s%s", c.apiVer, postValidateStripeTransaction)

	// create request
	req, err := http.NewRequest(http.MethodPost, path, io.NopCloser(bytes.NewBuffer(body)))
	if err != nil {
		return V1VerifyResponseStripe{}, fmt.Errorf("%w: %w", ErrInvalidRequest, err)
	}
	// send request
	res, err := c.cb.DoWithContext(ctx, req)
	if err != nil {
		return V1VerifyResponseStripe{}, fmt.Errorf("%w: %w", ErrUnableToExecuteRequest, err)
	}

	// make sure to read the full body from stream
	if res.Body != nil {
		defer func() {
			_ = res.Body.Close()
		}()

		var body []byte
		body, err = io.ReadAll(res.Body)
		if err != nil {
			return V1VerifyResponseStripe{}, fmt.Errorf("%w: %v", ErrReadFullBody, err)
		}
		res.Body = io.NopCloser(bytes.NewBuffer(body))
	}

	// when status is valid tries to unmarshal de response
	if res.StatusCode == http.StatusOK {
		if res.Body == nil {
			return V1VerifyResponseStripe{}, nil
		}

		var out V1VerifyResponseStripe
		// unmarshal the results
		err = circuitbreaker.Unmarshal(res, &out)
		if err != nil {
			return V1VerifyResponseStripe{}, fmt.Errorf("%w: %w: %w", ErrInvalidResponse, ErrUnmarshalling, err)
		}

		return out, nil
	}

	// status is invalid, tries to unmarshal the errors
	// when is 404, no need to unmarshal, just return error
	if res.StatusCode == http.StatusNotFound {
		return V1VerifyResponseStripe{}, ErrEmptyResults
	}

	// tries to unmarshal the errors
	err = errorUnmarshal(res)
	return V1VerifyResponseStripe{}, fmt.Errorf("%w: %w", ErrInvalidResponse, err)
}

// ValidateSamsungTransaction given the in as V1ValidateSamsungTransactionInput validates the transaction and returns V1VerifyResponseSamsung
// behind the scenes the operation POST /receiptverify/validate-samsung-transaction will be executed.
func (c *Client) ValidateSamsungTransaction(ctx context.Context, in V1ValidateSamsungTransactionInput) (V1VerifyResponseSamsung, error) {
	body, err := in.Body()
	if err != nil {
		return V1VerifyResponseSamsung{}, fmt.Errorf("%w: %w", ErrUnableToExecuteOperation, err)
	}

	path := fmt.Sprintf("%s%s", c.apiVer, postValidateSamsungTransaction)

	// create request
	req, err := http.NewRequest(http.MethodPost, path, io.NopCloser(bytes.NewBuffer(body)))
	if err != nil {
		return V1VerifyResponseSamsung{}, fmt.Errorf("%w: %w", ErrInvalidRequest, err)
	}
	// send request
	res, err := c.cb.DoWithContext(ctx, req)
	if err != nil {
		return V1VerifyResponseSamsung{}, fmt.Errorf("%w: %w", ErrUnableToExecuteRequest, err)
	}

	// make sure to read the full body from stream
	if res.Body != nil {
		defer func() {
			_ = res.Body.Close()
		}()

		var body []byte
		body, err = io.ReadAll(res.Body)
		if err != nil {
			return V1VerifyResponseSamsung{}, fmt.Errorf("%w: %v", ErrReadFullBody, err)
		}
		res.Body = io.NopCloser(bytes.NewBuffer(body))
	}

	// when status is valid tries to unmarshal de response
	if res.StatusCode == http.StatusOK {
		if res.Body == nil {
			return V1VerifyResponseSamsung{}, nil
		}

		var out V1VerifyResponseSamsung
		// unmarshal the results
		err = circuitbreaker.Unmarshal(res, &out)
		if err != nil {
			return V1VerifyResponseSamsung{}, fmt.Errorf("%w: %w: %w", ErrInvalidResponse, ErrUnmarshalling, err)
		}

		return out, nil
	}

	// status is invalid, tries to unmarshal the errors
	// when is 404, no need to unmarshal, just return error
	if res.StatusCode == http.StatusNotFound {
		return V1VerifyResponseSamsung{}, ErrEmptyResults
	}

	// tries to unmarshal the errors
	err = errorUnmarshal(res)
	return V1VerifyResponseSamsung{}, fmt.Errorf("%w: %w", ErrInvalidResponse, err)
}

// ValidateAppleTransaction given the in as V1AppleSubscriptionStatusInput validates the transaction and returns V1VerifyResponse
// behind the scenes the operation POST /receiptverify/validate-appstore-transaction will be executed.
func (c *Client) ValidateAppleTransaction(ctx context.Context, in V1ValidateAppleTransactionInput) (V1VerifyResponse, error) {
	body, err := in.Body()
	if err != nil {
		return V1VerifyResponse{}, fmt.Errorf("%w: %w", ErrUnableToExecuteOperation, err)
	}

	path := fmt.Sprintf("%s%s", c.apiVer, postValidateAppleTransaction)

	// create request
	req, err := http.NewRequest(http.MethodPost, path, io.NopCloser(bytes.NewBuffer(body)))
	if err != nil {
		return V1VerifyResponse{}, fmt.Errorf("%w: %w", ErrInvalidRequest, err)
	}
	// send request
	res, err := c.cb.DoWithContext(ctx, req)
	if err != nil {
		return V1VerifyResponse{}, fmt.Errorf("%w: %w", ErrUnableToExecuteRequest, err)
	}

	// make sure to read the full body from stream
	if res.Body != nil {
		defer func() {
			_ = res.Body.Close()
		}()

		var body []byte
		body, err = io.ReadAll(res.Body)
		if err != nil {
			return V1VerifyResponse{}, fmt.Errorf("%w: %v", ErrReadFullBody, err)
		}
		res.Body = io.NopCloser(bytes.NewBuffer(body))
	}

	// when status is valid tries to unmarshal de response
	if res.StatusCode == http.StatusOK {
		if res.Body == nil {
			return V1VerifyResponse{}, nil
		}

		var out V1VerifyResponse
		// unmarshal the results
		err = circuitbreaker.Unmarshal(res, &out)
		if err != nil {
			return V1VerifyResponse{}, fmt.Errorf("%w: %w: %w", ErrInvalidResponse, ErrUnmarshalling, err)
		}

		return out, nil
	}

	// status is invalid, tries to unmarshal the errors
	// when is 404, no need to unmarshal, just return error
	if res.StatusCode == http.StatusNotFound {
		return V1VerifyResponse{}, ErrEmptyResults
	}

	// tries to unmarshal the errors
	err = errorUnmarshal(res)
	return V1VerifyResponse{}, fmt.Errorf("%w: %w", ErrInvalidResponse, err)
}

// VerifyAppleSubscriptionStatus given the in as V1AppleSubscriptionStatusInput verifies the subscription and returns V1AppleStatusRes
// behind the scenes the operation POST /receiptverify/apple-status will be executed.
func (c *Client) VerifyAppleSubscriptionStatus(ctx context.Context, in V1AppleSubscriptionStatusInput, apiKey string) (V1AppleStatusRes, error) {
	addlHeaders := http.Header{}
	addlHeaders.Add(apiKeyHeader, apiKey)
	body, err := in.Body()
	if err != nil {
		return V1AppleStatusRes{}, fmt.Errorf("%w: %w", ErrUnableToExecuteOperation, err)
	}

	path := fmt.Sprintf("%s%s", c.apiVer, postVerifyAppleSubscriptionStatus)

	// create request
	req, err := http.NewRequest(http.MethodPost, path, io.NopCloser(bytes.NewBuffer(body)))
	if err != nil {
		return V1AppleStatusRes{}, fmt.Errorf("%w: %w", ErrInvalidRequest, err)
	}
	req.Header = addlHeaders
	// send request
	res, err := c.cb.DoWithContext(ctx, req)
	if err != nil {
		return V1AppleStatusRes{}, fmt.Errorf("%w: %w", ErrUnableToExecuteRequest, err)
	}

	// make sure to read the full body from stream
	if res.Body != nil {
		defer func() {
			_ = res.Body.Close()
		}()

		var body []byte
		body, err = io.ReadAll(res.Body)
		if err != nil {
			return V1AppleStatusRes{}, fmt.Errorf("%w: %v", ErrReadFullBody, err)
		}
		res.Body = io.NopCloser(bytes.NewBuffer(body))
	}

	// when status is valid tries to unmarshal de response
	if res.StatusCode == http.StatusOK {
		if res.Body == nil {
			return V1AppleStatusRes{}, nil
		}

		var out V1AppleStatusRes
		// unmarshal the results
		err = circuitbreaker.Unmarshal(res, &out)
		if err != nil {
			return V1AppleStatusRes{}, fmt.Errorf("%w: %w: %w", ErrInvalidResponse, ErrUnmarshalling, err)
		}

		return out, nil
	}

	// status is invalid, tries to unmarshal the errors
	// when is 404, no need to unmarshal, just return error
	if res.StatusCode == http.StatusNotFound {
		return V1AppleStatusRes{}, ErrEmptyResults
	}

	// tries to unmarshal the errors
	err = errorUnmarshal(res)
	return V1AppleStatusRes{}, fmt.Errorf("%w: %w", ErrInvalidResponse, err)
}

// errorUnmarshal tries to unmarshal the error based on Delta format
func errorUnmarshal(res *http.Response) error {
	// when content type is missing, unmarshal always fails
	if c := res.Header.Get("Content-Type"); c == "" {
		return fmt.Errorf("%w: %w: statuscode=%d", ErrUnmarshalling, fmt.Errorf("missing content type"), res.StatusCode)
	}

	var errRes response.Error
	if err := circuitbreaker.Unmarshal(res, &errRes); err != nil {
		return fmt.Errorf("%w: %w: statuscode=%d", ErrUnmarshalling, err, res.StatusCode)
	}

	if len(errRes.Message) > 0 {
		return fmt.Errorf("%w: %s: statuscode=%d", ErrUnmarshalling, errRes.Message, res.StatusCode)
	}

	return fmt.Errorf("%w: statuscode=%d", ErrUnmarshalling, res.StatusCode)
}
