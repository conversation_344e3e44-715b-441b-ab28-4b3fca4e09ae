package client

import (
	"bytes"
	"context"
	"errors"
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

type mockCbAPI struct {
	doWithContextFn func(ctx context.Context, req *http.Request) (resp *http.Response, err error)
}

func (m mockCbAPI) DoWithContext(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
	if m.doWithContextFn != nil {
		return m.doWithContextFn(ctx, req)
	}
	return &http.Response{}, nil
}

func TestNew(t *testing.T) {
	type args struct {
		opts []Options
		cb   CircuitAPI
	}
	tests := []struct {
		name    string
		args    args
		want    *Client
		wantErr error
	}{
		{
			name: "when building options return error, should return error",
			args: args{
				opts: []Options{
					func(c *Client) error {
						return errors.New("invalid")
					},
				},
			},
			wantErr: ErrUnableToCreateClient,
		},
		{
			name: "when building is okay, should return a *Client",
			args: args{
				cb: &mockCbAPI{},
			},
			want: &Client{
				apiVer: "/v1", // this is the default version
			},
		},
		{
			name: "when override the api version, should return a *Client",
			args: args{
				cb: &mockCbAPI{},
				opts: []Options{
					WithApiVersion("v2"),
				},
			},
			want: &Client{
				apiVer: "/v2",
			},
		},
		{
			name: "when override the api version with slash as prefix, should return a *Client",
			args: args{
				cb: &mockCbAPI{},
				opts: []Options{
					WithApiVersion("/v3"),
				},
			},
			want: &Client{
				apiVer: "/v3",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := New(tt.args.opts...)
			if tt.wantErr != nil {
				require.NotNil(t, err)
				assert.ErrorIs(t, err, tt.wantErr)
				return
			}
			assert.IsType(t, tt.want, got)
			assert.EqualValues(t, tt.want.apiVer, got.apiVer)
		})
	}
}

func TestClient_Verify(t *testing.T) {
	type fields struct {
		cb CircuitAPI
	}
	type args struct {
		ctx context.Context
		in  V1ReceiptVerifyRequestInput
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr error
	}{
		{
			name: "when input is valid but got error from server, should return error",
			args: args{
				in: V1ReceiptVerifyRequestInput{
					AppServiceID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusInternalServerError,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
						}, errors.New("unable to execute request")
					},
				},
			},
			wantErr: ErrUnableToExecuteRequest,
		},
		{
			name: "when response is not 200 with not expected body, should return error",
			args: args{
				in: V1ReceiptVerifyRequestInput{
					AppServiceID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusFailedDependency,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
							Body: io.NopCloser(bytes.NewBuffer([]byte("some valid response"))),
						}, nil
					},
				},
			},
			wantErr: ErrInvalidResponse,
		},
		{
			name: "when response is 200 with no data, should not return error",
			args: args{
				in: V1ReceiptVerifyRequestInput{
					AppServiceID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusOK,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
						}, nil
					},
				},
			},
		},
		{
			name: "when unmarshal not fails and response has results, should not return error",
			args: args{
				in: V1ReceiptVerifyRequestInput{
					AppServiceID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusOK,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
							Body: io.NopCloser(bytes.NewBuffer([]byte("some valid response"))),
						}, nil
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				cb: tt.fields.cb,
			}
			_, err := c.Verify(tt.args.ctx, tt.args.in, "apikey")
			if tt.wantErr != nil {
				require.NotNil(t, err)
				assert.ErrorIs(t, err, tt.wantErr)
			}
		})
	}
}

func TestClient_ValidateAmazonTransaction(t *testing.T) {
	type fields struct {
		cb CircuitAPI
	}
	type args struct {
		ctx context.Context
		in  V1ValidateAmazonTransactionInput
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr error
	}{
		{
			name: "when input is valid but got error from server, should return error",
			args: args{
				in: V1ValidateAmazonTransactionInput{
					AppServiceId: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusInternalServerError,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
						}, errors.New("unable to execute request")
					},
				},
			},
			wantErr: ErrUnableToExecuteRequest,
		},
		{
			name: "when response is not 200 with not expected body, should return error",
			args: args{
				in: V1ValidateAmazonTransactionInput{
					AppServiceId: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusFailedDependency,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
							Body: io.NopCloser(bytes.NewBuffer([]byte("some valid response"))),
						}, nil
					},
				},
			},
			wantErr: ErrInvalidResponse,
		},
		{
			name: "when response is 200 with no data, should not return error",
			args: args{
				in: V1ValidateAmazonTransactionInput{
					AppServiceId: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusOK,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
						}, nil
					},
				},
			},
		},
		{
			name: "when unmarshal not fails and response has results, should not return error",
			args: args{
				in: V1ValidateAmazonTransactionInput{
					AppServiceId: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusOK,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
							Body: io.NopCloser(bytes.NewBuffer([]byte("some valid response"))),
						}, nil
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				cb: tt.fields.cb,
			}
			_, err := c.ValidateAmazonTransaction(tt.args.ctx, tt.args.in)
			if tt.wantErr != nil {
				require.NotNil(t, err)
				assert.ErrorIs(t, err, tt.wantErr)
			}
		})
	}
}

func TestClient_ValidateGoogleTransaction(t *testing.T) {
	type fields struct {
		cb CircuitAPI
	}
	type args struct {
		ctx context.Context
		in  V1ValidateGoogleTransactionInput
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr error
	}{
		{
			name: "when input is valid but got error from server, should return error",
			args: args{
				in: V1ValidateGoogleTransactionInput{
					ProductID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusInternalServerError,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
						}, errors.New("unable to execute request")
					},
				},
			},
			wantErr: ErrUnableToExecuteRequest,
		},
		{
			name: "when response is not 200 with not expected body, should return error",
			args: args{
				in: V1ValidateGoogleTransactionInput{
					ProductID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusFailedDependency,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
							Body: io.NopCloser(bytes.NewBuffer([]byte("some valid response"))),
						}, nil
					},
				},
			},
			wantErr: ErrInvalidResponse,
		},
		{
			name: "when response is 200 with no data, should not return error",
			args: args{
				in: V1ValidateGoogleTransactionInput{
					ProductID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusOK,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
						}, nil
					},
				},
			},
		},
		{
			name: "when unmarshal not fails and response has results, should not return error",
			args: args{
				in: V1ValidateGoogleTransactionInput{
					ProductID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusOK,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
							Body: io.NopCloser(bytes.NewBuffer([]byte("some valid response"))),
						}, nil
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				cb: tt.fields.cb,
			}
			_, err := c.ValidateGoogleTransaction(tt.args.ctx, tt.args.in)
			if tt.wantErr != nil {
				require.NotNil(t, err)
				assert.ErrorIs(t, err, tt.wantErr)
			}
		})
	}
}

func TestClient_ValidateRokuTransaction(t *testing.T) {
	type fields struct {
		cb CircuitAPI
	}
	type args struct {
		ctx context.Context
		in  V1ValidateRokuTransactionInput
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr error
	}{
		{
			name: "when input is valid but got error from server, should return error",
			args: args{
				in: V1ValidateRokuTransactionInput{
					AppServiceID:  "appserviceID",
					TransactionID: "transactionID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusInternalServerError,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
						}, errors.New("unable to execute request")
					},
				},
			},
			wantErr: ErrUnableToExecuteRequest,
		},
		{
			name: "when response is not 200 with not expected body, should return error",
			args: args{
				in: V1ValidateRokuTransactionInput{
					AppServiceID:  "appserviceID",
					TransactionID: "transactionID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusFailedDependency,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
							Body: io.NopCloser(bytes.NewBuffer([]byte("some valid response"))),
						}, nil
					},
				},
			},
			wantErr: ErrInvalidResponse,
		},
		{
			name: "when response is 200 with no data, should not return error",
			args: args{
				in: V1ValidateRokuTransactionInput{
					AppServiceID:  "appserviceID",
					TransactionID: "transactionID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusOK,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
						}, nil
					},
				},
			},
		},
		{
			name: "when unmarshal not fails and response has results, should not return error",
			args: args{
				in: V1ValidateRokuTransactionInput{
					AppServiceID:  "appserviceID",
					TransactionID: "transactionID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusOK,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
							Body: io.NopCloser(bytes.NewBuffer([]byte("some valid response"))),
						}, nil
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				cb: tt.fields.cb,
			}
			_, err := c.ValidateRokuTransaction(tt.args.ctx, tt.args.in)
			if tt.wantErr != nil {
				require.NotNil(t, err)
				assert.ErrorIs(t, err, tt.wantErr)
			}
		})
	}
}

func TestClient_ValidateSamsungTransaction(t *testing.T) {
	type fields struct {
		cb CircuitAPI
	}
	type args struct {
		ctx context.Context
		in  V1ValidateSamsungTransactionInput
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr error
	}{
		{
			name: "when input is valid but got error from server, should return error",
			args: args{
				in: V1ValidateSamsungTransactionInput{
					AppServiceID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusInternalServerError,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
						}, errors.New("unable to execute request")
					},
				},
			},
			wantErr: ErrUnableToExecuteRequest,
		},
		{
			name: "when response is not 200 with not expected body, should return error",
			args: args{
				in: V1ValidateSamsungTransactionInput{
					AppServiceID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusFailedDependency,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
							Body: io.NopCloser(bytes.NewBuffer([]byte("some valid response"))),
						}, nil
					},
				},
			},
			wantErr: ErrInvalidResponse,
		},
		{
			name: "when response is 200 with no data, should not return error",
			args: args{
				in: V1ValidateSamsungTransactionInput{
					AppServiceID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusOK,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
						}, nil
					},
				},
			},
		},
		{
			name: "when unmarshal not fails and response has results, should not return error",
			args: args{
				in: V1ValidateSamsungTransactionInput{
					AppServiceID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusOK,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
							Body: io.NopCloser(bytes.NewBuffer([]byte("some valid response"))),
						}, nil
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				cb: tt.fields.cb,
			}
			_, err := c.ValidateSamsungTransaction(tt.args.ctx, tt.args.in)
			if tt.wantErr != nil {
				require.NotNil(t, err)
				assert.ErrorIs(t, err, tt.wantErr)
			}
		})
	}
}

func TestClient_ValidateStripeTransaction(t *testing.T) {
	type fields struct {
		cb CircuitAPI
	}
	type args struct {
		ctx context.Context
		in  V1ValidateStripeTransactionInput
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr error
	}{
		{
			name: "when input is valid but got error from server, should return error",
			args: args{
				in: V1ValidateStripeTransactionInput{
					AppServiceID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusInternalServerError,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
						}, errors.New("unable to execute request")
					},
				},
			},
			wantErr: ErrUnableToExecuteRequest,
		},
		{
			name: "when response is not 200 with not expected body, should return error",
			args: args{
				in: V1ValidateStripeTransactionInput{
					AppServiceID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusFailedDependency,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
							Body: io.NopCloser(bytes.NewBuffer([]byte("some valid response"))),
						}, nil
					},
				},
			},
			wantErr: ErrInvalidResponse,
		},
		{
			name: "when response is 200 with no data, should not return error",
			args: args{
				in: V1ValidateStripeTransactionInput{
					AppServiceID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusOK,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
						}, nil
					},
				},
			},
		},
		{
			name: "when unmarshal not fails and response has results, should not return error",
			args: args{
				in: V1ValidateStripeTransactionInput{
					AppServiceID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusOK,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
							Body: io.NopCloser(bytes.NewBuffer([]byte("some valid response"))),
						}, nil
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				cb: tt.fields.cb,
			}
			_, err := c.ValidateStripeTransaction(tt.args.ctx, tt.args.in)
			if tt.wantErr != nil {
				require.NotNil(t, err)
				assert.ErrorIs(t, err, tt.wantErr)
			}
		})
	}
}

func TestClient_ValidateAppleTransaction(t *testing.T) {
	type fields struct {
		cb CircuitAPI
	}
	type args struct {
		ctx context.Context
		in  V1ValidateAppleTransactionInput
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr error
	}{
		{
			name: "when input is valid but got error from server, should return error",
			args: args{
				in: V1ValidateAppleTransactionInput{
					AppServiceID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusInternalServerError,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
						}, errors.New("unable to execute request")
					},
				},
			},
			wantErr: ErrUnableToExecuteRequest,
		},
		{
			name: "when response is not 200 with not expected body, should return error",
			args: args{
				in: V1ValidateAppleTransactionInput{
					AppServiceID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusFailedDependency,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
							Body: io.NopCloser(bytes.NewBuffer([]byte("some valid response"))),
						}, nil
					},
				},
			},
			wantErr: ErrInvalidResponse,
		},
		{
			name: "when response is 200 with no data, should not return error",
			args: args{
				in: V1ValidateAppleTransactionInput{
					AppServiceID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusOK,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
						}, nil
					},
				},
			},
		},
		{
			name: "when unmarshal not fails and response has results, should not return error",
			args: args{
				in: V1ValidateAppleTransactionInput{
					AppServiceID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusOK,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
							Body: io.NopCloser(bytes.NewBuffer([]byte("some valid response"))),
						}, nil
					}}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				cb: tt.fields.cb,
			}
			_, err := c.ValidateAppleTransaction(tt.args.ctx, tt.args.in)
			if tt.wantErr != nil {
				require.NotNil(t, err)
				assert.ErrorIs(t, err, tt.wantErr)
			}
		})
	}
}

func TestClient_VerifyAppleSubscriptionStatus(t *testing.T) {
	type fields struct {
		cb CircuitAPI
	}
	type args struct {
		ctx context.Context
		in  V1AppleSubscriptionStatusInput
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr error
	}{
		{
			name: "when input is valid but got error from server, should return error",
			args: args{
				in: V1AppleSubscriptionStatusInput{
					AppServiceID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusInternalServerError,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
						}, errors.New("unable to execute request")
					},
				},
			},
			wantErr: ErrUnableToExecuteRequest,
		},
		{
			name: "when response is not 200 with not expected body, should return error",
			args: args{
				in: V1AppleSubscriptionStatusInput{
					AppServiceID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusFailedDependency,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
							Body: io.NopCloser(bytes.NewBuffer([]byte("some valid response"))),
						}, nil
					},
				},
			},
			wantErr: ErrInvalidResponse,
		},
		{
			name: "when response is 200 with no data, should not return error",
			args: args{
				in: V1AppleSubscriptionStatusInput{
					AppServiceID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusOK,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
						}, nil
					},
				},
			},
		},
		{
			name: "when unmarshal not fails and response has results, should not return error",
			args: args{
				in: V1AppleSubscriptionStatusInput{
					AppServiceID: "appserviceID",
				},
			},
			fields: fields{
				cb: &mockCbAPI{
					doWithContextFn: func(ctx context.Context, req *http.Request) (resp *http.Response, err error) {
						return &http.Response{
							StatusCode: http.StatusOK,
							Header: http.Header{
								"Content-Type": []string{"application/json"},
							},
							Body: io.NopCloser(bytes.NewBuffer([]byte("some valid response"))),
						}, nil
					}}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				cb: tt.fields.cb,
			}
			_, err := c.VerifyAppleSubscriptionStatus(tt.args.ctx, tt.args.in, "some-service-id")
			if tt.wantErr != nil {
				require.NotNil(t, err)
				assert.ErrorIs(t, err, tt.wantErr)
			}
		})
	}
}
