// Package config provides methods and definitions to load service
// configuration.
//
// To add a new value for the configuration, add it to the Config
// struct. The value will be later retrieved from different origins
// using providers.
//
// If there are any default values, add those to defaultValues
package config

import (
	"time"

	"github.com/foxcorp-product/entitlement-sdk/config"
	"github.com/gookit/validate"
)

// If you wan't disable a provider locally sets ENABLED_CONFIG_PROVIDERS env var
// the local providers is not used in EKS cluster
const AllProviders = "local,env,parametersStore,secretsManager,default"

var (
	// DefaultValues are the ones to be used in case no value is found any other Provider.
	//
	// Fill out any values that are needed.
	// Default values can also be loaded from a JSON file. In this case, types
	// are limited to basic ones: int, string, float, bool and struct
	DefaultValues = map[string]interface{}{
		"Loglevel":    "info",
		"ServicePort": "8080",
		"Retries":     3,
	}

	// LocalValues are the ones to be used in local development. Anything that
	// should be read first for local development should be put here.
	// These values can also be loaded from a JSON file. In this case, types
	// are limited to basic ones: int, string, float, bool and struct.
	LocalValues = map[string]interface{}{
		"Loglevel":    "debug",
		"ServicePort": "8080",
		"JWTPublicKeys": config.MapStringString{
			"key": "value",
		},
		"HydraPublicKeys": config.MapStringString{
			"key": "value",
		},
		"AppleFOXSportsSecretKey":               "",
		"AppleFOXNationSecretKey":               "",
		"AppleFOXNewsInternationalSecretKey":    "",
		"AppleD2cSecretKey":                     "",
		"AppleAppStoreAPIKey":                   "",
		"AppleSDKAppStoreAPIKey":                "",
		"RokuFOXSportsSecretKey":                "",
		"RokuFOXNationSecretKey":                "",
		"RokuD2cSecretKey":                      "",
		"AmazonFOXSportsSecretKey":              "",
		"AmazonFOXNationSecretKey":              "",
		"AmazonD2cSecretKey":                    "",
		"GoogleFOXSportsSecretKey":              "",
		"GoogleFOXNationSecretKey":              "",
		"GoogleD2cSecretKey":                    "",
		"AppleSDKFOXNationSecretKey":            "",
		"AppleSDKFOXNewsInternationalSecretKey": "",
		"AppleSDKD2cSecretKey":                  "",
		"AppStoreSDKAPIKeyId":                   "",
		"AppStoreAPIKeyId":                      "",
	}
)

// Config is the service configuration. Add here everything needed by
// the service
type Config struct {
	// Retries used by retryIapRequest method
	// for reference check https://github.com/foxbroadcasting/cpe-receiptverify/blob/64445b0c51baa48d082873295dea9ed9d386988f/iap/receipt/appstore.go#L363
	Retries     int    `validate:"required" cfgproviders:"local,env,parametersStore,default"`
	Loglevel    string `validate:"required" cfgproviders:"local,env,parametersStore,default"`
	ServicePort string `validate:"required" cfgproviders:"local,env,parametersStore,default"`

	AppStoreAPIIssuerId string `validate:"required" cfgproviders:"local,env,parametersStore,default"`
	AppStoreAPIKeyId    string `validate:"required" cfgproviders:"local,env,secretsManager,default"` ////TODO: do we need fox one secrets for these too?
	AppleAppStoreAPIKey string `validate:"required" cfgproviders:"local,env,secretsManager,default"`

	AppStoreSDKApiIssuerId string `validate:"required" cfgproviders:"local,env,parametersStore,default"`
	AppleSDKAppStoreAPIKey string `validate:"required" cfgproviders:"local,env,secretsManager,default"`
	AppStoreSDKAPIKeyId    string `validate:"required" cfgproviders:"local,env,secretsManager,default"`

	AppleFOXSportsSecretKey            string `validate:"required" cfgproviders:"local,env,secretsManager,default"`
	AppleFOXNationSecretKey            string `validate:"required" cfgproviders:"local,env,secretsManager,default"`
	AppleFOXNewsInternationalSecretKey string `validate:"required" cfgproviders:"local,env,secretsManager,default"`
	AppleD2cSecretKey                  string `validate:"required" cfgproviders:"local,env,secretsManager,default"`

	RokuFOXSportsSecretKey string `validate:"required" cfgproviders:"local,env,secretsManager,default"`
	RokuFOXNationSecretKey string `validate:"required" cfgproviders:"local,env,secretsManager,default"`
	RokuD2cSecretKey       string `validate:"required" cfgproviders:"local,env,secretsManager,default"`

	AmazonFOXSportsSecretKey string `validate:"required" cfgproviders:"local,env,secretsManager,default"`
	AmazonFOXNationSecretKey string `validate:"required" cfgproviders:"local,env,secretsManager,default"`
	AmazonD2cSecretKey       string `validate:"required" cfgproviders:"local,env,secretsManager,default"`

	GoogleFOXSportsSecretKey string `validate:"required" cfgproviders:"local,env,secretsManager,default"`
	GoogleFOXNationSecretKey string `validate:"required" cfgproviders:"local,env,secretsManager,default"`
	GoogleD2cSecretKey       string `validate:"required" cfgproviders:"local,env,secretsManager,default"`

	AppleSDKFOXNationSecretKey            string `validate:"required" cfgproviders:"local,env,secretsManager,default"`
	AppleSDKFOXNewsInternationalSecretKey string `validate:"required" cfgproviders:"local,env,secretsManager,default"`
	AppleSDKD2cSecretKey                  string `validate:"required" cfgproviders:"local,env,secretsManager,default"`

	JWTPublicKeys   config.MapStringString `validate:"RequiredIfEmpty:HydraPublicKeys" cfgproviders:"local,env,secretsManager,default"`
	HydraPublicKeys config.MapStringString `validate:"RequiredIfEmpty:JWTPublicKeys" cfgproviders:"local,env,secretsManager,default"`
	// relative to LaunchDarkly configuration
	LDApiKey string `validate:"required" cfgproviders:"local,env,secretsManager,default"`
}

func (c Config) RequiredIfEmpty(name any, fields ...string) bool {
	data, err := validate.FromStruct(c)
	if err != nil {
		return false
	}
	for _, field := range fields {
		v, ok, zero := data.TryGet(field)
		if ok && !zero && !validate.IsEmpty(v) {
			return ok
		}
	}
	return !validate.IsEmpty(name)
}

func (c Config) BuildSecretsMap() map[string]map[string]string {
	return map[string]map[string]string{
		"appstore": {
			"foxsports":            c.AppleFOXSportsSecretKey,
			"foxnation":            c.AppleFOXNationSecretKey,
			"foxnewsinternational": c.AppleFOXNewsInternationalSecretKey,
			"d2c":                  c.AppleD2cSecretKey,
		},
		"appstoreapi": {
			"privateKey": c.AppleAppStoreAPIKey,
			"keyId":      c.AppStoreAPIKeyId,
			"issuerId":   c.AppStoreAPIIssuerId,
		},
		"appstoresdkapi": {
			"privateKey": c.AppleSDKAppStoreAPIKey,
			"keyId":      c.AppStoreSDKAPIKeyId,
			"issuerId":   c.AppStoreSDKApiIssuerId,
		},
		"rokustore": {
			"foxsports": c.RokuFOXSportsSecretKey,
			"foxnation": c.RokuFOXNationSecretKey,
			"d2c":       c.RokuD2cSecretKey,
		},
		"amazonstore": {
			"foxsports": c.AmazonFOXSportsSecretKey,
			"foxnation": c.AmazonFOXNationSecretKey,
			"d2c":       c.AmazonD2cSecretKey,
		},
		"playstore": {
			"foxsports": c.GoogleFOXSportsSecretKey,
			"foxnation": c.GoogleFOXNationSecretKey,
			"d2c":       c.GoogleD2cSecretKey,
		},
		"applesdk": {
			"foxnation":            c.AppleSDKFOXNationSecretKey,
			"foxnewsinternational": c.AppleSDKFOXNewsInternationalSecretKey,
			"d2c":                  c.AppleSDKD2cSecretKey,
		},
	}
}

func EnsureTimeDuration(cfg config.String, fallback time.Duration) time.Duration {
	d, err := cfg.Duration()
	if err != nil {
		return fallback
	}
	return d
}
