provider "kubernetes" {
  host                   = module.autodiscovery.cluster_endpoint
  cluster_ca_certificate = base64decode(module.autodiscovery.cluster_ca)

  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "aws"
    args        = ["eks", "get-token", "--cluster-name", module.autodiscovery.cluster_name]
  }
}

provider "kubectl" {
  host                   = module.autodiscovery.cluster_endpoint
  cluster_ca_certificate = base64decode(module.autodiscovery.cluster_ca)

  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "aws"
    args        = ["eks", "get-token", "--cluster-name", module.autodiscovery.cluster_name]
  }
}
