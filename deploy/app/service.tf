module "service" {
  source = "github.com/foxcorp-product/entitlement-eng//modules/service?ref=v1"

  business_unit  = var.business_unit
  name           = var.service_name
  image_tag      = var.image_tag
  image_ssm_path = var.repo_name
  environment    = var.environment_name
  region         = var.region
  team           = ""

  # All routes by exception of /receiptverify/apple-status, has 10s. Since the outlier one is 30s, we will set 30s as max timeout.
  # Increasing the max timeout at appmesh level, to prevent the connection to be closed even when a successful response happens
  appmesh_retry_timeout      = "30"
  appmesh_retry_timeout_unit = "s"

  # Defaults to true, make it false if your service does not serves HTTP
  # requests. Workers, for example, do not take advantage of App Mesh, so
  # disable it that way we save resources. AppMesh assumes a default of 1 vCPU
  # for the envoy sidecar, which enables ~2k HTTP RPS per replica.
  #
  # appmesh_enable = false

  # Consider if your service needs to be highly-available. If it does,
  # min_replicas should be at least 2, probably 3. If your service can consume
  # work with breaks, then set min_replicas and max_replicas to 1, since the
  # Kubernetes Scheduler will take care of re-creating your service in the best
  # available node.
  min_replicas = 2
  max_replicas = 3

  # If you're using higher replicas counts as a way to allow your service to
  # scale in times of high-utilization peaks but you want to make it scale-up
  # not as fast - very important if your service is serving HTTP requests and
  # keep a steady load-balance - then use this variable to make sure your
  # service scales up closer to exhaustion of services and not before. Contrary
  # to what you may think, scaling up too fast hurts your service's ability to
  # serve requests properly in high-peak moments, exerting higher-pressure in
  # the routing of requests.
  #
  # hpa_avg_cpu_utilization = 90

  # Add any access you service need or remove that block, for example:
  #
  #  permissions = {
  #    sqs = {
  #      actions   = ["sqs:*"]
  #      resources = ["*"]
  #    },
  #  }

  # Only enable if your service serves HTTP requests. This will hit
  # /service/health unless you change the path to use. If you have a worker
  # without that endpoint, do not enable.
  #
  # liveness_http_probe_enabled  = true
  # readiness_http_probe_enabled = true

  # use for spreading how balanced your app scales. This config says:
  # difference between nodes can't be upwards of 1 a deployment of this
  # application, whithin the valid nodeSelectors. So, if you have 3 nodes, this
  # will try to make each node have one deployment of your application before
  # setting another replica in the same node.
  #
  # topology_spread_constraints = {
  #   "node" = {
  #     "max_skew" = 1
  #     "topology_key" = "kubernetes.io/hostname"
  #     "when_unsatisfiable" = "DoNotSchedule"
  #     "match_labels_name" = var.service_name
  #   }
  # }

  # tweak to your app usage. Load test first to figure out what are the right
  # figures. Use request for an acceptable resource set for your app to respond
  # peak-load. Use limit to allow it not to break on peak-load. Request is used
  # by the HPA controller to understand when it's time to scale-up or down.
  #
  # pod_cpu_limit   = "1000m"
  # pod_cpu_request = "750m"
  # pod_mem_limit   = "1Gi"
  # pod_mem_request = "512Mi"

  # This make only one instance of your app to be deployed per-EC2 node. Only
  # enable if your application requires forced-distribution in compute
  # instances. If your application receives HTTP requests and it's not
  # CDN-cached, you should must definitely enable this.
  #
  # pod_anti_affinity = {
  #   "name" = var.service_name
  # }

  # configmap_data is passed to pod as environment variables, you can add
  # custom env variables here if needed. You probably don't need this, several
  # environment variables are already available like: SERVICE_NAME,
  # SERVICE_PORT, SERVICE_VERSION, SERVICE_ENV, AWS_REGION,
  # ENABLED_CONFIG_PROVIDERS and SERVICE_BU. Ref:
  # https://github.com/foxcorp-product/entitlement-eng/blob/v1/terraform/modules/service/configmap.tf
  #
  # configmap_data = {
  #   "SQS_URL" = "whatever uri"
  #   "DEBUG"   = "false"
  # }
}
