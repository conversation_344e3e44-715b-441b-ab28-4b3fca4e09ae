locals {
  key_by_env = {
    # Consul DEV -> EKS DEV
    dev1 = {
      AppStoreAPIIssuerId    = "69a6de6e-4c81-47e3-e053-5b8c7c11a4d",
      AppStoreSDKApiIssuerId = "69a6de77-d744-47e3-e053-5b8c7c11a4d1",
      Loglevel               = "warn"
    },
    # Consul QA -> EKS STAGE
    stage1 = {
      AppStoreAPIIssuerId    = "69a6de6e-4c81-47e3-e053-5b8c7c11a4d",
      AppStoreSDKApiIssuerId = "69a6de77-d744-47e3-e053-5b8c7c11a4d1",
      Loglevel               = "debug"
    },
    # Consul PROD -> EKS PROD
    prod1 = {
      AppStoreAPIIssuerId    = "69a6de6e-4c81-47e3-e053-5b8c7c11a4d",
      AppStoreSDKApiIssuerId = "69a6de77-d744-47e3-e053-5b8c7c11a4d1",
      Loglevel               = "warn"
    }
  }

  env_keys = lookup(local.key_by_env, var.environment_name)
}

resource "aws_ssm_parameter" "appstore_api_issuer_id" {
  name  = "${local.ssmPath}/AppStoreAPIIssuerId"
  type  = "String"
  value = local.env_keys["AppStoreAPIIssuerId"]
}

resource "aws_ssm_parameter" "appstore_sdk_api_issuer_id" {
  name  = "${local.ssmPath}/AppStoreSDKApiIssuerId"
  type  = "String"
  value = local.env_keys["AppStoreSDKApiIssuerId"]
}

resource "aws_ssm_parameter" "log_level" {
  name  = "${local.ssmPath}/Loglevel"
  type  = "String"
  value = local.env_keys["Loglevel"]
}
