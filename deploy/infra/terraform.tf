terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 4.0"
    }
  }

  backend "s3" {
  }
}

provider "aws" {
  region = var.region

  default_tags {
    tags = {
      Team         = "commerce"
      GithubRepo   = "commerce-receiptverify"
      GithubOrg    = "foxcorp-product"
      Service      = var.service_name
      Environment  = var.environment_name
      BusinessUnit = "comm"
      EndOfLife    = ""
    }
  }
}
