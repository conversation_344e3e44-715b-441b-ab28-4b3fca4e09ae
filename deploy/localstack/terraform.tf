terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 4.0"
    }
  }
}

provider "aws" {
  region                      = "us-east-1"
  access_key                  = "fake"
  secret_key                  = "fake"
  skip_credentials_validation = true
  skip_metadata_api_check     = true
  skip_requesting_account_id  = true
  s3_use_path_style           = true

  default_tags {
    tags = {
      Team         = "commerce"
      GithubRepo   = "commerce-receiptverify"
      GithubOrg    = "foxcorp-product"
      Service      = var.service_name
      Environment  = var.environment_name
      BusinessUnit = "comm"
      EndOfLife    = ""
    }
  }

  endpoints {
    ssm = "http://localhost:4566"
    sts = "http://localhost:4566"
    iam = "http://localhost:4566"
    s3  = "http://localhost:4566"
  }
}
