variable "service_name" {
  type        = string
  default     = "receiptverify"
  description = "the name of the service, it will be added to dns under that name"
}
variable "service_version" {
  type        = string
  default     = "v1"
  description = "the service version, like _v1, it will be added to the service name"
}

variable "repo_name" {
  type        = string
  default     = "foxcorp-product/commerce-receiptverify"
  description = "name of github repo, for example 'foxcorp-product/sps-service-one'"
}

variable "region" {
  type        = string
  default     = "us-east-1"
  description = "AWS region, like us-east-1"
}

variable "environment_name" {
  type        = string
  default     = "local"
  description = "the name of environment, like dev1, qa1 etc."
}

variable "business_unit" {
  type        = string
  default     = "comm"
  description = "fox business unit, like: sports, sps etc"
}
