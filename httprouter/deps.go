package httprouter

import (
	"context"
	"errors"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	planclient "github.com/foxcorp-product/commerce-plan/client"
	"github.com/foxcorp-product/commerce-receiptverify/config"
	"github.com/foxcorp-product/commerce-receiptverify/iap"
	"github.com/foxcorp-product/commerce-receiptverify/iap/receipt"
	"github.com/foxcorp-product/commerce-receiptverify/internal/featureflag"
	"github.com/foxcorp-product/entitlement-sdk/circuitbreaker"
	"github.com/foxcorp-product/entitlement-sdk/feature"
	"github.com/foxcorp-product/entitlement-sdk/stats"
	ld "github.com/launchdarkly/go-server-sdk/v6"
)

const (
	planSvcName = "plan"
	planSvcVer  = "v1"

	defaultTimeout = 5 * time.Second
)

type Stats interface {
	StartMethodSpan(ctx context.Context, resource string, opts ...stats.StartSpanOption) (context.Context, stats.Span)
	StartHTTPSpan(operationName stats.OperationName, resource string, r *http.Request, opts ...stats.StartSpanOption) (stats.Span, *http.Request)
	StartSpan(ctx context.Context, spanType string, operation stats.OperationName, resource string, opts ...stats.StartSpanOption) (context.Context, stats.Span)
	Timing(name string, value time.Duration, rate float64, tags ...string) error
	Gauge(name string, value float64, rate float64, tags ...string) error
	Incr(name string, rate float64, tags ...string) error
	Count(name string, value int64, rate float64, tags ...string) error
	TimeInMilliseconds(name string, value float64, rate float64, tags ...string) error
	WrapHTTPClient(operationName stats.OperationName, resourceProvider func(*http.Request) string, client stats.HTTPClient, opts ...stats.StartSpanOption) (stats.HTTPClient, stats.FinishFunc)
	Flush() error
}

// HandlerDeps holds all handler dependencies, and exposes the MakeHandleDeps, that will be used on main in order to create the routes
type HandlerDeps struct {
	iap                    IAP
	planClient             PlanClient
	amazonClient           CircuitAPI
	rokuClient             CircuitAPI
	samsungClient          CircuitAPI
	stripeClient           CircuitAPI
	googleHttpClient       *http.Client
	stat                   Stats
	secrets                map[string]map[string]string
	appstoreApiKeyId       string
	appstoreApiIssuerId    string
	appstoresdkApiKeyId    string
	appstoresdkApiIssuerId string
	featureClient          featureflag.FeatureClient
}

var (
	serviceEnv = os.Getenv("SERVICE_ENV")
	// useLaunchDarklyClientInLocal by default,
	// a noopFeatureClient will be used when the SERVICE_ENV variable is set to empty or local.
	// When this environment variable USE_LD_CLIENT_LOCALLY is set to true, the noopFeatureClient will not override the LaunchDarkly client for local environment.
	useLaunchDarklyClientInLocal = os.Getenv("USE_LD_CLIENT_LOCALLY") == "true"
)

func MakeHandlerDeps(cfg *config.Config, awsCfg aws.Config, stat *stats.StandardStats) (HandlerDeps, error) {
	// setup LaunchDarkly client
	featOpts := []feature.FeatureOption[ld.Config]{
		feature.WithTimeout(10 * time.Second),
		feature.WithApiKey(cfg.LDApiKey),
		feature.WithProviderOptions(ld.Config{Offline: false}),
	}
	if (serviceEnv == "" || serviceEnv == "local") && !useLaunchDarklyClientInLocal {
		featOpts = append(featOpts, feature.WithProvider(&feature.NoopFeatureClient[ld.Config]{}))
	}

	ldc, err := feature.New(featOpts...)
	if err != nil {
		return HandlerDeps{}, fmt.Errorf("unable to setup launchDarkly client: %w", err)
	}

	// creates a plan client
	planClient, err := planclient.New(
		planclient.WithApiVersion(planSvcVer),
		planclient.WithCircuitOptions(
			circuitbreaker.WithServiceName(planSvcName),
			circuitbreaker.WithStatsdClient(stat),
			circuitbreaker.WithAPMTracer(),
			circuitbreaker.WithTimeout(defaultTimeout),
		),
	)
	if err != nil {
		return HandlerDeps{}, err
	}

	rokuClient, err := circuitbreaker.New(
		circuitbreaker.WithURL(receipt.RokuURL),
		circuitbreaker.WithBaseURLPath(true),
		circuitbreaker.WithHTTPClient(RokuMockServer(ldc).MockedHTTPClient()),
		circuitbreaker.WithStatsdClient(stat),
		circuitbreaker.WithAPMTracer(),
		circuitbreaker.WithTimeout(defaultTimeout),
	)
	if err != nil {
		return HandlerDeps{}, err
	}

	amazonClient, err := circuitbreaker.New(
		circuitbreaker.WithURL(receipt.AmazonUrl),
		circuitbreaker.WithHTTPClient(AmazonMockServer(ldc).MockedHTTPClient()),
		circuitbreaker.WithStatsdClient(stat),
		circuitbreaker.WithAPMTracer(),
		circuitbreaker.WithTimeout(defaultTimeout),
	)
	if err != nil {
		return HandlerDeps{}, err
	}

	samsungProxyClient, err := circuitbreaker.New(
		circuitbreaker.WithServiceName(receipt.SamsungProxyService),
		circuitbreaker.WithURL(receipt.SamsungProxyURL),
		circuitbreaker.WithBaseURLPath(true),
		circuitbreaker.WithStatsdClient(stat),
		circuitbreaker.WithTimeout(defaultTimeout),
		circuitbreaker.WithAPMTracer(),
	)
	if err != nil {
		return HandlerDeps{}, err
	}

	stripeProxyClient, err := circuitbreaker.New(
		circuitbreaker.WithServiceName(receipt.StripeProxyServiceName),
		circuitbreaker.WithStatsdClient(stat),
		circuitbreaker.WithAPMTracer(),
		circuitbreaker.WithTimeout(defaultTimeout),
	)
	if err != nil {
		return HandlerDeps{}, err
	}

	secrets := cfg.BuildSecretsMap()
	// check if the secrets are being read
	// apple
	log.Printf("appStoreFoxSportsSecret found: %t", len(secrets["appstore"]["foxsports"]) > 0)
	log.Printf("appStoreFoxNationSecret found: %t", len(secrets["appstore"]["foxnation"]) > 0)
	log.Printf("appStoreFoxNewsInternationalSecret found: %t", len(secrets["appstore"]["foxnewsinternational"]) > 0)
	log.Printf("appStoreD2cSecret found: %t", len(secrets["appstore"]["d2c"]) > 0)
	log.Printf("appStoreServerApiKey found: %t", len(secrets["appstoreapi"]["privateKey"]) > 0)

	// amazon
	log.Printf("amazonStoreFoxSportsSecret found: %t", len(secrets["amazonstore"]["foxsports"]) > 0)
	log.Printf("amazonStoreFoxNationSecret found: %t", len(secrets["amazonstore"]["foxnation"]) > 0)
	log.Printf("amazonStoreD2cSecret found: %t", len(secrets["amazonstore"]["d2c"]) > 0)

	// google
	log.Printf("playStoreFoxSportsSecret found: %t", len(secrets["playstore"]["foxsports"]) > 0)
	log.Printf("playStoreFoxNationSecret found: %t", len(secrets["playstore"]["foxnation"]) > 0)
	log.Printf("playStoreD2cSecret found: %t", len(secrets["playstore"]["d2c"]) > 0)

	// roku
	log.Printf("rokuStoreFoxSportsSecret found: %t", len(secrets["rokustore"]["foxsports"]) > 0)
	log.Printf("rokuStoreFoxNationSecret found: %t", len(secrets["rokustore"]["foxnation"]) > 0)
	log.Printf("rokuStoreD2cSecret found: %t", len(secrets["rokustore"]["d2c"]) > 0)

	// apple sdk
	log.Printf("appleSdkFoxNationSecret found: %t", len(secrets["applesdk"]["foxnation"]) > 0)
	log.Printf("appleSdkFoxNewsInternationalSecret found: %t", len(secrets["applesdk"]["foxnewsinternational"]) > 0)
	log.Printf("appleSdkD2cSecret found: %t", len(secrets["applesdk"]["d2c"]) > 0)
	log.Printf("appStoreServerSdkApiKey found: %t", len(secrets["appstoresdkapi"]["privateKey"]) > 0)

	// try to get appStore certificate
	appleRootCADer := receipt.GetRootCert()
	if appleRootCADer == nil {
		return HandlerDeps{}, errors.New("could not get root certificate for appstore")
	}
	receipt.AppleRootCADer = appleRootCADer

	ld, err := featureflag.MakeFeatureClient(serviceEnv, cfg)
	if err != nil {
		return HandlerDeps{}, err
	}
	return HandlerDeps{
		iap: iap.New(
			stat,
			cfg.BuildSecretsMap(),
			planClient,
			rokuClient,
			amazonClient,
			samsungProxyClient,
			stripeProxyClient,
			GoogleMockServer(ldc).MockedHTTPClient(),
		),
		stat:                   stat,
		planClient:             planClient,
		amazonClient:           amazonClient,
		rokuClient:             rokuClient,
		samsungClient:          samsungProxyClient,
		stripeClient:           stripeProxyClient,
		googleHttpClient:       GoogleMockServer(ldc).MockedHTTPClient(),
		secrets:                secrets,
		appstoreApiKeyId:       cfg.AppStoreAPIKeyId,
		appstoreApiIssuerId:    cfg.AppStoreAPIIssuerId,
		appstoresdkApiKeyId:    cfg.AppStoreSDKAPIKeyId,
		appstoresdkApiIssuerId: cfg.AppStoreSDKApiIssuerId,
		featureClient:          ld,
	}, nil
}
