package httprouter

import (
	"testing"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/foxcorp-product/commerce-receiptverify/config"
	"github.com/foxcorp-product/entitlement-sdk/stats"
)

func TestMakeHandlerDeps(t *testing.T) {
	s, _ := stats.New("receiptverify", "v1", stats.WithDevMode())
	type args struct {
		cfg    *config.Config
		awsCfg aws.Config
		stat   *stats.StandardStats
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "success: should make handler deps",
			args: args{
				cfg: &config.Config{
					AppleFOXSportsSecretKey:               "apple_fox_sports_secret_key",
					AppleFOXNationSecretKey:               "apple_fox_nation_secret_key",
					AppleFOXNewsInternationalSecretKey:    "apple_fox_news_international_secret_key",
					AppleD2cSecretKey:                     "apple_d2c_secret_key",
					AppleAppStoreAPIKey:                   "apple_app_store_api_key",
					AppStoreAPIKeyId:                      "app_store_api_key_id",
					AppStoreAPIIssuerId:                   "app_store_api_issuer_id",
					AppleSDKAppStoreAPIKey:                "apple_sdk_app_store_api_key",
					AppStoreSDKAPIKeyId:                   "app_store_sdk_api_key_id",
					AppStoreSDKApiIssuerId:                "app_store_sdk_api_issuer_id",
					RokuFOXSportsSecretKey:                "roku_fox_sports_secret_key",
					RokuFOXNationSecretKey:                "roku_fox_nation_secret_key",
					RokuD2cSecretKey:                      "roku_d2c_secret_key",
					AmazonFOXSportsSecretKey:              "amazon_fox_sports_secret_key",
					AmazonFOXNationSecretKey:              "amazon_fox_nation_secret_key",
					AmazonD2cSecretKey:                    "amazon_d2c_secret_key",
					GoogleFOXSportsSecretKey:              "google_fox_sports_secret_key",
					GoogleFOXNationSecretKey:              "google_fox_nation_secret_key",
					GoogleD2cSecretKey:                    "google_d2c_secret_key",
					AppleSDKFOXNationSecretKey:            "apple_sdk_fox_nation_secret_key",
					AppleSDKFOXNewsInternationalSecretKey: "apple_sdk_fox_news_international_secret_key",
					AppleSDKD2cSecretKey:                  "apple_sdk_d2c_secret_key",
				},
				awsCfg: aws.Config{},
				stat:   s,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := MakeHandlerDeps(tt.args.cfg, tt.args.awsCfg, tt.args.stat)
			if (err != nil) != tt.wantErr {
				t.Errorf("MakeHandlerDeps() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
