package httprouter

import (
	"context"
	"log"

	"github.com/foxcorp-product/commerce-receiptverify/iap/receipt"
	"github.com/foxcorp-product/commerce-receiptverify/iap/receipt/mocks"
	"github.com/foxcorp-product/entitlement-sdk/mockserver"
)

type featureFlagClient interface {
	IsEnabled(context.Context, string) bool
}

func RokuMockServer(ldc featureFlagClient) *mockserver.MockServer {
	mockserver, err := mockserver.New(
		mockserver.MakeRealRequests(false),
		mockserver.WithMocks(receipt.RokuURL, mocks.RokuMocksRouter()),
		mockserver.WithFeatureFlag(ldc, "use_roku_mockserver"),
	)
	if err != nil {
		log.Panicf("Failed to create Roku mock server: %v", err)
	}
	return mockserver
}

func GoogleMockServer(ldc featureFlagClient) *mockserver.MockServer {
	mockserver, err := mockserver.New(
		mockserver.MakeRealRequests(false),
		mockserver.WithMocks(receipt.GoogleAPIURL, mocks.GoogleAPIMockRouter()),
		mockserver.WithMocks(receipt.GoogleAuthURL, mocks.GoogleAuthMockRouter()),
		mockserver.WithFeatureFlag(ldc, "use_google_mockserver"),
	)
	if err != nil {
		log.Panicf("Failed to create Google mock server: %v", err)
	}
	return mockserver
}

func AmazonMockServer(ldc featureFlagClient) *mockserver.MockServer {
	mockserver, err := mockserver.New(
		mockserver.MakeRealRequests(false),
		mockserver.WithMocks(receipt.AmazonUrl, mocks.AmazonMockRouter()),
		mockserver.WithFeatureFlag(ldc, "use_amazon_mockserver"),
	)
	if err != nil {
		log.Panicf("Failed to create Amazon mock server: %v", err)
	}
	return mockserver
}
