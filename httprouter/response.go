package httprouter

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/awa/go-iap/appstore"
	"github.com/awa/go-iap/appstore/api"
	planclient "github.com/foxcorp-product/commerce-plan/client"
	"github.com/foxcorp-product/commerce-receiptverify/iap"
	"github.com/foxcorp-product/commerce-receiptverify/iap/receipt"
	"github.com/foxcorp-product/entitlement-sdk/request"
)

const (
	defaultDateFormat = "2006-01-02T15:04:05.999Z07:00"
)

type V1AppleStatusRes struct {
	HasUsedIntro bool `json:"hasUsedIntro"`
	HasUsedTrial bool `json:"hasUsedTrial"`
	HasUsedPromo bool `json:"hasUsedPromo"`
	IsSubscribed bool `json:"isSubscribed"`
	IsChurned    bool `json:"isChurned"`
}

type V1ValidateGoogleTransactionRes struct {
	OrderId                   string     `json:"orderId,omitempty"`
	PurchaseDate              string     `json:"productDate,omitempty"`
	ExpiryDate                string     `json:"expiryDate,omitempty"`
	AutoRenewing              bool       `json:"autoRenewing,omitempty"`
	OneTime                   bool       `json:"oneTime,omitempty"`
	UserInitiatedCancellation bool       `json:"userInitiatedCancellation,omitempty"`
	LinkedPurchaseToken       string     `json:"linkedPurchaseToken,omitempty"`
	ResumeAtDate              *string    `json:"resumeAtDate,omitempty"`
	Charged                   *V1Charged `json:"charged,omitempty"`
}

type V1VerifyResponseRoku struct {
	CancelledTransactionIds []string   `json:"cancelledTransactionIds,omitempty"`
	Valid                   bool       `json:"valid"`
	StartDate               string     `json:"startDate,omitempty"`
	EndDate                 string     `json:"endDate,omitempty"`
	TransactionID           string     `json:"transactionId,omitempty"`
	Store                   iap.Store  `json:"store,omitempty"`
	ErrorMessage            string     `json:"errorMessage,omitempty"`
	ErrorType               string     `json:"errorType,omitempty"`
	Charged                 *V1Charged `json:"charged,omitempty"`
}

type V1VerifyResponseStripe struct {
	Valid           bool      `json:"valid"`
	StartDate       string    `json:"startDate,omitempty"`
	EndDate         string    `json:"endDate,omitempty"`
	PaymentIntentID string    `json:"paymentIntentID,omitempty"`
	Store           iap.Store `json:"store,omitempty"`
}

type V1VerifyResponseSamsung struct {
	Valid                  bool       `json:"valid"`
	InvoiceID              string     `json:"invoiceID,omitempty"`
	StartDate              string     `json:"startDate,omitempty"`
	EndDate                string     `json:"endDate,omitempty"`
	NextPaymentDate        string     `json:"nextPaymentDate,omitempty"`
	FreeTrial              bool       `json:"freeTrial"`
	Store                  iap.Store  `json:"store,omitempty"`
	Charged                *V1Charged `json:"charged,omitempty"`
	FlexibleOfferPrice     string     `json:"flexibleOfferPrice,omitempty"`
	LastPaymentTime        string     `json:"lastPaymentTime,omitempty"`
	IsFlexibleOfferApplied bool       `json:"isFlexibleOfferApplied,omitempty"`
}

type V1VerifyResponse struct {
	Valid                  bool             `json:"valid"`
	StartDate              string           `json:"startDate,omitempty"`
	EndDate                string           `json:"endDate,omitempty"`
	FreeTrial              bool             `json:"freeTrial"`
	NextPaymentDate        string           `json:"nextPaymentDate,omitempty"`
	TransactionID          string           `json:"transactionId,omitempty"`
	Store                  iap.Store        `json:"store,omitempty"`
	Plan                   *planclient.Plan `json:"plan,omitempty"`
	Charged                *V1Charged       `json:"charged,omitempty"`
	LastPaymentTime        string           `json:"lastPaymentTime,omitempty"`
	FlexibleOfferPrice     string           `json:"flexibleOfferPrice,omitempty"`
	IsFlexibleOfferApplied bool             `json:"isFlexibleOfferApplied,omitempty"`
}

type V1Charged struct {
	Amount   float64    `json:"amount"`
	Currency string     `json:"currency"`
	Date     *time.Time `json:"date"`
}

func (v *V1VerifyResponse) customizeDates() error {
	startDate, err := toISO8601Date(v.StartDate)
	if err != nil {
		return err
	}
	v.StartDate = startDate
	endDate, err := toISO8601Date(v.EndDate)
	if err != nil {
		return err
	}
	v.EndDate = endDate
	return nil
}

func toISO8601Date(date string) (string, error) {
	if strings.TrimSpace(date) == "" {
		return "", nil
	}

	s := strings.Split(date, " ")
	if len(s) < 2 {
		return "", fmt.Errorf("unexpected date format: %s", date)
	}

	str := fmt.Sprintf("%s %s", s[0], s[1])

	p, err := time.Parse("2006-01-02 15:04:05", str)
	if err != nil {
		return "", err
	}

	return p.Format(defaultDateFormat), nil
}

func newV1VerifyRes(ctx context.Context, plan planclient.Plan, recRaw interface{}) (V1VerifyResponse, error) {
	l := request.GetFromContext(ctx).GetLoggingEntry()
	apiKey := request.GetFromContext(ctx).GetAPIKey()

	store, err := iap.GetStore(apiKey)
	if err != nil {
		return V1VerifyResponse{}, err
	}

	res := V1VerifyResponse{
		Valid: true,
		Store: store,
		Plan:  &plan,
	}

	switch rec := recRaw.(type) {
	case appstore.InApp:
		res.StartDate = rec.PurchaseDate.PurchaseDate
		res.EndDate = rec.ExpiresDate.ExpiresDate
		res.TransactionID = string(rec.OriginalTransactionID)
		res.FreeTrial = strings.TrimSpace(strings.ToLower(rec.IsTrialPeriod)) == "true"

		if err = res.customizeDates(); err != nil {
			return V1VerifyResponse{}, err
		}
	case receipt.RokuResponse:
		purchaseDate, err := rec.GetPurchaseDate()
		if err != nil {
			return V1VerifyResponse{}, err
		}
		res.StartDate = purchaseDate.Format(defaultDateFormat)

		if len(rec.ExpirationDate) > 0 {
			expDate, err := rec.GetExpirationDate()
			if err != nil {
				return V1VerifyResponse{}, err
			}
			res.EndDate = expDate.Format(defaultDateFormat)
		}

		res.TransactionID = rec.OriginalTransactionID
		res.FreeTrial = rec.IsPurchase() && rec.CouponCode == nil && rec.Total == 0 // no charge means free trial
		var pd *time.Time
		if len(rec.PurchaseDate) > 0 {
			pdt, err := rec.GetPurchaseDate()
			if err != nil {
				return V1VerifyResponse{}, err
			}
			pd = &pdt
		}
		res.Charged = &V1Charged{
			Amount:   rec.Total,
			Currency: rec.Currency,
			Date:     pd,
		}

	case receipt.AmazonResponse:
		freeTrialEnd := time.UnixMilli(rec.FreeTrialEndDate)
		res.TransactionID = rec.ReceiptID
		res.FreeTrial = time.Now().Before(freeTrialEnd)
		if rec.PurchaseDate != 0 {
			res.StartDate = rec.GetPurchaseDate().Format(defaultDateFormat)
		}
		if err = rec.CalculateChargedAmount(plan); err != nil {
			l.Errorf("newV1VerifyRes: AmazonResponse.CalculateChargedAmount: %v", err)
			return V1VerifyResponse{}, err
		}
		if rec.Charged != nil {
			res.Charged = &V1Charged{
				Amount:   rec.Charged.Amount,
				Currency: rec.Charged.Currency,
				Date:     rec.Charged.Date,
			}
		}
		if res.FreeTrial {
			res.EndDate = freeTrialEnd.Format(defaultDateFormat)
		} else if rec.RenewalDate != 0 {
			res.EndDate = rec.GetExpirationDate().Format(defaultDateFormat)
		}
		res.IsFlexibleOfferApplied = rec.HasActivePromotion()
	case receipt.PlayStoreData:
		r := receipt.NewPlayStoreResponse(ctx, rec)
		if r.PurchaseDate != 0 {
			res.StartDate = r.GetPurchaseDate().Format(defaultDateFormat)
		}
		if r.ExpiryDate != 0 {
			res.EndDate = r.GetExpirationDate().Format(defaultDateFormat)
		}
		res.TransactionID = r.OrderId
		res.FreeTrial = r.IsFreeTrial
		res.Charged = &V1Charged{
			Amount:   r.Amount,
			Currency: r.Currency,
			Date:     receipt.ToPtr(r.GetPurchaseDate()),
		}
		res.IsFlexibleOfferApplied = r.IsFlexibleOfferApplied

	case receipt.RetrievePaymentIntentResponse:
		stripeRes, err := NewStripeVerifyRes(rec)
		if err != nil {
			return V1VerifyResponse{}, err
		}
		res.StartDate = stripeRes.StartDate
		res.EndDate = stripeRes.EndDate
		res.TransactionID = stripeRes.PaymentIntentID

		return res, nil

	case receipt.SamsungVerifyResponse:
		samsungRes, err := NewSamsungVerifyRes(rec)
		if err != nil {
			return V1VerifyResponse{}, err
		}
		res.StartDate = samsungRes.StartDate
		res.EndDate = samsungRes.EndDate
		res.TransactionID = samsungRes.InvoiceID
		res.NextPaymentDate = samsungRes.NextPaymentDate
		res.FreeTrial = samsungRes.FreeTrial
		res.Charged = samsungRes.Charged
		res.IsFlexibleOfferApplied = samsungRes.IsFlexibleOfferApplied
		res.FlexibleOfferPrice = samsungRes.FlexibleOfferPrice
		res.LastPaymentTime = samsungRes.LastPaymentTime

	default:
		return V1VerifyResponse{}, fmt.Errorf("unexpected appstore rec: %T", rec)
	}

	return res, nil
}

func NewStripeVerifyRes(recRaw interface{}) (*V1VerifyResponseStripe, error) {
	re := recRaw.(receipt.RetrievePaymentIntentResponse)
	res := &V1VerifyResponseStripe{
		Valid:           true,
		Store:           iap.StripeStore,
		PaymentIntentID: re.PaymentIntent.ID,
	}
	startDate, err := re.GetStartDate()
	if err != nil {
		return nil, err
	}
	res.StartDate = startDate.Format(defaultDateFormat)

	endDate := re.GetEndDate()
	if endDate != nil {
		res.EndDate = endDate.Format(defaultDateFormat)
	}
	return res, nil
}

func NewSamsungVerifyRes(recRaw interface{}) (*V1VerifyResponseSamsung, error) {
	re := recRaw.(receipt.SamsungVerifyResponse)
	res := &V1VerifyResponseSamsung{
		Valid:     true,
		Store:     iap.SamsungStore,
		InvoiceID: re.InvoiceID,
		FreeTrial: re.Invoice.SubscriptionInfo.IsFreeTrialPeriod,
		Charged: &V1Charged{
			Currency: re.Invoice.OrderCurrencyID,
		},
		FlexibleOfferPrice:     re.Invoice.SubscriptionInfo.FlexibleOfferPrice,
		LastPaymentTime:        re.Invoice.SubscriptionInfo.LastPaymentTime,
		IsFlexibleOfferApplied: re.Invoice.SubscriptionInfo.IsFlexibleOfferApplied,
	}

	date, err := parseSamsungDate(re.Invoice.OrderTime)
	if err != nil {
		return nil, err
	}

	res.Charged.Date = date
	if !res.FreeTrial {
		res.Charged.Amount = re.Invoice.Price
	}

	// recreate the charge if there is a flexible offer applied
	if re.Invoice.SubscriptionInfo.IsFlexibleOfferApplied && re.Invoice.SubscriptionInfo.FlexibleOfferPrice != "" {
		var amount float64
		amount, err = strconv.ParseFloat(re.Invoice.SubscriptionInfo.FlexibleOfferPrice, 64)
		if err != nil {
			return nil, err
		}

		res.Charged.Amount = amount
		res.Charged.Date = date
	}

	startDate, err := re.GetStartDate()
	if err != nil {
		return nil, err
	}
	res.StartDate = startDate.Format(defaultDateFormat)

	endDate, err := re.GetEndDate()
	if err != nil {
		return nil, err
	}
	res.EndDate = endDate.Format(defaultDateFormat)

	nextPaymentDate, err := re.GetNextPaymentDate()
	if err != nil {
		return nil, err
	}
	res.NextPaymentDate = nextPaymentDate.Format(defaultDateFormat)

	return res, nil
}

// convert Samsung date format  2018-01-01 00:00:00 to time.Time
func parseSamsungDate(date string) (*time.Time, error) {
	t, err := time.Parse(receipt.Utc14TimeStringLayout, date)
	return &t, err
}

func NewRokuVerifyRes(recRaw interface{}) (V1VerifyResponseRoku, error) {
	re := recRaw.(receipt.RokuResponse)
	res := V1VerifyResponseRoku{
		Valid:                   true,
		Store:                   "rokustore",
		CancelledTransactionIds: re.CancelledTransactionIds,
		ErrorMessage:            re.ErrorMessage,
		TransactionID:           re.OriginalTransactionID,
	}
	purchaseDate, err := re.GetPurchaseDate()
	if err != nil {
		return V1VerifyResponseRoku{}, err
	}
	res.StartDate = purchaseDate.Format(defaultDateFormat)

	if len(re.ExpirationDate) > 0 {
		expDate, err := re.GetExpirationDate()
		if err != nil {
			return V1VerifyResponseRoku{}, err
		}
		res.EndDate = expDate.Format(defaultDateFormat)
	}

	var pd *time.Time
	if len(re.PurchaseDate) > 0 {
		pdt, err := re.GetPurchaseDate()
		if err != nil {
			return V1VerifyResponseRoku{}, err
		}
		pd = &pdt
	}
	res.Charged = &V1Charged{
		Amount:   re.Total,
		Currency: re.Currency,
		Date:     pd,
	}

	return res, nil
}

func newV1ValidateGoogleTransactionRes(res receipt.PlayStoreResponse) V1ValidateGoogleTransactionRes {
	return V1ValidateGoogleTransactionRes{
		res.OrderId,
		res.GetPurchaseDate().Format(defaultDateFormat),
		res.GetExpirationDate().Format(defaultDateFormat),
		res.AutoRenewing,
		res.OneTime,
		res.UserInitiatedCancellation,
		res.LinkedPurchaseToken,
		res.GetResumeAtDate(defaultDateFormat),
		&V1Charged{
			Amount:   res.Amount,
			Currency: res.Currency,
			Date:     receipt.ToPtr(res.GetPurchaseDate()),
		},
	}
}

func NewAppleTransactionStatusRes(transactions []*api.JWSTransaction, appServiceID string) V1AppleStatusRes {
	res := V1AppleStatusRes{}
	previouslySubscribed := false
	serviceId := strings.TrimSpace(appServiceID)

	// retrieve the subscription group for appServiceId from the first entry matching the service
	subGroupId := ""
	foundServiceItem := false
	for _, tx := range transactions {
		if strings.TrimSpace(tx.ProductID) == serviceId {
			subGroupId = tx.SubscriptionGroupIdentifier
			foundServiceItem = true
			break
		}
	}

	if !foundServiceItem {
		return res // no entry found for service, return empty response
	}

	for _, tx := range transactions {
		// if subGroupId has value (i.e. renewable subscription) then filter for it, else it must be a one-time purchase (i.e. ppv) and appServiceId must match
		if subGroupId != "" {
			if subGroupId != tx.SubscriptionGroupIdentifier {
				continue
			}
		} else {
			if strings.TrimSpace(tx.ProductID) != serviceId {
				continue
			}
		}

		previouslySubscribed = true

		if tx.OfferType == 1 {
			res.HasUsedIntro = true
			res.HasUsedTrial = true // for now assume intro is free trial also
		}

		if tx.OfferType == 2 {
			res.HasUsedPromo = true
		}

		if res.IsSubscribed { // if already determined subscription is active no need to check expiration below
			continue
		}

		if tx.RevocationDate > 0 { // if order was cancelled then don't use its expiration date
			continue
		}

		if tx.ExpiresDate == 0 { // if expire time isn't set, it's probably ppv, consider it active
			res.IsSubscribed = true
			continue
		}

		if tx.ExpiresDate > time.Now().UnixMilli() {
			res.IsSubscribed = true
		}
	}

	res.IsChurned = previouslySubscribed && !res.IsSubscribed
	return res
}

func NewAppleStatusRes(data appstore.IAPResponse, appServiceID string) V1AppleStatusRes {
	res := V1AppleStatusRes{}
	previouslySubscribed := false
	serviceId := strings.TrimSpace(appServiceID)

	// retrieve the subscription group for appServiceId from the first entry matching the service
	subGroupId := ""
	foundServiceItem := false
	for _, v := range data.LatestReceiptInfo {
		if strings.TrimSpace(v.ProductID) == serviceId {
			subGroupId = v.SubscriptionGroupIdentifier
			foundServiceItem = true
			break
		}
	}

	if !foundServiceItem {
		return res // no entry found for service, return empty response
	}

	for _, v := range data.LatestReceiptInfo {
		// if subGroupId has value (i.e. renewable subscription) then filter for it, else it must be a one-time purchase (i.e. ppv) and appServiceId must match
		if subGroupId != "" {
			if subGroupId != v.SubscriptionGroupIdentifier {
				continue
			}
		} else {
			if strings.TrimSpace(v.ProductID) != serviceId {
				continue
			}
		}

		previouslySubscribed = true

		if v.IsInIntroOfferPeriod != "" && strings.TrimSpace(strings.ToLower(v.IsInIntroOfferPeriod)) == "true" {
			res.HasUsedIntro = true
		}

		if v.IsTrialPeriod != "" && strings.TrimSpace(strings.ToLower(v.IsTrialPeriod)) == "true" {
			res.HasUsedTrial = true
		}

		if v.PromotionalOfferID != "" {
			res.HasUsedPromo = true
		}

		if res.IsSubscribed { // if already determined subscription is active no need to check expiration below
			continue
		}

		if v.CancellationDate.CancellationDateMS != "" { // if order was cancelled then don't use its expiration date
			continue
		}

		expiresMs := v.ExpiresDate.ExpiresDateMS
		if expiresMs == "" { // if expire time isn't set, it's probably ppv, consider it active
			res.IsSubscribed = true
			continue
		}

		expires, err := strconv.ParseInt(expiresMs, 10, 64)
		if err != nil {
			continue
		}

		if expires > time.Now().UnixMilli() {
			res.IsSubscribed = true
		}
	}

	res.IsChurned = previouslySubscribed && !res.IsSubscribed

	return res
}

func NewValidateAppStoreTransactionRes(transaction *api.JWSTransaction, plan planclient.Plan) V1VerifyResponse {
	purchaseDateUnix := time.UnixMilli(transaction.PurchaseDate)
	// in case of free trial, set the price to 0
	if transaction.OfferType == 1 {
		transaction.Price = 0
	}

	res := V1VerifyResponse{
		Valid: true,
		Store: iap.AppStore,
		Plan:  &plan,
		Charged: &V1Charged{
			Amount:   float64(transaction.Price) / 1000,
			Currency: transaction.Currency,
			Date:     &purchaseDateUnix,
		},
	}

	const layout = "2006-01-02T15:04:05Z"
	res.StartDate = time.UnixMilli(transaction.PurchaseDate).Format(layout)
	res.EndDate = time.UnixMilli(transaction.ExpiresDate).Format(layout)
	res.TransactionID = transaction.OriginalTransactionId
	res.FreeTrial = transaction.OfferType == 1
	return res
}
