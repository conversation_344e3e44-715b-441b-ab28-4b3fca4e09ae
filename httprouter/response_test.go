package httprouter

import (
	"context"
	"github.com/awa/go-iap/amazon"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"github.com/awa/go-iap/appstore"
	"github.com/awa/go-iap/appstore/api"
	planclient "github.com/foxcorp-product/commerce-plan/client"
	"github.com/foxcorp-product/commerce-receiptverify/iap/receipt"
	"github.com/foxcorp-product/entitlement-sdk/apikeys"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/stretchr/testify/assert"
	"github.com/stripe/stripe-go/v74"
	"google.golang.org/api/androidpublisher/v3"
)

func TestNewV1VerifyRes_Apple(t *testing.T) {
	tests := map[string]struct {
		date string
	}{
		"with valid receipt": {
			date: "2021-10-10 00:02:03 Etc/GMT",
		},
		"with invalid receipt": {
			date: "invalid date",
		},
	}
	for name, test := range tests {
		t.Run(name, func(t *testing.T) {
			rec := appstore.InApp{}
			rec.PurchaseDate.PurchaseDate = test.date
			rec.ExpiresDate.ExpiresDate = test.date
			rec.TransactionID = "123"
			rh := request.New()
			rh.SetAPIKey(apikeys.APIKey{
				Platform: "apple_tvos",
			})

			_, err := newV1VerifyRes(request.NewContext(context.Background(), rh), planclient.Plan{}, rec)
			if err != nil {
				assert.Equal(t, strings.Contains(err.Error(), "cannot parse"), true)
			}
		})
	}
}

func TestNewV1VerifyRes_Roku(t *testing.T) {
	tests := map[string]struct {
		date string
	}{
		"with valid receipt": {
			date: "2021-10-10 00:02:03 Etc/GMT",
		},
		"with invalid receipt": {
			date: "0",
		},
	}
	for name, test := range tests {
		t.Run(name, func(t *testing.T) {
			rec := receipt.RokuResponse{}
			rec.TransactionID = "123"
			rec.PurchaseDate = test.date
			rec.ExpirationDate = test.date
			rh := request.New()
			rh.SetAPIKey(apikeys.APIKey{
				Platform: "roku_os",
			})

			_, err := newV1VerifyRes(request.NewContext(context.Background(), rh), planclient.Plan{}, rec)
			if err != nil {
				assert.Equal(t, strings.Contains(err.Error(), "cannot parse"), true)
			}
		})
	}
}

func TestNewV1VerifyRes_Amazon(t *testing.T) {
	tests := map[string]struct {
		date int64
	}{
		"with valid receipt": {
			date: 1257894000000000000,
		},
		"with invalid receipt": {
			date: 0,
		},
	}
	for name, test := range tests {
		t.Run(name, func(t *testing.T) {
			rec := receipt.AmazonResponse{}
			rec.PurchaseDate = test.date
			rec.RenewalDate = test.date
			rec.Term = "1 Month"
			rec.Promotions = []amazon.Promotion{
				{
					PromotionStatus: amazon.InProgress,
					PromotionType:   amazon.IntroductoryPrice,
				},
			}

			plan := planclient.Plan{
				AppServiceID: "test-service",
				Price: []planclient.Price{
					{
						RetailPrice:  9.99,
						CurrencyCode: "USD",
					},
				},
			}

			rh := request.New()
			rh.SetAPIKey(apikeys.APIKey{
				Platform: "firetv_firetv",
			})

			_, err := newV1VerifyRes(request.NewContext(context.Background(), rh), plan, rec)
			if err != nil {
				assert.Equal(t, strings.Contains(err.Error(), "cannot parse"), true, err.Error())
			}
		})
	}
}

func TestNewV1VerifyRes_Google(t *testing.T) {
	tests := map[string]struct {
		date string
	}{
		"with valid receipt": {
			date: "2025-07-25T22:54:44.417Z",
		},
		"with invalid receipt": {
			date: "2006-01-02T15:04:05.000Z",
		},
	}
	for name, test := range tests {
		t.Run(name, func(t *testing.T) {
			rec := receipt.PlayStoreData{}

			dateTime, err := time.Parse(time.RFC3339, test.date)
			require.NoError(t, err)
			rec.Purchase.PurchaseTimeMillis = dateTime.UnixMilli()
			rec.Subscription.StartTime = test.date
			rec.Subscription.LineItems = []*androidpublisher.SubscriptionPurchaseLineItem{
				{
					ExpiryTime: test.date,
				},
			}

			rh := request.New()
			rh.SetAPIKey(apikeys.APIKey{
				Platform: "android_tv",
			})

			_, err = newV1VerifyRes(request.NewContext(context.Background(), rh), planclient.Plan{}, rec)
			if err != nil {
				assert.Equal(t, strings.Contains(err.Error(), "cannot parse"), true)
			}
		})
	}
}

func TestNewV1VerifyRes_Stripe(t *testing.T) {
	tests := map[string]struct {
		date int64
	}{
		"with valid receipt": {
			date: 1257894000000000000,
		},
		"with invalid receipt": {
			date: 223092309230390,
		},
	}
	for name, test := range tests {
		t.Run(name, func(t *testing.T) {
			rec := receipt.RetrievePaymentIntentResponse{}
			invoice := stripe.Invoice{
				Subscription: &stripe.Subscription{
					CurrentPeriodEnd:   test.date,
					CurrentPeriodStart: test.date,
				},
			}
			rec.PaymentIntent.Invoice = &invoice
			rec.PaymentIntent.Created = test.date
			rec.PaymentIntent.ID = "id123"
			rh := request.New()
			rh.SetAPIKey(apikeys.APIKey{
				Platform: "web_desktop",
			})

			_, err := newV1VerifyRes(request.NewContext(context.Background(), rh), planclient.Plan{}, rec)
			if err != nil {
				assert.Equal(t, strings.Contains(err.Error(), "cannot parse"), true)
			}
		})
	}
}

func TestNewV1VerifyRes_Samsung(t *testing.T) {
	tests := map[string]struct {
		date string
	}{
		"with valid receipt": {
			date: "20241002044453",
		},
		"with invalid receipt": {
			date: "",
		},
	}
	for name, test := range tests {
		t.Run(name, func(t *testing.T) {
			rec := receipt.SamsungVerifyResponse{}
			invoice := receipt.InvoiceDetail{
				OrderCurrencyID: "idCurr123",
				OrderTime:       test.date,
				SubscriptionInfo: receipt.SubscriptionInfo{
					IsFreeTrialPeriod: false,
					SubsStartTime:     test.date,
					NextPaymentTime:   test.date,
				},
			}
			rec.InvoiceID = "id123"
			rec.Invoice = &invoice
			rh := request.New()
			rh.SetAPIKey(apikeys.APIKey{
				Platform: "samsung_tizen",
			})

			_, err := newV1VerifyRes(request.NewContext(context.Background(), rh), planclient.Plan{}, rec)
			if err != nil {
				assert.Equal(t, strings.Contains(err.Error(), "cannot parse"), true)
			}
		})
	}
}

func TestToISO8601Date(t *testing.T) {
	tests := map[string]struct {
		date         string
		expectedDate string
	}{
		"with a valid date": {
			date:         "2021-10-10 00:02:03 Etc/GMT",
			expectedDate: "2021-10-10T00:02:03Z",
		},
		"with a valid empty date cuz is ppv": {
			date:         "",
			expectedDate: "",
		},
		"with an invalid date": {
			date: "invalid date",
		},
	}

	for name, test := range tests {
		t.Run(name, func(t *testing.T) {
			_, err := toISO8601Date(test.date)
			if err != nil {
				assert.Equal(t, strings.Contains(err.Error(), "cannot parse"), true)
			}
		})
	}
}

func TestNewAppleStatusRes(t *testing.T) {
	data := appstore.IAPResponse{}
	t.Run("no transactions", func(t *testing.T) {
		appServiceID := "prod1"
		res := NewAppleStatusRes(data, appServiceID)
		assert.False(t, res.HasUsedIntro)
		assert.False(t, res.HasUsedPromo)
		assert.False(t, res.HasUsedTrial)
		assert.False(t, res.IsChurned)
		assert.False(t, res.IsSubscribed)
	})
	t.Run("no group, all transactions for a different product", func(t *testing.T) {
		data.LatestReceiptInfo = []appstore.InApp{
			{ProductID: "prod1", TransactionID: "001"},
			{ProductID: "prod1", TransactionID: "002"},
			{ProductID: "prod1", TransactionID: "003"},
		}
		data.LatestReceiptInfo[0].ExpiresDateMS = "1681101400000"
		data.LatestReceiptInfo[1].ExpiresDateMS = "1681101400001"
		data.LatestReceiptInfo[2].ExpiresDateMS = "1681101400002"
		appServiceID := "prod2"
		res := NewAppleStatusRes(data, appServiceID)
		assert.False(t, res.HasUsedIntro)
		assert.False(t, res.HasUsedPromo)
		assert.False(t, res.HasUsedTrial)
		assert.False(t, res.IsChurned)
		assert.False(t, res.IsSubscribed)
	})
	t.Run("all transactions for group expired, no intro, no promo, no trial", func(t *testing.T) {
		data.LatestReceiptInfo = []appstore.InApp{
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "001"},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "002"},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "003"},
		}
		data.LatestReceiptInfo[0].ExpiresDateMS = strconv.Itoa(int(time.Now().UnixMilli() - 3000))
		data.LatestReceiptInfo[1].ExpiresDateMS = strconv.Itoa(int(time.Now().UnixMilli() - 2000))
		data.LatestReceiptInfo[2].ExpiresDateMS = strconv.Itoa(int(time.Now().UnixMilli() - 1000))
		appServiceID := "prod1"
		res := NewAppleStatusRes(data, appServiceID)
		assert.False(t, res.HasUsedIntro)
		assert.False(t, res.HasUsedPromo)
		assert.False(t, res.HasUsedTrial)
		assert.True(t, res.IsChurned)
		assert.False(t, res.IsSubscribed)
	})
	t.Run("one active transaction, no intro, no promo, no trial", func(t *testing.T) {
		data.LatestReceiptInfo = []appstore.InApp{
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "001"},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "002"},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "003"},
		}
		data.LatestReceiptInfo[0].ExpiresDateMS = strconv.Itoa(int(time.Now().UnixMilli() - 2000))
		data.LatestReceiptInfo[1].ExpiresDateMS = strconv.Itoa(int(time.Now().UnixMilli() - 1000))
		data.LatestReceiptInfo[2].ExpiresDateMS = strconv.Itoa(int(time.Now().UnixMilli() + 1000))
		appServiceID := "prod1"
		res := NewAppleStatusRes(data, appServiceID)
		assert.False(t, res.HasUsedIntro)
		assert.False(t, res.HasUsedPromo)
		assert.False(t, res.HasUsedTrial)
		assert.False(t, res.IsChurned)
		assert.True(t, res.IsSubscribed)
	})
	t.Run("one active transaction, used intro, no promo, no trial", func(t *testing.T) {
		data.LatestReceiptInfo = []appstore.InApp{
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "001", IsInIntroOfferPeriod: "true"},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "002"},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "003"},
		}
		data.LatestReceiptInfo[0].ExpiresDateMS = strconv.Itoa(int(time.Now().UnixMilli() - 2000))
		data.LatestReceiptInfo[1].ExpiresDateMS = strconv.Itoa(int(time.Now().UnixMilli() - 1000))
		data.LatestReceiptInfo[2].ExpiresDateMS = strconv.Itoa(int(time.Now().UnixMilli() + 1000))
		appServiceID := "prod1"
		res := NewAppleStatusRes(data, appServiceID)
		assert.True(t, res.HasUsedIntro)
		assert.False(t, res.HasUsedPromo)
		assert.False(t, res.HasUsedTrial)
		assert.False(t, res.IsChurned)
		assert.True(t, res.IsSubscribed)
	})
	t.Run("one active transaction, no intro, used promo, no trial", func(t *testing.T) {
		data.LatestReceiptInfo = []appstore.InApp{
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "001", PromotionalOfferID: "promo1"},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "002"},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "003"},
		}
		data.LatestReceiptInfo[0].ExpiresDateMS = strconv.Itoa(int(time.Now().UnixMilli() - 2000))
		data.LatestReceiptInfo[1].ExpiresDateMS = strconv.Itoa(int(time.Now().UnixMilli() - 1000))
		data.LatestReceiptInfo[2].ExpiresDateMS = strconv.Itoa(int(time.Now().UnixMilli() + 1000))
		appServiceID := "prod1"
		res := NewAppleStatusRes(data, appServiceID)
		assert.False(t, res.HasUsedIntro)
		assert.True(t, res.HasUsedPromo)
		assert.False(t, res.HasUsedTrial)
		assert.False(t, res.IsChurned)
		assert.True(t, res.IsSubscribed)
	})
	t.Run("one active transaction, used intro, no promo, used trial", func(t *testing.T) {
		data.LatestReceiptInfo = []appstore.InApp{
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "001", IsInIntroOfferPeriod: "true", IsTrialPeriod: "true"},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "002"},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "003"},
		}
		data.LatestReceiptInfo[0].ExpiresDateMS = strconv.Itoa(int(time.Now().UnixMilli() - 2000))
		data.LatestReceiptInfo[1].ExpiresDateMS = strconv.Itoa(int(time.Now().UnixMilli() - 1000))
		data.LatestReceiptInfo[2].ExpiresDateMS = strconv.Itoa(int(time.Now().UnixMilli() + 1000))
		appServiceID := "prod1"
		res := NewAppleStatusRes(data, appServiceID)
		assert.True(t, res.HasUsedIntro)
		assert.False(t, res.HasUsedPromo)
		assert.True(t, res.HasUsedTrial)
		assert.False(t, res.IsChurned)
		assert.True(t, res.IsSubscribed)
	})
	t.Run("different group has active transaction, used intro, used promo, used trial", func(t *testing.T) {
		data.LatestReceiptInfo = []appstore.InApp{
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "001", IsInIntroOfferPeriod: "true", IsTrialPeriod: "true", PromotionalOfferID: "promo1"},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "002"},
			{SubscriptionGroupIdentifier: "group2", ProductID: "prod2", TransactionID: "003"},
		}
		data.LatestReceiptInfo[0].ExpiresDateMS = strconv.Itoa(int(time.Now().UnixMilli() + 1000))
		data.LatestReceiptInfo[1].ExpiresDateMS = strconv.Itoa(int(time.Now().UnixMilli() - 1000))
		data.LatestReceiptInfo[2].ExpiresDateMS = strconv.Itoa(int(time.Now().UnixMilli() - 2000))
		appServiceID := "prod2"
		res := NewAppleStatusRes(data, appServiceID)
		assert.False(t, res.HasUsedIntro)
		assert.False(t, res.HasUsedPromo)
		assert.False(t, res.HasUsedTrial)
		assert.True(t, res.IsChurned)
		assert.False(t, res.IsSubscribed)
	})
	t.Run("latest transaction cancelled (not active), used intro, used promo, used trial", func(t *testing.T) {
		data.LatestReceiptInfo = []appstore.InApp{
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "001", IsInIntroOfferPeriod: "true", IsTrialPeriod: "true", PromotionalOfferID: "promo1"},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "002"},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "003"},
		}
		data.LatestReceiptInfo[0].ExpiresDateMS = strconv.Itoa(int(time.Now().UnixMilli() - 2000))
		data.LatestReceiptInfo[1].ExpiresDateMS = strconv.Itoa(int(time.Now().UnixMilli() - 1000))
		data.LatestReceiptInfo[2].ExpiresDateMS = strconv.Itoa(int(time.Now().UnixMilli() + 1000))
		data.LatestReceiptInfo[2].CancellationDateMS = strconv.Itoa(int(time.Now().UnixMilli() - 1000))
		appServiceID := "prod1"
		res := NewAppleStatusRes(data, appServiceID)
		assert.True(t, res.HasUsedIntro)
		assert.True(t, res.HasUsedPromo)
		assert.True(t, res.HasUsedTrial)
		assert.True(t, res.IsChurned)
		assert.False(t, res.IsSubscribed)
	})
	t.Run("no expiration date (ppv), no group, not intro, not promo, not trial", func(t *testing.T) {
		data.LatestReceiptInfo = []appstore.InApp{
			{ProductID: "prod1", TransactionID: "001"},
			{ProductID: "prod1", TransactionID: "002"},
			{ProductID: "prod1", TransactionID: "003"},
		}
		appServiceID := "prod1"
		res := NewAppleStatusRes(data, appServiceID)
		assert.False(t, res.HasUsedPromo)
		assert.False(t, res.HasUsedTrial)
		assert.False(t, res.IsChurned)
		assert.True(t, res.IsSubscribed)
	})
}

func TestNewAppleTransactionStatusRes(t *testing.T) {
	t.Run("no transactions", func(t *testing.T) {
		data := []*api.JWSTransaction{}
		appServiceID := "prod1"
		res := NewAppleTransactionStatusRes(data, appServiceID)
		assert.False(t, res.HasUsedIntro)
		assert.False(t, res.HasUsedPromo)
		assert.False(t, res.HasUsedTrial)
		assert.False(t, res.IsChurned)
		assert.False(t, res.IsSubscribed)
	})
	t.Run("no group, all transactions for a different product", func(t *testing.T) {
		data := []*api.JWSTransaction{
			{ProductID: "prod1", TransactionID: "001"},
			{ProductID: "prod1", TransactionID: "002"},
			{ProductID: "prod1", TransactionID: "003"},
		}
		data[0].ExpiresDate = 1681101400000
		data[1].ExpiresDate = 1681101400001
		data[2].ExpiresDate = 1681101400002
		appServiceID := "prod2"
		res := NewAppleTransactionStatusRes(data, appServiceID)
		assert.False(t, res.HasUsedIntro)
		assert.False(t, res.HasUsedPromo)
		assert.False(t, res.HasUsedTrial)
		assert.False(t, res.IsChurned)
		assert.False(t, res.IsSubscribed)
	})
	t.Run("all transactions for group expired, no intro, no promo, no trial", func(t *testing.T) {
		data := []*api.JWSTransaction{
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "001"},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "002"},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "003"},
		}
		data[0].ExpiresDate = time.Now().UnixMilli() - 3000
		data[1].ExpiresDate = time.Now().UnixMilli() - 2000
		data[2].ExpiresDate = time.Now().UnixMilli() - 1000
		appServiceID := "prod1"
		res := NewAppleTransactionStatusRes(data, appServiceID)
		assert.False(t, res.HasUsedIntro)
		assert.False(t, res.HasUsedPromo)
		assert.False(t, res.HasUsedTrial)
		assert.True(t, res.IsChurned)
		assert.False(t, res.IsSubscribed)
	})
	t.Run("one active transaction, no intro, no promo, no trial", func(t *testing.T) {
		data := []*api.JWSTransaction{
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "001"},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "002"},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "003"},
		}
		data[0].ExpiresDate = time.Now().UnixMilli() - 3000
		data[1].ExpiresDate = time.Now().UnixMilli() - 2000
		data[2].ExpiresDate = time.Now().UnixMilli() + 1000
		appServiceID := "prod1"
		res := NewAppleTransactionStatusRes(data, appServiceID)
		assert.False(t, res.HasUsedIntro)
		assert.False(t, res.HasUsedPromo)
		assert.False(t, res.HasUsedTrial)
		assert.False(t, res.IsChurned)
		assert.True(t, res.IsSubscribed)
	})
	t.Run("one active transaction, used intro, no promo, used trial", func(t *testing.T) {
		data := []*api.JWSTransaction{
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "001", OfferType: 1},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "002"},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "003"},
		}
		data[0].ExpiresDate = time.Now().UnixMilli() - 3000
		data[1].ExpiresDate = time.Now().UnixMilli() - 2000
		data[2].ExpiresDate = time.Now().UnixMilli() + 1000
		appServiceID := "prod1"
		res := NewAppleTransactionStatusRes(data, appServiceID)
		assert.True(t, res.HasUsedIntro)
		assert.False(t, res.HasUsedPromo)
		assert.True(t, res.HasUsedTrial)
		assert.False(t, res.IsChurned)
		assert.True(t, res.IsSubscribed)
	})
	t.Run("one active transaction, no intro, used promo, no trial", func(t *testing.T) {
		data := []*api.JWSTransaction{
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "001", OfferType: 2},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "002"},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "003"},
		}
		data[0].ExpiresDate = time.Now().UnixMilli() - 3000
		data[1].ExpiresDate = time.Now().UnixMilli() - 2000
		data[2].ExpiresDate = time.Now().UnixMilli() + 1000
		appServiceID := "prod1"
		res := NewAppleTransactionStatusRes(data, appServiceID)
		assert.False(t, res.HasUsedIntro)
		assert.True(t, res.HasUsedPromo)
		assert.False(t, res.HasUsedTrial)
		assert.False(t, res.IsChurned)
		assert.True(t, res.IsSubscribed)
	})
	t.Run("different group has active transaction, used intro, no promo, used trial", func(t *testing.T) {
		data := []*api.JWSTransaction{
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "001", OfferType: 1},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "002"},
			{SubscriptionGroupIdentifier: "group2", ProductID: "prod2", TransactionID: "003"},
		}
		data[0].ExpiresDate = time.Now().UnixMilli() + 3000
		data[1].ExpiresDate = time.Now().UnixMilli() - 2000
		data[2].ExpiresDate = time.Now().UnixMilli() - 1000
		appServiceID := "prod2"
		res := NewAppleTransactionStatusRes(data, appServiceID)
		assert.False(t, res.HasUsedIntro)
		assert.False(t, res.HasUsedPromo)
		assert.False(t, res.HasUsedTrial)
		assert.True(t, res.IsChurned)
		assert.False(t, res.IsSubscribed)
	})
	t.Run("latest transaction cancelled (not active), used intro, no promo, used trial", func(t *testing.T) {
		data := []*api.JWSTransaction{
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "001", OfferType: 1},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "002"},
			{SubscriptionGroupIdentifier: "group1", ProductID: "prod1", TransactionID: "003"},
		}
		data[0].ExpiresDate = time.Now().UnixMilli() - 3000
		data[1].ExpiresDate = time.Now().UnixMilli() - 2000
		data[2].ExpiresDate = time.Now().UnixMilli() + 1000
		data[2].RevocationDate = time.Now().UnixMilli() - 1000
		appServiceID := "prod1"
		res := NewAppleTransactionStatusRes(data, appServiceID)
		assert.True(t, res.HasUsedIntro)
		assert.False(t, res.HasUsedPromo)
		assert.True(t, res.HasUsedTrial)
		assert.True(t, res.IsChurned)
		assert.False(t, res.IsSubscribed)
	})
	t.Run("no expiration date (ppv), not intro, not promo, not trial", func(t *testing.T) {
		data := []*api.JWSTransaction{
			{ProductID: "prod1", TransactionID: "001"},
			{ProductID: "prod1", TransactionID: "002"},
			{ProductID: "prod1", TransactionID: "003"},
		}
		appServiceID := "prod1"
		res := NewAppleTransactionStatusRes(data, appServiceID)
		assert.False(t, res.HasUsedPromo)
		assert.False(t, res.HasUsedTrial)
		assert.False(t, res.IsChurned)
		assert.True(t, res.IsSubscribed)
	})
}
