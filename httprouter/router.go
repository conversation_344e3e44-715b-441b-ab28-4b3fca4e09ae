package httprouter

import (
	"errors"
	"net/http"
	"time"

	"github.com/foxcorp-product/entitlement-sdk/route"
)

var ErrorMissingServiceDependency = errors.New("missing service dependency")

// in a httprouter or handler pkg, expose a func that returns an array of route.Route
func MakeRoutes(deps HandlerDeps) (route.Routes, error) {
	// create your service by config
	if deps.iap == nil {
		return nil, ErrorMissingServiceDependency
	}

	if deps.stat == nil {
		return nil, ErrorMissingServiceDependency
	}

	// create your handler passing the service as a dependency of it
	h := newReceiptVerifyHandler(deps.iap, deps.stat, deps.planClient,
		deps.amazonClient,
		deps.rokuClient,
		deps.samsungClient,
		deps.stripeClient,
		deps.googleHttpClient,
		deps.secrets,
		deps.appstoreApiKeyId,
		deps.appstoreApiIssuerId,
		deps.appstoresdkApiKeyId,
		deps.appstoresdkApiIssuerId,
		deps.featureClient,
	)

	// return a array of routes
	return route.Routes{
		{
			Name:           "V1Verify",
			Method:         http.MethodPost,
			HandlerTimeout: 10 * time.Second,
			Path:           "/receiptverify",
			Prefix:         "v1",
			Handler:        h.V1Verify,
			Middlewares: []route.Middleware{
				V1ReceiptVerifyValidator,
			},
		},
		{
			Name:           "V1ValidateRokuTransaction",
			Method:         http.MethodPost,
			HandlerTimeout: 10 * time.Second,
			Path:           "/receiptverify/validate-roku-transaction",
			Prefix:         "v1",
			Handler:        h.V1ValidateRokuTransaction,
			Middlewares: []route.Middleware{
				V1ValidateRokuTransactionValidator,
			},
		},
		{
			Name:           "V1ValidateGoogleTransaction",
			Method:         http.MethodPost,
			HandlerTimeout: 10 * time.Second,
			Path:           "/receiptverify/validate-google-transaction",
			Prefix:         "v1",
			Handler:        h.V1ValidateGoogleTransaction,
			Middlewares: []route.Middleware{
				V1ValidateGoogleTransactionValidator,
			},
		},
		{
			Name:           "V1ValidateAmazonTransaction",
			Method:         http.MethodPost,
			HandlerTimeout: 10 * time.Second,
			Path:           "/receiptverify/validate-amazon-transaction",
			Prefix:         "v1",
			Handler:        h.V1ValidateAmazonTransaction,
			Middlewares: []route.Middleware{
				V1ValidateAmazonTransactionValidator,
			},
		},
		{
			Name:           "V1ValidateStripeTransaction",
			Method:         http.MethodPost,
			HandlerTimeout: 10 * time.Second,
			Path:           "/receiptverify/validate-stripe-transaction",
			Prefix:         "v1",
			Handler:        h.V1ValidateStripeTransaction,
			Middlewares: []route.Middleware{
				V1ValidateStripeTransactionValidator,
			},
		},
		{
			Name:           "V1ValidateSamsungTransaction",
			Method:         http.MethodPost,
			HandlerTimeout: 10 * time.Second,
			Path:           "/receiptverify/validate-samsung-transaction",
			Prefix:         "v1",
			Handler:        h.V1ValidateSamsungTransaction,
			Middlewares: []route.Middleware{
				V1ValidateSamsungTransactionValidator,
			},
		},
		{
			Name:           "V1AppleSubscriptionStatus",
			Method:         http.MethodPost,
			HandlerTimeout: 30 * time.Second,
			Path:           "/receiptverify/apple-status",
			Prefix:         "v1",
			Handler:        h.V1AppleSubscriptionStatus,
			Middlewares: []route.Middleware{
				V1AppleSubscriptionStatusValidator,
			},
		},
		{
			Name:           "V1ValidateAppleTransaction",
			Method:         http.MethodPost,
			HandlerTimeout: 10 * time.Second,
			Path:           "/receiptverify/validate-appstore-transaction",
			Prefix:         "v1",
			Handler:        h.V1ValidateAppleTransaction,
			Middlewares: []route.Middleware{
				V1ValidateAppleTransactionValidator,
			},
		},
	}, nil
}
