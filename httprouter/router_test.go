package httprouter

import (
	"testing"

	"github.com/foxcorp-product/commerce-receiptverify/mocks"
	"github.com/foxcorp-product/entitlement-sdk/stats"
)

func TestMakeRoutes(t *testing.T) {
	s, _ := stats.New("receiptverify", "v1", stats.WithDevMode())
	type args struct {
		deps HandlerDeps
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "failure: should error without iap",
			args: args{
				deps: HandlerDeps{
					iap:                    nil,
					stat:                   s,
					planClient:             nil,
					amazonClient:           nil,
					rokuClient:             nil,
					samsungClient:          nil,
					stripeClient:           nil,
					secrets:                nil,
					appstoreApiKeyId:       "",
					appstoreApiIssuerId:    "",
					appstoresdkApiKeyId:    "",
					appstoresdkApiIssuerId: "",
				},
			},
			wantErr: true,
		},
		{
			name: "failure: should error without stat",
			args: args{
				deps: HandlerDeps{
					iap:                    mocks.NewIAP(t),
					stat:                   nil,
					planClient:             nil,
					amazonClient:           nil,
					rokuClient:             nil,
					samsungClient:          nil,
					stripeClient:           nil,
					secrets:                nil,
					appstoreApiKeyId:       "",
					appstoreApiIssuerId:    "",
					appstoresdkApiKeyId:    "",
					appstoresdkApiIssuerId: "",
				},
			},
			wantErr: true,
		},
		{
			name: "success: should make routes",
			args: args{
				deps: HandlerDeps{
					iap:                    mocks.NewIAP(t),
					stat:                   s,
					planClient:             nil,
					amazonClient:           nil,
					rokuClient:             nil,
					samsungClient:          nil,
					stripeClient:           nil,
					secrets:                nil,
					appstoreApiKeyId:       "",
					appstoreApiIssuerId:    "",
					appstoresdkApiKeyId:    "",
					appstoresdkApiIssuerId: "",
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := MakeRoutes(tt.args.deps)
			if (err != nil) != tt.wantErr {
				t.Errorf("MakeRoutes() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
