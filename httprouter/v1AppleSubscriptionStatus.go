package httprouter

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/awa/go-iap/appstore/api"
	"github.com/foxcorp-product/commerce-receiptverify/iap"
	"github.com/foxcorp-product/commerce-receiptverify/iap/receipt"
	"github.com/foxcorp-product/commerce-receiptverify/internal/response"
	"github.com/foxcorp-product/entitlement-sdk/inputvalidator"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/gookit/validate"
)

var (
	errBundleIdRequired              = "bundleId is required when transactionId is used"
	errNoAppleApiKeyFound            = errors.New("no apple api key was found")
	errNoAppleSharedSecretFound      = errors.New("no apple shared secret was found")
	receiptAppStoreHistoryFunc       = receipt.NewAppStoreTransactionHistory
	receiptNewAppStoreFunc           = receipt.NewAppStore
	newAppleTransactionStatusResFunc = NewAppleTransactionStatusRes
	newAppleStatusResFunc            = NewAppleStatusRes
)

var V1AppleSubscriptionStatusValidator = inputvalidator.MakeRouteValidatorMiddleware[V1AppleSubscriptionStatusRequestInput](
	inputvalidator.WithCustomErrorResponse(response.SendErrors),
	inputvalidator.WithRule("AppServiceID", "required", errAppServiceIdRequired),
	func(opts *inputvalidator.RouteValidatorOptions) {
		opts.Validation = append(opts.Validation, func(validation *validate.Validation) {
			validation.AddValidator("receipt_and_txID", func(val interface{}, receiptIDField string, TxIDField string) bool {
				receiptIDVal, ok := validation.Get(receiptIDField)
				TxIDVal, okTx := validation.Get(TxIDField)

				if !ok && !okTx {
					return false
				}

				if TxIDVal == "" && receiptIDVal == "" {
					return false
				}
				return true
			}).AddValidator("bundle_id", func(val interface{}, bundleIDField string, TxIDField string) bool {
				bundleIDVal, ok := validation.Get(bundleIDField)
				TxIDVal, okTx := validation.Get(TxIDField)

				if !ok && !okTx {
					return false
				}

				if TxIDVal == "" {
					return true
				}

				if TxIDVal != "" && bundleIDVal == "" {
					return false
				}
				return true
			}).WithMessages(map[string]string{
				"bundle_id":        errBundleIdRequired,
				"receipt_and_txID": errReceiptRequired,
			})
		})
	},
	inputvalidator.WithStringRule("Receipt", "receipt_and_txID:Receipt,TxID"),
	inputvalidator.WithStringRule("BundleID", "bundle_id:BundleID,TxID"),
	inputvalidator.WithoutErrorWrap(),
	inputvalidator.WithoutErrorRuleName(),
)

type V1AppleSubscriptionStatusRequestInput struct {
	V1AppleSubscriptionStatusRequest `in:"body=json" validate:"required"`
}

type V1AppleSubscriptionStatusRequest struct {
	AppServiceID string `json:"appServiceId"`
	Receipt      string `json:"receipt"`
	TxID         string `json:"originalTransactionId"`
	BundleID     string `json:"bundleId"`
}

func (h V1Handler) V1AppleSubscriptionStatus(w http.ResponseWriter, r *http.Request) {
	var (
		err error
	)

	ctx, span := h.stat.StartMethodSpan(r.Context(), "V1Handler.V1AppleSubscriptionStatus")
	defer func() { span.FinishWithError(err) }()

	l := request.GetFromContext(ctx).GetLoggingEntry()

	in, ok := inputvalidator.GetValidatedStruct[V1AppleSubscriptionStatusRequestInput](r.Context())
	if !ok {
		response.SendErrors(w, http.StatusBadRequest, fmt.Errorf("bad request"))
		return
	}
	switch {
	case in.TxID != "":
		start := time.Now().UnixMilli()
		transactions, err := h.getAppleTransactionHistory(ctx, in.BundleID, in.TxID)
		l.Infof("getAppleTransactionHistory finished in %v millis", time.Now().UnixMilli()-start)
		if err != nil {
			response.SendErrors(w, http.StatusUnauthorized, err)
			l.WithField("err", "NewAppStoreTransactionHistory:"+err.Error())
			l.Errorf("error retrieving apple transaction history: %v, txId: %v, bundleId: %v, appServiceId: %v", err.Error(), in.TxID, in.BundleID, in.AppServiceID)
			return
		}
		statusRes := newAppleTransactionStatusResFunc(transactions, in.AppServiceID)
		response.Send(w, http.StatusOK, statusRes)
		return

	case in.Receipt != "":
		data, err := h.getAppleReceiptData(ctx, in.AppServiceID, in.Receipt)
		if err != nil {
			response.SendErrors(w, http.StatusUnauthorized, err)
			l.WithField("err", "NewAppStore:"+err.Error())
			l.Errorf("error retrieving apple receipt: %v, appServiceId: %v", err.Error(), in.AppServiceID)
			return
		}
		statusRes := newAppleStatusResFunc(data.Data, in.AppServiceID)
		response.Send(w, http.StatusOK, statusRes)
		return

	default:
		// shouldn't reach here
		l.Errorf("bad input, no transaction or receipt")
		response.SendErrors(w, http.StatusBadRequest, errors.New("bad request, unknown input type"))
		return
	}
}

func (h V1Handler) getAppleTransactionHistory(ctx context.Context, reqBundleId, reqTxId string) ([]*api.JWSTransaction, error) {
	l := request.GetFromContext(ctx).GetLoggingEntry()

	// first try standard api key, if error try dev api key
	apiKey := h.secrets[string(iap.AppStoreApi)][iap.AppStoreApiKey]
	if len(apiKey) == 0 {
		l.WithField("err", "secrets:"+errNoAppleApiKeyFound.Error())
		l.Errorf("error retrieving apple api key for signing requests")
		return nil, errNoAppleApiKeyFound
	}

	resp, err := receiptAppStoreHistoryFunc(ctx, reqBundleId, reqTxId, h.appstoreApiKeyId, h.appstoreApiIssuerId, apiKey)
	if err == nil {
		return resp, nil // success with standard api key
	}

	// standard apik key didn't work, now try with dev api key
	apiKey = h.secrets[string(iap.AppStoreDevApi)][iap.AppStoreApiKey]
	if len(apiKey) == 0 {
		l.WithField("err", "dev secrets:"+errNoAppleApiKeyFound.Error())
		l.Errorf("error retrieving apple sdk api key for signing requests")
		return nil, err
	}

	resp, devErr := receiptAppStoreHistoryFunc(ctx, reqBundleId, reqTxId, h.appstoresdkApiKeyId, h.appstoresdkApiIssuerId, apiKey)
	if devErr == nil {
		return resp, nil // success with dev api key
	}

	return nil, err // dev key also failed, return original error
}

func (h V1Handler) getAppleReceiptData(ctx context.Context, reqAppServiceID, reqReceipt string) (*receipt.AppStoreSVC, error) {
	l := request.GetFromContext(ctx).GetLoggingEntry()
	apiKey := request.GetFromContext(ctx).GetAPIKey()

	secret := h.secrets[string(iap.AppStore)][apiKey.Brand]
	if len(secret) == 0 {
		l.WithField("err", "secrets:"+errNoAppleSharedSecretFound.Error()+" for "+apiKey.Brand)
		l.Errorf("error retrieving apple shared secret for app: %v, key: %v", apiKey.Brand, apiKey.Brand)
		return nil, errNoAppleSharedSecretFound
	}

	conf := receipt.AppStoreConfig{
		ReceiptData:  reqReceipt,
		AppServiceID: reqAppServiceID,
	}

	rs, err := receiptNewAppStoreFunc(ctx, h.stat, secret, conf)
	if err == nil { // no verification error, success return for standard secret
		return rs, nil
	}

	// reached here if verification error, try once more with the dev secret
	devSecret := h.secrets[string(iap.AppleDev)][apiKey.Brand]
	if len(devSecret) == 0 {
		l.WithField("err", "dev secrets:"+errNoAppleSharedSecretFound.Error()+" for dev "+apiKey.Brand)
		l.Errorf("error retrieving apple sdk shared secret for app: %v, key: %v", apiKey.Brand, apiKey.APIKey)
		return nil, err // return original error
	}
	rs, devErr := receiptNewAppStoreFunc(ctx, h.stat, devSecret, conf)
	if devErr != nil {
		l.Errorf("error retrieving apple receipt: %v", err.Error())
		return nil, err // return original error
	}

	return rs, nil // success return for dev secret
}
