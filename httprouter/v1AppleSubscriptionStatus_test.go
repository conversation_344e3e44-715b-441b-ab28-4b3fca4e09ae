package httprouter

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/awa/go-iap/appstore/api"
	"github.com/foxcorp-product/commerce-receiptverify/config"
	"github.com/foxcorp-product/commerce-receiptverify/iap/receipt"
	"github.com/foxcorp-product/commerce-receiptverify/internal/featureflag"
	"github.com/foxcorp-product/commerce-receiptverify/mocks"
	"github.com/foxcorp-product/entitlement-sdk/apikeys"
	"github.com/foxcorp-product/entitlement-sdk/logger"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/foxcorp-product/entitlement-sdk/route"
	"github.com/foxcorp-product/entitlement-sdk/stats"
	"github.com/stretchr/testify/assert"
)

func mockReceiptHistoryFunc(ctx context.Context, bundleId string, transactionId string, appStoreApiKeyId string, appStoreIssId string, appStoreApiKey string) ([]*api.JWSTransaction, error) {
	if appStoreApiKey == "test-should-error" {
		return nil, errors.New("this should error")
	}
	return []*api.JWSTransaction{
		{},
	}, nil
}

func mockReceiptNewAppStoreFunc(ctx context.Context, stat receipt.Stats, secret string, conf receipt.AppStoreConfig) (*receipt.AppStoreSVC, error) {
	if secret == "test-should-error" {
		return nil, errors.New("this should error")
	}
	return &receipt.AppStoreSVC{}, nil
}

func TestV1AppleSubscriptionStatus(t *testing.T) {
	// Generate the mocks for dependencies
	svcMock := mocks.NewIAP(t)
	planClientMock := mocks.NewPlanClient(t)
	amazonClientMock := mocks.NewCircuitAPI(t)
	rokuClientMock := mocks.NewCircuitAPI(t)
	samsungClientMock := mocks.NewCircuitAPI(t)
	stripeClientMock := mocks.NewCircuitAPI(t)

	s, _ := stats.New("receiptverify", "v1", stats.WithDevMode())
	l, _ := logger.New()
	tests := map[string]struct {
		body               V1AppleSubscriptionStatusRequestInput
		deps               V1Handler
		runMocks           func()
		receiptAppHistory  func(ctx context.Context, bundleId string, transactionId string, appStoreApiKeyId string, appStoreIssId string, appStoreApiKey string) ([]*api.JWSTransaction, error)
		receiptNewAppStore func(ctx context.Context, stat receipt.Stats, secret string, conf receipt.AppStoreConfig) (*receipt.AppStoreSVC, error)
		expectedStatusCode int
	}{
		"failure: should return unauthorized when invalid apikey is provided": {
			body: V1AppleSubscriptionStatusRequestInput{
				V1AppleSubscriptionStatusRequest: V1AppleSubscriptionStatusRequest{
					AppServiceID: "appServiceID-test",
					TxID:         "txID-test",
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			receiptAppHistory:  mockReceiptHistoryFunc,
			expectedStatusCode: http.StatusUnauthorized,
		},
		"failure: should return unauthorized when ReceiptHistoryFunc returns an error with prod apikey": {
			body: V1AppleSubscriptionStatusRequestInput{
				V1AppleSubscriptionStatusRequest: V1AppleSubscriptionStatusRequest{
					AppServiceID: "appServiceID-test",
					TxID:         "txID-test",
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{"appstoreapi": {"privateKey": "test-should-error"}},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			receiptAppHistory:  mockReceiptHistoryFunc,
			expectedStatusCode: http.StatusUnauthorized,
		},
		"failure: should return unauthorized when ReceiptHistoryFunc returns an error with dev apikey": {
			body: V1AppleSubscriptionStatusRequestInput{
				V1AppleSubscriptionStatusRequest: V1AppleSubscriptionStatusRequest{
					AppServiceID: "appServiceID-test",
					TxID:         "txID-test",
				},
			},
			deps: V1Handler{
				svc:           svcMock,
				planClient:    planClientMock,
				amazonClient:  amazonClientMock,
				rokuClient:    rokuClientMock,
				samsungClient: samsungClientMock,
				stripeClient:  stripeClientMock,
				stat:          s,
				secrets: map[string]map[string]string{
					"appstoreapi":    {"privateKey": "test-should-error"},
					"appstoresdkapi": {"privateKey": "test-should-error"},
				},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			receiptAppHistory:  mockReceiptHistoryFunc,
			expectedStatusCode: http.StatusUnauthorized,
		},
		"success: should return ok when valid history from txID with valid dev apikey": {
			body: V1AppleSubscriptionStatusRequestInput{
				V1AppleSubscriptionStatusRequest: V1AppleSubscriptionStatusRequest{
					AppServiceID: "appServiceID-test",
					TxID:         "txID-test",
				},
			},
			deps: V1Handler{
				svc:           svcMock,
				planClient:    planClientMock,
				amazonClient:  amazonClientMock,
				rokuClient:    rokuClientMock,
				samsungClient: samsungClientMock,
				stripeClient:  stripeClientMock,
				stat:          s,
				secrets: map[string]map[string]string{
					"appstoreapi":    {"privateKey": "test-should-error"},
					"appstoresdkapi": {"privateKey": "test-should-success"},
				},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			receiptAppHistory:  mockReceiptHistoryFunc,
			expectedStatusCode: http.StatusOK,
		},
		"success: should return ok when valid Status from history": {
			body: V1AppleSubscriptionStatusRequestInput{
				V1AppleSubscriptionStatusRequest: V1AppleSubscriptionStatusRequest{
					AppServiceID: "appServiceID-test",
					TxID:         "test-should-success",
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{"appstoreapi": {"privateKey": "test-should-success"}},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			receiptAppHistory:  mockReceiptHistoryFunc,
			expectedStatusCode: http.StatusOK,
		},
		"failure: should return unauthorized when invalid receipt from Receipt with invalid apikey": {
			body: V1AppleSubscriptionStatusRequestInput{
				V1AppleSubscriptionStatusRequest: V1AppleSubscriptionStatusRequest{
					AppServiceID: "appServiceID-test",
					Receipt:      "receipt-test",
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			expectedStatusCode: http.StatusUnauthorized,
		},
		"success: should return ok when valid receipt from Receipt with valid prod apikey": {
			body: V1AppleSubscriptionStatusRequestInput{
				V1AppleSubscriptionStatusRequest: V1AppleSubscriptionStatusRequest{
					AppServiceID: "appServiceID-test",
					Receipt:      "receipt-test",
				},
			},
			deps: V1Handler{
				svc:           svcMock,
				planClient:    planClientMock,
				amazonClient:  amazonClientMock,
				rokuClient:    rokuClientMock,
				samsungClient: samsungClientMock,
				stripeClient:  stripeClientMock,
				stat:          s,
				secrets: map[string]map[string]string{
					"appstore": {"test-brand": "test-should-success"},
				},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			receiptNewAppStore: mockReceiptNewAppStoreFunc,
			expectedStatusCode: http.StatusOK,
		},
		"failure: should return unauthorized when invalid receipt from Receipt with invalid dev apikey": {
			body: V1AppleSubscriptionStatusRequestInput{
				V1AppleSubscriptionStatusRequest: V1AppleSubscriptionStatusRequest{
					AppServiceID: "appServiceID-test",
					Receipt:      "receipt-test",
				},
			},
			deps: V1Handler{
				svc:           svcMock,
				planClient:    planClientMock,
				amazonClient:  amazonClientMock,
				rokuClient:    rokuClientMock,
				samsungClient: samsungClientMock,
				stripeClient:  stripeClientMock,
				stat:          s,
				secrets: map[string]map[string]string{
					"appstore": {"test-brand": "test-should-error"},
					"applesdk": {"blank-dev": "test-should-success"},
				},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			receiptNewAppStore: mockReceiptNewAppStoreFunc,
			expectedStatusCode: http.StatusUnauthorized,
		},
		"failure: should return unauthorized when invalid receipt from Receipt with valid dev apikey": {
			body: V1AppleSubscriptionStatusRequestInput{
				V1AppleSubscriptionStatusRequest: V1AppleSubscriptionStatusRequest{
					AppServiceID: "appServiceID-test",
					Receipt:      "receipt-test",
				},
			},
			deps: V1Handler{
				svc:           svcMock,
				planClient:    planClientMock,
				amazonClient:  amazonClientMock,
				rokuClient:    rokuClientMock,
				samsungClient: samsungClientMock,
				stripeClient:  stripeClientMock,
				stat:          s,
				secrets: map[string]map[string]string{
					"appstore": {"test-brand": "test-should-error"},
					"applesdk": {"test-brand": "test-should-error"},
				},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			receiptNewAppStore: mockReceiptNewAppStoreFunc,
			expectedStatusCode: http.StatusUnauthorized,
		},
		"success: should return ok when valid receipt from Receipt with valid dev apikey": {
			body: V1AppleSubscriptionStatusRequestInput{
				V1AppleSubscriptionStatusRequest: V1AppleSubscriptionStatusRequest{
					AppServiceID: "appServiceID-test",
					Receipt:      "receipt-test",
				},
			},
			deps: V1Handler{
				svc:           svcMock,
				planClient:    planClientMock,
				amazonClient:  amazonClientMock,
				rokuClient:    rokuClientMock,
				samsungClient: samsungClientMock,
				stripeClient:  stripeClientMock,
				stat:          s,
				secrets: map[string]map[string]string{
					"appstore": {"test-brand": "test-should-error"},
					"applesdk": {"test-brand": "test-should-success"},
				},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			receiptNewAppStore: mockReceiptNewAppStoreFunc,
			expectedStatusCode: http.StatusOK,
		},
	}

	for name, test := range tests {
		t.Run(name, func(t *testing.T) {
			var b bytes.Buffer
			if err := json.NewEncoder(&b).Encode(test.body); err != nil {
				t.Fatal(err)
			}
			body := io.NopCloser(&b)

			// Define globals
			receiptAppStoreHistoryFunc = test.receiptAppHistory
			receiptNewAppStoreFunc = test.receiptNewAppStore

			w := httptest.NewRecorder()
			rh, r := request.NewWithRequest(httptest.NewRequest(http.MethodPost, "/receiptverify/apple-status", body), "receiptverify")
			rh.SetAPIKey(apikeys.APIKey{Brand: "test-brand"})
			rh.SetLoggingEntry(l)

			h := newReceiptVerifyHandler(test.deps.svc, s, test.deps.planClient, test.deps.amazonClient, test.deps.rokuClient, test.deps.samsungClient, test.deps.stripeClient, test.deps.googleHttpClient, test.deps.secrets, test.deps.appstoreApiKeyId, test.deps.appstoreApiIssuerId, test.deps.appstoresdkApiKeyId, test.deps.appstoresdkApiIssuerId, test.deps.featureClient)
			if test.runMocks != nil {
				test.runMocks()
			}
			handler := V1AppleSubscriptionStatusValidator(route.Route{})(http.HandlerFunc(h.V1AppleSubscriptionStatus))
			handler.ServeHTTP(w, r)
			res := w.Result()
			defer res.Body.Close()
			if res.StatusCode != test.expectedStatusCode {
				t.Errorf("expected %d, but got %d", test.expectedStatusCode, res.StatusCode)
			}
		})
	}
}

func TestV1AppleSubscriptionStatusWithoutValidator(t *testing.T) {
	s, _ := stats.New("receiptverify", "v1", stats.WithDevMode())
	req, err := http.NewRequest("POST", "/receiptverify/apple-status", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	ff, _ := featureflag.MakeFeatureClient("", &config.Config{})
	h := newReceiptVerifyHandler(mocks.NewIAP(t), s, mocks.NewPlanClient(t), mocks.NewCircuitAPI(t), mocks.NewCircuitAPI(t), mocks.NewCircuitAPI(t), mocks.NewCircuitAPI(t), &http.Client{}, map[string]map[string]string{}, "", "", "", "", ff)

	handler := http.HandlerFunc(h.V1AppleSubscriptionStatus)
	handler.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusBadRequest, rr.Code, "Should have returned status BadRequest")
}
