package httprouter

import (
	"errors"
	"fmt"
	"net/http"

	planclient "github.com/foxcorp-product/commerce-plan/client"
	"github.com/foxcorp-product/commerce-receiptverify/iap/receipt"
	"github.com/foxcorp-product/commerce-receiptverify/internal/response"
	"github.com/foxcorp-product/entitlement-sdk/inputvalidator"
	"github.com/foxcorp-product/entitlement-sdk/request"
)

var (
	errReceiptRequired        = "receipt or transactionId is required"
	errAmazonUserIdRequired   = "amazon user id required"
	errNoAmazonApiKeyFound    = errors.New("no amazon api key was found")
	receiptNewAmazonStoreFunc = receipt.NewAmazon
)

var V1ValidateAmazonTransactionValidator = inputvalidator.MakeRouteValidatorMiddleware[V1ValidateAmazonTransactionRequestInput](
	inputvalidator.WithCustomErrorResponse(response.SendErrors),
	inputvalidator.WithRule("AppServiceId", "required", errAppServiceIdRequired),
	inputvalidator.WithRule("Receipt", "required", errReceiptRequired),
	inputvalidator.WithRule("AmazonUserId", "required", errAmazonUserIdRequired),
	inputvalidator.WithoutErrorWrap(),
	inputvalidator.WithoutErrorRuleName(),
)

type V1ValidateAmazonTransactionRequestInput struct {
	V1ValidateAmazonTransactionRequest `in:"body=json" validate:"required"`
}

type V1ValidateAmazonTransactionRequest struct {
	AppServiceId string `json:"appServiceId"`
	Receipt      string `json:"receipt"`
	AmazonUserId string `json:"amazonUserId"`
	AllowExpired bool   `json:"allowExpired"`
}

func (h V1Handler) V1ValidateAmazonTransaction(w http.ResponseWriter, r *http.Request) {
	var err error

	l := request.GetFromContext(r.Context()).GetLoggingEntry()

	ctx, span := h.stat.StartMethodSpan(r.Context(), "V1Handler.V1ValidateAmazonTransaction")
	defer func() { span.FinishWithError(err) }()

	in, ok := inputvalidator.GetValidatedStruct[V1ValidateAmazonTransactionRequestInput](r.Context())
	if !ok {
		response.SendErrors(w, http.StatusBadRequest, fmt.Errorf("bad request"))
		return
	}

	p, err := h.planClient.GetPlanByAppServiceID(ctx, planclient.V1GetPlanByAppServiceIDInput{
		AppServiceID: in.AppServiceId,
	})
	if err != nil {
		l.Errorf("V1ValidateAmazonTransaction: planClient.GetPlanByAppServiceID: %v", err)
		response.SendErrors(w, http.StatusUnauthorized, err)
		request.GetFromContext(ctx).SetLoggingEntry(l.WithField("err", "amazon verifyPlanFunc:"+err.Error()))
		return
	}

	apikey := h.secrets["amazonstore"][p.AppID]
	if len(apikey) == 0 {
		l.Errorf("V1ValidateAmazonTransaction: secrets: %v", errNoAmazonApiKeyFound)
		response.SendErrors(w, http.StatusUnauthorized, errNoAmazonApiKeyFound)
		request.GetFromContext(ctx).SetLoggingEntry(l.WithField("err", "secrets:"+errNoAmazonApiKeyFound.Error()+" for "+p.AppID))
		return
	}

	conf := receipt.AmazonConfig{
		ReceiptID:    in.Receipt,
		UserID:       in.AmazonUserId,
		AppServiceID: in.AppServiceId,
	}
	rs, err := receiptNewAmazonStoreFunc(ctx, h.stat, apikey, conf, h.amazonClient)
	if err != nil {
		l.Errorf("V1ValidateAmazonTransaction: receipt.NewAmazonStore: %v", err)
		response.SendErrors(w, http.StatusUnauthorized, err)
		request.GetFromContext(ctx).SetLoggingEntry(l.WithField("err", "receipt.NewAmazonStore:"+err.Error()))
		return
	}

	if h.featureClient.GetUseMockServer(ctx) {
		rs.Data.ProductID = in.AppServiceId
	}

	if conf.AppServiceID != rs.Data.ProductID {
		err = fmt.Errorf("receipt.NewAmazon: mismatch appServiceID=%s,productID=%s", conf.AppServiceID, rs.Data.ProductID)
		l.Errorf("V1ValidateAmazonTransaction: receipt.NewAmazonStore: %v", err)
		response.SendErrors(w, http.StatusUnauthorized, err)
		request.GetFromContext(ctx).SetLoggingEntry(l.WithField("err", "receipt.NewAmazonStore:"+err.Error()))
		return
	}

	if !in.AllowExpired && !h.featureClient.GetUseMockServer(ctx) {
		// check if receipt have unexpired
		_, err = rs.UnExpired()
		if err != nil {
			l.Errorf("V1ValidateAmazonTransaction: receipt.UnExpired: %v", err)
			response.Send(w, http.StatusUnauthorized, err)
			request.GetFromContext(ctx).SetLoggingEntry(l.WithField("err", "V1ValidateAmazonTransaction receipt.UnExpired:"+err.Error()))
			return
		}
	}

	rec := rs.GetData().(receipt.AmazonResponse)
	if err = rec.CalculateChargedAmount(p); err != nil {
		l.Errorf("V1ValidateAmazonTransaction: failed to calculate the charged amount: %w", err)
		response.SendErrors(w, http.StatusInternalServerError, err)
		request.GetFromContext(ctx).SetLoggingEntry(l.WithField("err", "receipt.NewAmazonStore:"+err.Error()))
	}
	response.Send(w, http.StatusOK, rec)
}
