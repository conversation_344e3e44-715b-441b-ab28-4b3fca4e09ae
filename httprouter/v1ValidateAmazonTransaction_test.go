package httprouter

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	planclient "github.com/foxcorp-product/commerce-plan/client"
	"github.com/foxcorp-product/commerce-receiptverify/config"
	"github.com/foxcorp-product/commerce-receiptverify/iap/receipt"
	"github.com/foxcorp-product/commerce-receiptverify/internal/featureflag"
	"github.com/foxcorp-product/commerce-receiptverify/mocks"
	"github.com/foxcorp-product/entitlement-sdk/apikeys"
	"github.com/foxcorp-product/entitlement-sdk/logger"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/foxcorp-product/entitlement-sdk/route"
	"github.com/foxcorp-product/entitlement-sdk/stats"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

var mockedReceipt *receipt.AmazonStore

func mockReceiptNewAmazonStoreFunc(ctx context.Context, stat receipt.Stats, secret string, conf receipt.AmazonConfig, cb receipt.CircuitAPI) (*receipt.AmazonStore, error) {
	if secret == "test-should-error" {
		return nil, errors.New("mockReceiptNewAmazonStoreFunc error")
	}
	return mockedReceipt, nil
}

func TestV1AmazonTransaction(t *testing.T) {
	// Generate the mocks for dependencies
	svcMock := mocks.NewIAP(t)
	planClientMock := mocks.NewPlanClient(t)
	amazonClientMock := mocks.NewCircuitAPI(t)
	rokuClientMock := mocks.NewCircuitAPI(t)
	samsungClientMock := mocks.NewCircuitAPI(t)
	stripeClientMock := mocks.NewCircuitAPI(t)
	ff, _ := featureflag.MakeFeatureClient("", &config.Config{})

	s, _ := stats.New("receiptverify", "v1", stats.WithDevMode())
	l, _ := logger.New()
	tests := map[string]struct {
		body                  V1ValidateAmazonTransactionRequestInput
		deps                  V1Handler
		runMocks              func()
		receiptNewAmazonStore func(ctx context.Context, stat receipt.Stats, secret string, conf receipt.AmazonConfig, cb receipt.CircuitAPI) (*receipt.AmazonStore, error)
		expectedStatusCode    int
	}{
		"failure: should return unauthorized when GetPlanByAppServiceID returns an error": {
			body: V1ValidateAmazonTransactionRequestInput{
				V1ValidateAmazonTransactionRequest: V1ValidateAmazonTransactionRequest{
					AppServiceId: "appServiceId",
					Receipt:      "receipt",
					AmazonUserId: "amazonUserId",
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{}, errors.New("plan error")).Once()
			},
			expectedStatusCode: http.StatusUnauthorized,
		},
		"failure: should return unauthorized when invalid api key is provided": {
			body: V1ValidateAmazonTransactionRequestInput{
				V1ValidateAmazonTransactionRequest: V1ValidateAmazonTransactionRequest{
					AppServiceId: "appServiceId",
					Receipt:      "receipt",
					AmazonUserId: "amazonUserId",
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{}, nil).Once()
			},
			expectedStatusCode: http.StatusUnauthorized,
		},
		"failure: should return unauthorized when NewAmazonStoreFunc returns an error": {
			body: V1ValidateAmazonTransactionRequestInput{
				V1ValidateAmazonTransactionRequest: V1ValidateAmazonTransactionRequest{
					AppServiceId: "appServiceId",
					Receipt:      "receipt",
					AmazonUserId: "amazonUserId",
				},
			},
			deps: V1Handler{
				svc:           svcMock,
				planClient:    planClientMock,
				amazonClient:  amazonClientMock,
				rokuClient:    rokuClientMock,
				samsungClient: samsungClientMock,
				stripeClient:  stripeClientMock,
				stat:          s,
				secrets: map[string]map[string]string{
					"amazonstore": {
						"plan-appid": "test-should-error",
					},
				},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{AppID: "plan-appid"}, nil).Once()
			},
			receiptNewAmazonStore: mockReceiptNewAmazonStoreFunc,
			expectedStatusCode:    http.StatusUnauthorized,
		},
		"failure: should return unauthorized when UnExpired returns an error": {
			body: V1ValidateAmazonTransactionRequestInput{
				V1ValidateAmazonTransactionRequest: V1ValidateAmazonTransactionRequest{
					AppServiceId: "appServiceId",
					Receipt:      "receipt",
					AmazonUserId: "amazonUserId",
				},
			},
			deps: V1Handler{
				svc:           svcMock,
				planClient:    planClientMock,
				amazonClient:  amazonClientMock,
				rokuClient:    rokuClientMock,
				samsungClient: samsungClientMock,
				stripeClient:  stripeClientMock,
				stat:          s,
				secrets: map[string]map[string]string{
					"amazonstore": {
						"plan-appid": "test-should-pass",
					},
				},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
				featureClient:          ff,
			},
			runMocks: func() {
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{AppID: "plan-appid"}, nil).Once()
				mockedReceipt = &receipt.AmazonStore{
					Data: receipt.AmazonResponse{
						CancelDate: 1,
						ProductID:  "appServiceId",
					},
				}
			},
			receiptNewAmazonStore: mockReceiptNewAmazonStoreFunc,
			expectedStatusCode:    http.StatusUnauthorized,
		},
		"success: should return ok when Amazon Transaction is expired but allowExpired is true": {
			body: V1ValidateAmazonTransactionRequestInput{
				V1ValidateAmazonTransactionRequest: V1ValidateAmazonTransactionRequest{
					AppServiceId: "appServiceId",
					Receipt:      "receipt",
					AmazonUserId: "amazonUserId",
					AllowExpired: true,
				},
			},
			deps: V1Handler{
				svc:           svcMock,
				planClient:    planClientMock,
				amazonClient:  amazonClientMock,
				rokuClient:    rokuClientMock,
				samsungClient: samsungClientMock,
				stripeClient:  stripeClientMock,
				stat:          s,
				secrets: map[string]map[string]string{
					"amazonstore": {
						"plan-appid": "test-should-pass",
					},
				},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
				featureClient:          ff,
			},
			runMocks: func() {
				plan := planclient.Plan{
					AppID:        "plan-appid",
					AppServiceID: "appServiceId",
					Price: []planclient.Price{
						{
							RetailPrice:  1.99,
							CurrencyCode: "USD",
						},
					},
				}
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(plan, nil).Once()
				mockedReceipt = &receipt.AmazonStore{
					Data: receipt.AmazonResponse{
						CancelDate: 1,
						ProductID:  "appServiceId",
						Term:       "1 Month",
					},
				}
			},
			receiptNewAmazonStore: mockReceiptNewAmazonStoreFunc,
			expectedStatusCode:    http.StatusOK,
		},
		"success: should return ok when Amazon Transaction is correctly validated": {
			body: V1ValidateAmazonTransactionRequestInput{
				V1ValidateAmazonTransactionRequest: V1ValidateAmazonTransactionRequest{
					AppServiceId: "appServiceId",
					Receipt:      "receipt",
					AmazonUserId: "amazonUserId",
				},
			},
			deps: V1Handler{
				svc:           svcMock,
				planClient:    planClientMock,
				amazonClient:  amazonClientMock,
				rokuClient:    rokuClientMock,
				samsungClient: samsungClientMock,
				stripeClient:  stripeClientMock,
				stat:          s,
				secrets: map[string]map[string]string{
					"amazonstore": {
						"plan-appid": "test-should-pass",
					},
				},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
				featureClient:          ff,
			},
			runMocks: func() {
				plan := planclient.Plan{
					AppID:        "plan-appid",
					AppServiceID: "appServiceId",
					Price: []planclient.Price{
						{
							RetailPrice:  1.99,
							CurrencyCode: "USD",
						},
					},
				}
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(plan, nil).Once()
				mockedReceipt = &receipt.AmazonStore{
					Data: receipt.AmazonResponse{
						ProductID: "appServiceId",
						Term:      "1 Month",
					},
				}
			},
			receiptNewAmazonStore: mockReceiptNewAmazonStoreFunc,
			expectedStatusCode:    http.StatusOK,
		},
	}

	for name, test := range tests {
		t.Run(name, func(t *testing.T) {
			var b bytes.Buffer
			if err := json.NewEncoder(&b).Encode(test.body); err != nil {
				t.Fatal(err)
			}
			body := io.NopCloser(&b)

			// Define globals
			receiptNewAmazonStoreFunc = test.receiptNewAmazonStore

			w := httptest.NewRecorder()
			rh, r := request.NewWithRequest(httptest.NewRequest(http.MethodPost, "/receiptverify/validate-amazon-transaction", body), "receiptverify")
			rh.SetAPIKey(apikeys.APIKey{Brand: "test-brand"})
			rh.SetLoggingEntry(l)

			h := newReceiptVerifyHandler(test.deps.svc, s, test.deps.planClient, test.deps.amazonClient, test.deps.rokuClient, test.deps.samsungClient, test.deps.stripeClient, test.deps.googleHttpClient, test.deps.secrets, test.deps.appstoreApiKeyId, test.deps.appstoreApiIssuerId, test.deps.appstoresdkApiKeyId, test.deps.appstoresdkApiIssuerId, test.deps.featureClient)
			if test.runMocks != nil {
				test.runMocks()
			}
			handler := V1ValidateAmazonTransactionValidator(route.Route{})(http.HandlerFunc(h.V1ValidateAmazonTransaction))
			handler.ServeHTTP(w, r)
			res := w.Result()
			defer res.Body.Close()
			assert.Equal(t, test.expectedStatusCode, res.StatusCode, w.Body.String())
		})
	}
}

func TestV1AmazonTransactionWithoutValidator(t *testing.T) {
	s, _ := stats.New("receiptverify", "v1", stats.WithDevMode())
	req, err := http.NewRequest("POST", "/receiptverify/validate-amazon-transaction", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	ff, _ := featureflag.MakeFeatureClient("", &config.Config{})
	h := newReceiptVerifyHandler(mocks.NewIAP(t), s, mocks.NewPlanClient(t), mocks.NewCircuitAPI(t), mocks.NewCircuitAPI(t), mocks.NewCircuitAPI(t), mocks.NewCircuitAPI(t), &http.Client{}, map[string]map[string]string{}, "", "", "", "", ff)

	handler := http.HandlerFunc(h.V1ValidateAmazonTransaction)
	handler.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusBadRequest, rr.Code, "Should have returned status BadRequest")
}
