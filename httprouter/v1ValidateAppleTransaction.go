package httprouter

import (
	"fmt"
	"net/http"
	"os"
	"time"

	planclient "github.com/foxcorp-product/commerce-plan/client"
	"github.com/foxcorp-product/commerce-receiptverify/iap/receipt"
	"github.com/foxcorp-product/commerce-receiptverify/internal/response"
	"github.com/foxcorp-product/entitlement-sdk/inputvalidator"
	"github.com/foxcorp-product/entitlement-sdk/request"
)

var (
	errTransactionRequired = "signed transaction is required"

	receiptValidateAppStoreTransactionFunc = receipt.ValidateAppStoreTransaction
	newValidateAppStoreTransactionRes      = NewValidateAppStoreTransactionRes
)

var V1ValidateAppleTransactionValidator = inputvalidator.MakeRouteValidatorMiddleware[V1ValidateAppleTransactionRequestInput](
	inputvalidator.WithCustomErrorResponse(response.SendErrors),
	inputvalidator.WithRule("AppServiceID", "required", errAppServiceIdRequired),
	inputvalidator.WithRule("JWSTransaction", "required", errTransactionRequired),
	inputvalidator.WithoutErrorWrap(),
	inputvalidator.WithoutErrorRuleName(),
)

type V1ValidateAppleTransactionRequestInput struct {
	V1ValidateAppleTransactionRequest `in:"body=json" validate:"required"`
}

type V1ValidateAppleTransactionRequest struct {
	AppServiceID   string `json:"appServiceId"`
	JWSTransaction string `json:"receipt"`
	AllowExpired   bool   `json:"allowExpired"`
}

func (h V1Handler) V1ValidateAppleTransaction(w http.ResponseWriter, r *http.Request) {
	var (
		err error
	)

	ctx, span := h.stat.StartMethodSpan(r.Context(), "V1Handler.V1ValidateAppleTransaction")
	defer func() { span.FinishWithError(err) }()

	l := request.GetFromContext(ctx).GetLoggingEntry()

	in, ok := inputvalidator.GetValidatedStruct[V1ValidateAppleTransactionRequestInput](r.Context())
	if !ok {
		response.SendErrors(w, http.StatusBadRequest, fmt.Errorf("bad request"))
		return
	}
	usemockserver := h.featureClient.GetUseMockServer(ctx)
	transaction, err := receiptValidateAppStoreTransactionFunc(in.JWSTransaction, usemockserver)
	if err != nil {
		l.Errorf("validate apple transaction error: %v", err.Error())
		response.SendErrors(w, http.StatusUnauthorized, err)
		l.WithField("err", "validateAppStoreTransaction:"+err.Error())
		return
	}

	//FIXME: This is a temporary patch to allow QA do testing
	if in.AllowExpired && os.Getenv("SERVICE_ENV") != "prod1" {
		transaction.ExpiresDate = time.Now().AddDate(0, 1, 0).UnixMilli()
	}

	plan, err := h.planClient.GetPlanByAppServiceID(ctx, planclient.V1GetPlanByAppServiceIDInput{
		AppServiceID: in.AppServiceID,
	})

	if err != nil {
		l.Errorf("validate apple transaction verify plan error: %v", err.Error())
		response.SendErrors(w, http.StatusUnauthorized, err)
		l.WithField("err", "verifyPlanFunc:"+err.Error())
		return
	}

	verifyRes := newValidateAppStoreTransactionRes(transaction, plan)
	if err != nil {
		response.SendErrors(w, http.StatusUnauthorized, err)
		l.WithField("err", "newV1VerifyRes:"+err.Error())
		return
	}

	l.WithField("transactionId", verifyRes.TransactionID)
	response.Send(w, http.StatusOK, verifyRes)
}
