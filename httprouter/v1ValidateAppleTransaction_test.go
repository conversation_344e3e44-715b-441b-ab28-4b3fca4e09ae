package httprouter

import (
	"bytes"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/awa/go-iap/appstore/api"
	planclient "github.com/foxcorp-product/commerce-plan/client"
	"github.com/foxcorp-product/commerce-receiptverify/config"
	"github.com/foxcorp-product/commerce-receiptverify/internal/featureflag"
	"github.com/foxcorp-product/commerce-receiptverify/mocks"
	"github.com/foxcorp-product/entitlement-sdk/apikeys"
	"github.com/foxcorp-product/entitlement-sdk/logger"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/foxcorp-product/entitlement-sdk/route"
	"github.com/foxcorp-product/entitlement-sdk/stats"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func mockReceiptValidateAppStoreTransaction(signedTransaction string, usemock bool) (*api.JWSTransaction, error) {
	if signedTransaction == "invalid" {
		return nil, errors.New("invalid transaction")
	}
	return &api.JWSTransaction{}, nil
}

func mockNewValidateAppStoreTransaction(transaction *api.JWSTransaction, plan planclient.Plan) V1VerifyResponse {
	return V1VerifyResponse{}
}

func TestV1AppleTransaction(t *testing.T) {
	// Generate the mocks for dependencies
	svcMock := mocks.NewIAP(t)
	planClientMock := mocks.NewPlanClient(t)
	amazonClientMock := mocks.NewCircuitAPI(t)
	rokuClientMock := mocks.NewCircuitAPI(t)
	samsungClientMock := mocks.NewCircuitAPI(t)
	stripeClientMock := mocks.NewCircuitAPI(t)
	ff, _ := featureflag.MakeFeatureClient("", &config.Config{})

	s, _ := stats.New("receiptverify", "v1", stats.WithDevMode())
	l, _ := logger.New()
	tests := map[string]struct {
		body                               V1ValidateAppleTransactionRequestInput
		deps                               V1Handler
		runMocks                           func()
		receiptValidateAppStoreTransaction func(signedTransaction string, usemock bool) (*api.JWSTransaction, error)
		newValidateAppStoreTransaction     func(transaction *api.JWSTransaction, plan planclient.Plan) V1VerifyResponse
		expectedStatusCode                 int
	}{
		"failure: should return unauthorized when invalid JWSTransaction is provided": {
			body: V1ValidateAppleTransactionRequestInput{
				V1ValidateAppleTransactionRequest: V1ValidateAppleTransactionRequest{
					AppServiceID:   "appServiceId",
					JWSTransaction: "invalid",
					AllowExpired:   true,
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
				featureClient:          ff,
			},
			runMocks: func() {
			},
			receiptValidateAppStoreTransaction: mockReceiptValidateAppStoreTransaction,
			expectedStatusCode:                 http.StatusUnauthorized,
		},
		"failure: should return unauthorized when GetPlanByAppServiceID returns an error": {
			body: V1ValidateAppleTransactionRequestInput{
				V1ValidateAppleTransactionRequest: V1ValidateAppleTransactionRequest{
					AppServiceID:   "appServiceId",
					JWSTransaction: "valid",
					AllowExpired:   true,
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
				featureClient:          ff,
			},
			runMocks: func() {
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{}, errors.New("plan error")).Once()
			},
			receiptValidateAppStoreTransaction: mockReceiptValidateAppStoreTransaction,
			expectedStatusCode:                 http.StatusUnauthorized,
		},
		"success: should return ok when Apple Transaction is correctly validated": {
			body: V1ValidateAppleTransactionRequestInput{
				V1ValidateAppleTransactionRequest: V1ValidateAppleTransactionRequest{
					AppServiceID:   "appServiceId",
					JWSTransaction: "valid",
					AllowExpired:   true,
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
				featureClient:          ff,
			},
			runMocks: func() {
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{}, nil).Once()
			},
			receiptValidateAppStoreTransaction: mockReceiptValidateAppStoreTransaction,
			newValidateAppStoreTransaction:     mockNewValidateAppStoreTransaction,
			expectedStatusCode:                 http.StatusOK,
		},
	}

	for name, test := range tests {
		t.Run(name, func(t *testing.T) {
			var b bytes.Buffer
			if err := json.NewEncoder(&b).Encode(test.body); err != nil {
				t.Fatal(err)
			}
			body := io.NopCloser(&b)

			// Define globals
			receiptValidateAppStoreTransactionFunc = test.receiptValidateAppStoreTransaction
			newValidateAppStoreTransactionRes = test.newValidateAppStoreTransaction

			w := httptest.NewRecorder()
			rh, r := request.NewWithRequest(httptest.NewRequest(http.MethodPost, "/receiptverify/validate-appstore-transaction", body), "receiptverify")
			rh.SetAPIKey(apikeys.APIKey{Brand: "test-brand"})
			rh.SetLoggingEntry(l)

			h := newReceiptVerifyHandler(test.deps.svc, s, test.deps.planClient, test.deps.amazonClient, test.deps.rokuClient, test.deps.samsungClient, test.deps.stripeClient, test.deps.googleHttpClient, test.deps.secrets, test.deps.appstoreApiKeyId, test.deps.appstoreApiIssuerId, test.deps.appstoresdkApiKeyId, test.deps.appstoresdkApiIssuerId, test.deps.featureClient)
			if test.runMocks != nil {
				test.runMocks()
			}
			handler := V1ValidateAppleTransactionValidator(route.Route{})(http.HandlerFunc(h.V1ValidateAppleTransaction))
			handler.ServeHTTP(w, r)
			res := w.Result()
			defer res.Body.Close()
			if res.StatusCode != test.expectedStatusCode {
				t.Errorf("expected %d, but got %d", test.expectedStatusCode, res.StatusCode)
			}
		})
	}
}

func TestV1AppleTransactionWithoutValidator(t *testing.T) {
	s, _ := stats.New("receiptverify", "v1", stats.WithDevMode())
	req, err := http.NewRequest("POST", "/receiptverify/validate-appstore-transaction", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	ff, _ := featureflag.MakeFeatureClient("", &config.Config{})
	h := newReceiptVerifyHandler(mocks.NewIAP(t), s, mocks.NewPlanClient(t), mocks.NewCircuitAPI(t), mocks.NewCircuitAPI(t), mocks.NewCircuitAPI(t), mocks.NewCircuitAPI(t), &http.Client{}, map[string]map[string]string{}, "", "", "", "", ff)

	handler := http.HandlerFunc(h.V1ValidateAppleTransaction)
	handler.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusBadRequest, rr.Code, "Should have returned status BadRequest")
}
