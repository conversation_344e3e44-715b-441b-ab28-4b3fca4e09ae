package httprouter

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"

	planclient "github.com/foxcorp-product/commerce-plan/client"
	"github.com/foxcorp-product/commerce-receiptverify/iap/receipt"
	"github.com/foxcorp-product/commerce-receiptverify/internal/response"
	"github.com/foxcorp-product/entitlement-sdk/inputvalidator"
	"github.com/foxcorp-product/entitlement-sdk/request"
)

var (
	errPackageNameRequired  = "package name is required"
	errProductIdRequired    = "product id is required"
	errTokenRequired        = "token is required"
	errNoGoogleApiKeyFound  = errors.New("no google api key was found")
	receiptNewPlayStoreFunc = receipt.NewPlayStore
)

var V1ValidateGoogleTransactionValidator = inputvalidator.MakeRouteValidatorMiddleware[V1ValidateGoogleTransactionRequestInput](
	inputvalidator.WithCustomErrorResponse(response.SendErrors),
	inputvalidator.WithRule("PackageName", "required", errPackageNameRequired),
	inputvalidator.WithRule("ProductID", "required", errProductIdRequired),
	inputvalidator.WithRule("PurchaseToken", "required", errTokenRequired),
	inputvalidator.WithoutErrorWrap(),
	inputvalidator.WithoutErrorRuleName(),
)

type V1ValidateGoogleTransactionRequestInput struct {
	V1ValidateGoogleTransactionRequest `in:"body=json" validate:"required"`
}

type V1ValidateGoogleTransactionRequest struct {
	PackageName   string `json:"packageName"`
	ProductID     string `json:"productId"`
	PurchaseToken string `json:"purchaseToken"`
}

func (h V1Handler) V1ValidateGoogleTransaction(w http.ResponseWriter, r *http.Request) {
	var err error

	l := request.GetFromContext(r.Context()).GetLoggingEntry()

	ctx, span := h.stat.StartMethodSpan(r.Context(), "V1Handler.V1ValidateGoogleTransaction")
	defer func() { span.FinishWithError(err) }()

	in, ok := inputvalidator.GetValidatedStruct[V1ValidateGoogleTransactionRequestInput](r.Context())
	if !ok {
		response.SendErrors(w, http.StatusBadRequest, fmt.Errorf("bad request"))
		return
	}

	p, err := h.planClient.GetPlanByAppServiceID(ctx, planclient.V1GetPlanByAppServiceIDInput{
		AppServiceID: in.ProductID,
	})
	if err != nil {
		response.SendErrors(w, http.StatusUnauthorized, err)
		request.GetFromContext(ctx).SetLoggingEntry(l.WithField("err", "playstore verifyPlanFunc:"+err.Error()))
		return
	}

	apikey := h.secrets["playstore"][p.AppID]
	if len(apikey) == 0 {
		response.SendErrors(w, http.StatusUnauthorized, errNoGoogleApiKeyFound)
		request.GetFromContext(ctx).SetLoggingEntry(l.WithField("err", "secrets:"+errNoGoogleApiKeyFound.Error()+" for "+p.AppID))
		return
	}

	b, err := json.Marshal(V1ValidateGoogleTransactionRequest{
		PackageName:   in.PackageName,
		ProductID:     in.ProductID,
		PurchaseToken: in.PurchaseToken,
	})
	if err != nil {
		response.SendErrors(w, http.StatusUnauthorized, err)
		request.GetFromContext(ctx).SetLoggingEntry(l.WithField("err", "playstore marshalling req:"+err.Error()))
		return
	}
	payload := receipt.PlayStorePayload{
		Encrypted: base64.StdEncoding.EncodeToString(b),
	}

	rs, err := receiptNewPlayStoreFunc(ctx, h.stat, apikey, payload, h.planClient, h.googleHttpClient)
	if err != nil {
		response.SendErrors(w, http.StatusUnauthorized, err)
		request.GetFromContext(ctx).SetLoggingEntry(l.WithField("err", "receipt.NewPlayStore:"+err.Error()))
		return
	}

	verifyRes := receipt.NewPlayStoreResponse(ctx, rs.Data)
	res := newV1ValidateGoogleTransactionRes(verifyRes)

	response.Send(w, http.StatusOK, res)
}
