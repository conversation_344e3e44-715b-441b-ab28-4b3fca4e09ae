package httprouter

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	planclient "github.com/foxcorp-product/commerce-plan/client"
	"github.com/foxcorp-product/commerce-receiptverify/config"
	"github.com/foxcorp-product/commerce-receiptverify/iap/receipt"
	"github.com/foxcorp-product/commerce-receiptverify/internal/featureflag"
	"github.com/foxcorp-product/commerce-receiptverify/mocks"
	"github.com/foxcorp-product/entitlement-sdk/apikeys"
	"github.com/foxcorp-product/entitlement-sdk/logger"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/foxcorp-product/entitlement-sdk/route"
	"github.com/foxcorp-product/entitlement-sdk/stats"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func mockNewPlayStoreFunc(ctx context.Context, stat receipt.Stats, secret string, payload receipt.PlayStorePayload, planClient receipt.PlanClient, googleClient *http.Client) (*receipt.PlayStore, error) {
	if secret == "test-should-error" {
		return nil, errors.New("mockNewPlayStoreFunc error")
	}
	return &receipt.PlayStore{}, nil
}

func TestV1GoogleTransaction(t *testing.T) {
	// Generate the mocks for dependencies
	svcMock := mocks.NewIAP(t)
	planClientMock := mocks.NewPlanClient(t)
	amazonClientMock := mocks.NewCircuitAPI(t)
	rokuClientMock := mocks.NewCircuitAPI(t)
	samsungClientMock := mocks.NewCircuitAPI(t)
	stripeClientMock := mocks.NewCircuitAPI(t)

	s, _ := stats.New("receiptverify", "v1", stats.WithDevMode())
	l, _ := logger.New()
	tests := map[string]struct {
		body                V1ValidateGoogleTransactionRequestInput
		deps                V1Handler
		runMocks            func()
		receiptNewPlayStore func(ctx context.Context, stat receipt.Stats, secret string, payload receipt.PlayStorePayload, planClient receipt.PlanClient, googleClient *http.Client) (*receipt.PlayStore, error)
		expectedStatusCode  int
	}{
		"failure: should return unauthorized when GetPlanByAppServiceID returns an error": {
			body: V1ValidateGoogleTransactionRequestInput{
				V1ValidateGoogleTransactionRequest: V1ValidateGoogleTransactionRequest{
					ProductID:     "productId",
					PackageName:   "packageName",
					PurchaseToken: "invalid",
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{}, errors.New("error")).Once()
			},
			expectedStatusCode: http.StatusUnauthorized,
		},
		"failure: should return unauthorized when invalid apikey is provided": {
			body: V1ValidateGoogleTransactionRequestInput{
				V1ValidateGoogleTransactionRequest: V1ValidateGoogleTransactionRequest{
					ProductID:     "productId",
					PackageName:   "packageName",
					PurchaseToken: "invalid",
				},
			},
			deps: V1Handler{
				svc:           svcMock,
				planClient:    planClientMock,
				amazonClient:  amazonClientMock,
				rokuClient:    rokuClientMock,
				samsungClient: samsungClientMock,
				stripeClient:  stripeClientMock,
				stat:          s,
				secrets: map[string]map[string]string{
					"playstore": {
						"missing": "test-should-ok",
					},
				},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{AppID: "plan-appid"}, nil).Once()
			},
			expectedStatusCode: http.StatusUnauthorized,
		},
		"failure: should return unauthorized when receiptNewPlayStore returns an error": {
			body: V1ValidateGoogleTransactionRequestInput{
				V1ValidateGoogleTransactionRequest: V1ValidateGoogleTransactionRequest{
					ProductID:     "productId",
					PurchaseToken: "token",
					PackageName:   "packageName",
				},
			},
			deps: V1Handler{
				svc:           svcMock,
				planClient:    planClientMock,
				amazonClient:  amazonClientMock,
				rokuClient:    rokuClientMock,
				samsungClient: samsungClientMock,
				stripeClient:  stripeClientMock,
				stat:          s,
				secrets: map[string]map[string]string{
					"playstore": {
						"plan-appid": "test-should-error",
					},
				},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{AppID: "plan-appid"}, nil).Once()
			},
			receiptNewPlayStore: mockNewPlayStoreFunc,
			expectedStatusCode:  http.StatusUnauthorized,
		},
		"success: should return ok when Google Transaction is correctly validated": {
			body: V1ValidateGoogleTransactionRequestInput{
				V1ValidateGoogleTransactionRequest: V1ValidateGoogleTransactionRequest{
					ProductID:     "productId",
					PurchaseToken: "token",
					PackageName:   "packageName",
				},
			},
			deps: V1Handler{
				svc:           svcMock,
				planClient:    planClientMock,
				amazonClient:  amazonClientMock,
				rokuClient:    rokuClientMock,
				samsungClient: samsungClientMock,
				stripeClient:  stripeClientMock,
				stat:          s,
				secrets: map[string]map[string]string{
					"playstore": {
						"plan-appid": "test-should-ok",
					},
				},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{AppID: "plan-appid"}, nil).Once()
			},
			receiptNewPlayStore: mockNewPlayStoreFunc,
			expectedStatusCode:  http.StatusOK,
		},
	}

	for name, test := range tests {
		t.Run(name, func(t *testing.T) {
			var b bytes.Buffer
			if err := json.NewEncoder(&b).Encode(test.body); err != nil {
				t.Fatal(err)
			}
			body := io.NopCloser(&b)

			// Define globals
			receiptNewPlayStoreFunc = test.receiptNewPlayStore
			w := httptest.NewRecorder()
			rh, r := request.NewWithRequest(httptest.NewRequest(http.MethodPost, "/receiptverify/validate-appstore-transaction", body), "receiptverify")
			rh.SetAPIKey(apikeys.APIKey{Brand: "test-brand"})
			rh.SetLoggingEntry(l)

			h := newReceiptVerifyHandler(test.deps.svc, s, test.deps.planClient, test.deps.amazonClient, test.deps.rokuClient, test.deps.samsungClient, test.deps.stripeClient, test.deps.googleHttpClient, test.deps.secrets, test.deps.appstoreApiKeyId, test.deps.appstoreApiIssuerId, test.deps.appstoresdkApiKeyId, test.deps.appstoresdkApiIssuerId, test.deps.featureClient)
			if test.runMocks != nil {
				test.runMocks()
			}
			handler := V1ValidateGoogleTransactionValidator(route.Route{})(http.HandlerFunc(h.V1ValidateGoogleTransaction))
			handler.ServeHTTP(w, r)
			res := w.Result()
			defer res.Body.Close()
			if res.StatusCode != test.expectedStatusCode {
				t.Errorf("expected %d, but got %d", test.expectedStatusCode, res.StatusCode)
			}
		})
	}
}

func TestV1GoogleTransactionWithoutValidator(t *testing.T) {
	s, _ := stats.New("receiptverify", "v1", stats.WithDevMode())
	req, err := http.NewRequest("POST", "/receiptverify/validate-google-transaction", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	ff, _ := featureflag.MakeFeatureClient("", &config.Config{})
	h := newReceiptVerifyHandler(mocks.NewIAP(t), s, mocks.NewPlanClient(t), mocks.NewCircuitAPI(t), mocks.NewCircuitAPI(t), mocks.NewCircuitAPI(t), mocks.NewCircuitAPI(t), &http.Client{}, map[string]map[string]string{}, "", "", "", "", ff)

	handler := http.HandlerFunc(h.V1ValidateGoogleTransaction)
	handler.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusBadRequest, rr.Code, "Should have returned status BadRequest")
}
