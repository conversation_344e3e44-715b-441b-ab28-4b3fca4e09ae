package httprouter

import (
	"errors"
	"fmt"
	"net/http"

	planclient "github.com/foxcorp-product/commerce-plan/client"
	"github.com/foxcorp-product/commerce-receiptverify/iap/receipt"
	"github.com/foxcorp-product/commerce-receiptverify/internal/response"
	"github.com/foxcorp-product/entitlement-sdk/inputvalidator"
	"github.com/foxcorp-product/entitlement-sdk/logger"
	"github.com/foxcorp-product/entitlement-sdk/request"
)

var (
	errAppServiceIdRequired  = "appServiceId is required"
	errTransactionIdRequired = "transactionId is required"
	errNoRokuApiKeyFound     = errors.New("no roku api key was found")
	receiptNewRokuStoreFunc  = receipt.NewRokuStore
	newRokuVerifyResFunc     = NewRokuVerifyRes
)

var V1ValidateRokuTransactionValidator = inputvalidator.MakeRouteValidatorMiddleware[V1ValidateRokuTransactionRequestInput](
	inputvalidator.WithCustomErrorResponse(response.SendErrors),
	inputvalidator.WithRule("AppServiceID", "required", errAppServiceIdRequired),
	inputvalidator.WithRule("TransactionID", "required", errTransactionIdRequired),
	inputvalidator.WithoutErrorWrap(),
	inputvalidator.WithoutErrorRuleName(),
)

type V1ValidateRokuTransactionRequestInput struct {
	V1ValidateRokuTransactionRequest `in:"body=json" validate:"required"`
}

type V1ValidateRokuTransactionRequest struct {
	AppServiceID  string `json:"appServiceId"`
	TransactionID string `json:"transactionId"`
}

func (h V1Handler) V1ValidateRokuTransaction(w http.ResponseWriter, r *http.Request) {
	var (
		err error
	)

	rh := request.GetFromContext(r.Context())
	l := rh.GetLoggingEntry()

	ctx, span := h.stat.StartMethodSpan(r.Context(), "V1Handler.V1ValidateRokuTransaction")
	defer func() { span.FinishWithError(err) }()

	in, ok := inputvalidator.GetValidatedStruct[V1ValidateRokuTransactionRequestInput](r.Context())
	if !ok {
		response.SendErrors(w, http.StatusBadRequest, fmt.Errorf("bad request"))
		return
	}

	l = l.WithFields(logger.Fields{
		"appServiceId":  in.AppServiceID,
		"transactionId": in.TransactionID,
	})
	rh.SetLoggingEntry(l)

	p, err := h.planClient.GetPlanByAppServiceID(ctx, planclient.V1GetPlanByAppServiceIDInput{
		AppServiceID: in.AppServiceID,
	})

	if err != nil {
		response.SendErrors(w, http.StatusUnauthorized, err)
		l.Error("roku verifyPlanFunc:" + err.Error())
		return
	}

	apikey := h.secrets["rokustore"][p.AppID]
	if len(apikey) == 0 {
		response.SendErrors(w, http.StatusUnauthorized, errNoRokuApiKeyFound)
		l.Error("secrets:" + errNoRokuApiKeyFound.Error() + " for " + p.AppID)
		return
	}

	conf := receipt.RokuStoreConfig{
		ReceiptID: in.TransactionID, // duplicate to avoid bad request
	}
	rs, err := receiptNewRokuStoreFunc(ctx, h.stat, apikey, conf, h.rokuClient)
	if err != nil {
		l.Error("receipt.NewRokuStore:" + err.Error())
		response.SendErrors(w, http.StatusUnauthorized, err)
		return
	}

	// check if receipt have unexpired
	rec, err := rs.UnExpired()
	if err != nil {
		l.Error("V1ValidateRokuTransaction receipt.UnExpired:" + err.Error())
		response.Send(w, http.StatusUnauthorized, V1VerifyResponse{})
		return
	}

	verifyRes, err := newRokuVerifyResFunc(rec)
	if err != nil {
		l.Error("V1ValidateRokuTransaction newV1VerifyRes:" + err.Error())
		response.SendErrors(w, http.StatusUnauthorized, err)
		return
	}

	response.Send(w, http.StatusOK, verifyRes)
}
