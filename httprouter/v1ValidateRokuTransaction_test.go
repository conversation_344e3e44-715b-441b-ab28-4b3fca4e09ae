package httprouter

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	planclient "github.com/foxcorp-product/commerce-plan/client"
	"github.com/foxcorp-product/commerce-receiptverify/config"
	"github.com/foxcorp-product/commerce-receiptverify/iap/receipt"
	"github.com/foxcorp-product/commerce-receiptverify/internal/featureflag"
	"github.com/foxcorp-product/commerce-receiptverify/mocks"
	"github.com/foxcorp-product/entitlement-sdk/apikeys"
	"github.com/foxcorp-product/entitlement-sdk/logger"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/foxcorp-product/entitlement-sdk/route"
	"github.com/foxcorp-product/entitlement-sdk/stats"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

var mockRokuStoreReceipt *receipt.RokuStoreSVC

func mockReceiptNewRokuStoreFunc(ctx context.Context, stat receipt.Stats, secret string, conf receipt.RokuStoreConfig, rokuClient receipt.CircuitAPI) (*receipt.RokuStoreSVC, error) {
	if secret == "test-should-error" {
		return nil, errors.New("mockReceiptNewRokuStoreFunc error")
	}
	return mockRokuStoreReceipt, nil
}

func mockNewRokuVerifyResFunc(recRaw interface{}) (V1VerifyResponseRoku, error) {
	if rec, ok := recRaw.(receipt.RokuResponse); ok && rec.ErrorMessage == "test-should-error" {
		return V1VerifyResponseRoku{}, errors.New("mockNewRokuVerifyResFunc error")
	}
	return V1VerifyResponseRoku{}, nil
}

func TestV1RokuTransaction(t *testing.T) {
	// Generate the mocks for dependencies
	svcMock := mocks.NewIAP(t)
	planClientMock := mocks.NewPlanClient(t)
	amazonClientMock := mocks.NewCircuitAPI(t)
	rokuClientMock := mocks.NewCircuitAPI(t)
	samsungClientMock := mocks.NewCircuitAPI(t)
	stripeClientMock := mocks.NewCircuitAPI(t)

	s, _ := stats.New("receiptverify", "v1", stats.WithDevMode())
	l, _ := logger.New()
	tests := map[string]struct {
		body                V1ValidateRokuTransactionRequestInput
		deps                V1Handler
		runMocks            func()
		receiptNewRokuStore func(ctx context.Context, stat receipt.Stats, secret string, conf receipt.RokuStoreConfig, rokuClient receipt.CircuitAPI) (*receipt.RokuStoreSVC, error)
		newRokuVerifyRes    func(recRaw interface{}) (V1VerifyResponseRoku, error)
		expectedStatusCode  int
	}{
		"failure: should return unauthorized when GetPlanByAppServiceID returns an error": {
			body: V1ValidateRokuTransactionRequestInput{
				V1ValidateRokuTransactionRequest: V1ValidateRokuTransactionRequest{
					AppServiceID:  "appServiceId",
					TransactionID: "transactionId",
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{}, errors.New("error")).Once()
			},
			expectedStatusCode: http.StatusUnauthorized,
		},
		"failure: should return unauthorized when invalid api key is provided": {
			body: V1ValidateRokuTransactionRequestInput{
				V1ValidateRokuTransactionRequest: V1ValidateRokuTransactionRequest{
					AppServiceID:  "appServiceId",
					TransactionID: "transactionId",
				},
			},
			deps: V1Handler{
				svc:           svcMock,
				planClient:    planClientMock,
				amazonClient:  amazonClientMock,
				rokuClient:    rokuClientMock,
				samsungClient: samsungClientMock,
				stripeClient:  stripeClientMock,
				stat:          s,
				secrets: map[string]map[string]string{
					"rokustore": {
						"invalid": "invalid",
					},
				},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{AppID: "plan-appid"}, nil).Once()
			},
			expectedStatusCode: http.StatusUnauthorized,
		},
		"failure: should return unauthorized when receiptNewRokuStore returns an error": {
			body: V1ValidateRokuTransactionRequestInput{
				V1ValidateRokuTransactionRequest: V1ValidateRokuTransactionRequest{
					AppServiceID:  "appServiceId",
					TransactionID: "transactionId",
				},
			},
			deps: V1Handler{
				svc:           svcMock,
				planClient:    planClientMock,
				amazonClient:  amazonClientMock,
				rokuClient:    rokuClientMock,
				samsungClient: samsungClientMock,
				stripeClient:  stripeClientMock,
				stat:          s,
				secrets: map[string]map[string]string{
					"rokustore": {
						"plan-appid": "test-should-error",
					},
				},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{AppID: "plan-appid"}, nil).Once()
				mockRokuStoreReceipt = &receipt.RokuStoreSVC{}
			},
			receiptNewRokuStore: mockReceiptNewRokuStoreFunc,
			expectedStatusCode:  http.StatusUnauthorized,
		},
		"failure: should return unauthorized when unExpired returns an error": {
			body: V1ValidateRokuTransactionRequestInput{
				V1ValidateRokuTransactionRequest: V1ValidateRokuTransactionRequest{
					AppServiceID:  "appServiceId",
					TransactionID: "transactionId",
				},
			},
			deps: V1Handler{
				svc:           svcMock,
				planClient:    planClientMock,
				amazonClient:  amazonClientMock,
				rokuClient:    rokuClientMock,
				samsungClient: samsungClientMock,
				stripeClient:  stripeClientMock,
				stat:          s,
				secrets: map[string]map[string]string{
					"rokustore": {
						"plan-appid": "test-should-ok",
					},
				},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{AppID: "plan-appid"}, nil).Once()
				mockRokuStoreReceipt = &receipt.RokuStoreSVC{
					Data: receipt.RokuResponse{
						PurchaseStatus: "Inactive",
					},
				}
			},
			receiptNewRokuStore: mockReceiptNewRokuStoreFunc,
			expectedStatusCode:  http.StatusUnauthorized,
		},
		"failure: should return unauthorized when newRokuVerifyRes returns an error": {
			body: V1ValidateRokuTransactionRequestInput{
				V1ValidateRokuTransactionRequest: V1ValidateRokuTransactionRequest{
					AppServiceID:  "appServiceId",
					TransactionID: "transactionId",
				},
			},
			deps: V1Handler{
				svc:           svcMock,
				planClient:    planClientMock,
				amazonClient:  amazonClientMock,
				rokuClient:    rokuClientMock,
				samsungClient: samsungClientMock,
				stripeClient:  stripeClientMock,
				stat:          s,
				secrets: map[string]map[string]string{
					"rokustore": {
						"plan-appid": "test-should-ok",
					},
				},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{AppID: "plan-appid"}, nil).Once()
				mockRokuStoreReceipt = &receipt.RokuStoreSVC{
					Data: receipt.RokuResponse{
						Status:         0,
						IsEntitled:     true,
						PurchaseStatus: "Active",
						ErrorMessage:   "test-should-error",
					},
				}
			},
			receiptNewRokuStore: mockReceiptNewRokuStoreFunc,
			newRokuVerifyRes:    mockNewRokuVerifyResFunc,
			expectedStatusCode:  http.StatusUnauthorized,
		},
		"success: should return ok when Roku Transaction is correctly validated": {
			body: V1ValidateRokuTransactionRequestInput{
				V1ValidateRokuTransactionRequest: V1ValidateRokuTransactionRequest{
					AppServiceID:  "appServiceId",
					TransactionID: "transactionId",
				},
			},
			deps: V1Handler{
				svc:           svcMock,
				planClient:    planClientMock,
				amazonClient:  amazonClientMock,
				rokuClient:    rokuClientMock,
				samsungClient: samsungClientMock,
				stripeClient:  stripeClientMock,
				stat:          s,
				secrets: map[string]map[string]string{
					"rokustore": {
						"plan-appid": "test-should-ok",
					},
				},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{AppID: "plan-appid"}, nil).Once()
				mockRokuStoreReceipt = &receipt.RokuStoreSVC{
					Data: receipt.RokuResponse{
						Status:         0,
						IsEntitled:     true,
						PurchaseStatus: "Active",
					},
				}
			},
			receiptNewRokuStore: mockReceiptNewRokuStoreFunc,
			newRokuVerifyRes:    mockNewRokuVerifyResFunc,
			expectedStatusCode:  http.StatusOK,
		},
	}

	for name, test := range tests {
		t.Run(name, func(t *testing.T) {
			var b bytes.Buffer
			if err := json.NewEncoder(&b).Encode(test.body); err != nil {
				t.Fatal(err)
			}
			body := io.NopCloser(&b)

			// Define globals
			receiptNewRokuStoreFunc = test.receiptNewRokuStore
			newRokuVerifyResFunc = test.newRokuVerifyRes

			w := httptest.NewRecorder()
			rh, r := request.NewWithRequest(httptest.NewRequest(http.MethodPost, "/receiptverify/validate-roku-transaction", body), "receiptverify")
			rh.SetAPIKey(apikeys.APIKey{Brand: "test-brand"})
			rh.SetLoggingEntry(l)

			h := newReceiptVerifyHandler(test.deps.svc, s, test.deps.planClient, test.deps.amazonClient, test.deps.rokuClient, test.deps.samsungClient, test.deps.stripeClient, test.deps.googleHttpClient, test.deps.secrets, test.deps.appstoreApiKeyId, test.deps.appstoreApiIssuerId, test.deps.appstoresdkApiKeyId, test.deps.appstoresdkApiIssuerId, test.deps.featureClient)
			if test.runMocks != nil {
				test.runMocks()
			}
			handler := V1ValidateRokuTransactionValidator(route.Route{})(http.HandlerFunc(h.V1ValidateRokuTransaction))
			handler.ServeHTTP(w, r)
			res := w.Result()
			defer res.Body.Close()
			if res.StatusCode != test.expectedStatusCode {
				t.Errorf("expected %d, but got %d", test.expectedStatusCode, res.StatusCode)
			}
		})
	}
}

func TestV1RokuTransactionWithoutValidator(t *testing.T) {
	s, _ := stats.New("receiptverify", "v1", stats.WithDevMode())
	req, err := http.NewRequest("POST", "/receiptverify/validate-roku-transaction", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	ff, _ := featureflag.MakeFeatureClient("", &config.Config{})
	h := newReceiptVerifyHandler(mocks.NewIAP(t), s, mocks.NewPlanClient(t), mocks.NewCircuitAPI(t), mocks.NewCircuitAPI(t), mocks.NewCircuitAPI(t), mocks.NewCircuitAPI(t), &http.Client{}, map[string]map[string]string{}, "", "", "", "", ff)

	handler := http.HandlerFunc(h.V1ValidateRokuTransaction)
	handler.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusBadRequest, rr.Code, "Should have returned status BadRequest")
}
