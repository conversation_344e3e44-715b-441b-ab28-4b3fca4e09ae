package httprouter

import (
	"fmt"
	"net/http"

	"github.com/foxcorp-product/commerce-receiptverify/iap/receipt"
	"github.com/foxcorp-product/commerce-receiptverify/internal/response"
	"github.com/foxcorp-product/entitlement-sdk/inputvalidator"
	"github.com/foxcorp-product/entitlement-sdk/request"
)

var (
	errPlatformUserIDRequired  = "platformUserId is required"
	errInvoiceIdRequired       = "invoiceId is required"
	receiptNewSamsungStoreFunc = receipt.NewSamsungStore
	newSamsungVerifyResFunc    = NewSamsungVerifyRes
)

var V1ValidateSamsungTransactionValidator = inputvalidator.MakeRouteValidatorMiddleware[V1ValidateSamsungTransactionRequestInput](
	inputvalidator.WithCustomErrorResponse(response.SendErrors),
	inputvalidator.WithRule("AppServiceID", "required", errAppServiceIdRequired),
	inputvalidator.WithRule("InvoiceID", "required", errInvoiceIdRequired),
	inputvalidator.WithRule("PlatformUserID", "required", errPlatformUserIDRequired),
	inputvalidator.WithoutErrorWrap(),
	inputvalidator.WithoutErrorRuleName(),
)

type V1ValidateSamsungTransactionRequestInput struct {
	V1ValidateSamsungTransactionRequest `in:"body=json" validate:"required"`
}

type V1ValidateSamsungTransactionRequest struct {
	AppServiceID   string `json:"appServiceId"`
	InvoiceID      string `json:"invoiceId"`
	PlatformUserID string `json:"platformUserId"`
}

func (h V1Handler) V1ValidateSamsungTransaction(w http.ResponseWriter, r *http.Request) {
	var (
		err error
	)
	l := request.GetFromContext(r.Context()).GetLoggingEntry()

	ctx, span := h.stat.StartMethodSpan(r.Context(), "V1Handler.V1ValidateSamsungTransaction")
	defer func() { span.FinishWithError(err) }()

	in, ok := inputvalidator.GetValidatedStruct[V1ValidateSamsungTransactionRequestInput](r.Context())
	if !ok {
		response.SendErrors(w, http.StatusBadRequest, fmt.Errorf("bad request"))
		return
	}

	if err != nil {
		response.SendErrors(w, http.StatusUnauthorized, err)
		request.GetFromContext(ctx).SetLoggingEntry(l.WithField("err", "samsung verifyPlanFunc:"+err.Error()))
		return
	}

	conf := receipt.SamsungStoreConfig{
		AppServiceID: in.AppServiceID,
		InvoiceID:    in.InvoiceID,
		CustomID:     in.PlatformUserID,
		CountryCode:  "US", // enabling only US-based Samsung users for now
	}
	rs, err := receiptNewSamsungStoreFunc(ctx, h.stat, conf, h.samsungClient)
	if err != nil {
		response.SendErrors(w, http.StatusUnauthorized, err)
		request.GetFromContext(ctx).SetLoggingEntry(l.WithField("err", "receipt.NewSamsungStore:"+err.Error()))
		return
	}

	// check if receipt have unexpired
	rec, err := rs.UnExpired()
	if err != nil {
		response.Send(w, http.StatusUnauthorized, V1VerifyResponse{})
		request.GetFromContext(ctx).SetLoggingEntry(l.WithField("err", "V1ValidateSamsungTransaction receipt.UnExpired:"+err.Error()))
		return
	}

	verifyRes, err := newSamsungVerifyResFunc(rec)
	if err != nil {
		response.SendErrors(w, http.StatusUnauthorized, err)
		request.GetFromContext(ctx).SetLoggingEntry(l.WithField("err", "V1ValidateSamsungTransaction newV1VerifyRes:"+err.Error()))
		return
	}

	response.Send(w, http.StatusOK, verifyRes)
}
