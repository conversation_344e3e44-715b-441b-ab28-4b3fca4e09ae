package httprouter

import (
	"fmt"
	"net/http"

	planclient "github.com/foxcorp-product/commerce-plan/client"
	"github.com/foxcorp-product/commerce-receiptverify/iap/receipt"
	"github.com/foxcorp-product/commerce-receiptverify/internal/response"
	"github.com/foxcorp-product/entitlement-sdk/inputvalidator"
	"github.com/foxcorp-product/entitlement-sdk/request"
)

var (
	errPaymentIntentIdRequired = "paymentIntentId is required"
	receiptNewStripeStoreFunc  = receipt.NewStripeStore
	newStripeVerifyResFunc     = NewStripeVerifyRes
)

var V1ValidateStripeTransactionValidator = inputvalidator.MakeRouteValidatorMiddleware[V1ValidateStripeTransactionRequestInput](
	inputvalidator.WithCustomErrorResponse(response.SendErrors),
	inputvalidator.WithRule("AppServiceID", "required", errAppServiceIdRequired),
	inputvalidator.WithRule("PaymentIntentID", "required", errPaymentIntentIdRequired),
	inputvalidator.WithoutErrorWrap(),
	inputvalidator.WithoutErrorRuleName(),
)

type V1ValidateStripeTransactionRequestInput struct {
	V1ValidateStripeTransactionRequest `in:"body=json" validate:"required"`
}

type V1ValidateStripeTransactionRequest struct {
	AppServiceID    string `json:"appServiceId"`
	PaymentIntentID string `json:"paymentIntentId"`
}

func (h V1Handler) V1ValidateStripeTransaction(w http.ResponseWriter, r *http.Request) {
	var (
		err error
	)

	l := request.GetFromContext(r.Context()).GetLoggingEntry()

	ctx, span := h.stat.StartMethodSpan(r.Context(), "V1Handler.V1ValidateStripeTransaction")
	defer func() { span.FinishWithError(err) }()

	in, ok := inputvalidator.GetValidatedStruct[V1ValidateStripeTransactionRequestInput](r.Context())
	if !ok {
		response.SendErrors(w, http.StatusBadRequest, fmt.Errorf("bad request"))
		return
	}

	_, err = h.planClient.GetPlanByAppServiceID(ctx, planclient.V1GetPlanByAppServiceIDInput{
		AppServiceID: in.AppServiceID,
	})

	if err != nil {
		response.SendErrors(w, http.StatusUnauthorized, err)
		request.GetFromContext(ctx).SetLoggingEntry(l.WithField("err", "stripe verifyPlanFunc:"+err.Error()))
		return
	}

	conf := receipt.StripeStoreConfig{
		AppServiceID:    in.AppServiceID,
		PaymentIntentID: in.PaymentIntentID,
	}
	rs, err := receiptNewStripeStoreFunc(ctx, h.stat, conf, h.stripeClient)
	if err != nil {
		response.SendErrors(w, http.StatusUnauthorized, err)
		request.GetFromContext(ctx).SetLoggingEntry(l.WithField("err", "receipt.NewStripeStore:"+err.Error()))
		return
	}

	// check if receipt have unexpired
	rec, err := rs.UnExpired()
	if err != nil {
		response.Send(w, http.StatusUnauthorized, V1VerifyResponse{})
		request.GetFromContext(ctx).SetLoggingEntry(l.WithField("err", "V1ValidateStripeTransaction receipt.UnExpired:"+err.Error()))
		return
	}

	verifyRes, err := newStripeVerifyResFunc(rec)
	if err != nil {
		response.SendErrors(w, http.StatusUnauthorized, err)
		request.GetFromContext(ctx).SetLoggingEntry(l.WithField("err", "V1ValidateStripeTransaction newV1VerifyRes:"+err.Error()))
		return
	}

	response.Send(w, http.StatusOK, verifyRes)
}
