package httprouter

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	planclient "github.com/foxcorp-product/commerce-plan/client"
	"github.com/foxcorp-product/commerce-receiptverify/config"
	"github.com/foxcorp-product/commerce-receiptverify/iap/receipt"
	"github.com/foxcorp-product/commerce-receiptverify/internal/featureflag"
	"github.com/foxcorp-product/commerce-receiptverify/mocks"
	"github.com/foxcorp-product/entitlement-sdk/apikeys"
	"github.com/foxcorp-product/entitlement-sdk/logger"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/foxcorp-product/entitlement-sdk/route"
	"github.com/foxcorp-product/entitlement-sdk/stats"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stripe/stripe-go/v74"
)

var mockReceiptStripe *receipt.StripeStore

func mockNewStripeVerifyResFunc(recRaw interface{}) (*V1VerifyResponseStripe, error) {
	if rec, ok := recRaw.(receipt.RetrievePaymentIntentResponse); ok && rec.PaymentIntent.ID == "test-should-error" {
		return nil, errors.New("mockNewStripeVerifyResFunc error")
	}
	return &V1VerifyResponseStripe{}, nil
}

func mockReceiptNewStripeStoreFunc(ctx context.Context, stat receipt.Stats, conf receipt.StripeStoreConfig, cb receipt.CircuitAPI) (*receipt.StripeStore, error) {
	if conf.AppServiceID == "test-should-error" {
		return nil, errors.New("mockReceiptNewStripeStoreFunc error")
	}
	return mockReceiptStripe, nil
}

func TestV1StripeTransaction(t *testing.T) {
	// Generate the mocks for dependencies
	svcMock := mocks.NewIAP(t)
	planClientMock := mocks.NewPlanClient(t)
	amazonClientMock := mocks.NewCircuitAPI(t)
	rokuClientMock := mocks.NewCircuitAPI(t)
	samsungClientMock := mocks.NewCircuitAPI(t)
	stripeClientMock := mocks.NewCircuitAPI(t)

	s, _ := stats.New("receiptverify", "v1", stats.WithDevMode())
	l, _ := logger.New()
	tests := map[string]struct {
		body                  V1ValidateStripeTransactionRequestInput
		deps                  V1Handler
		runMocks              func()
		receiptNewStripeStore func(ctx context.Context, stat receipt.Stats, conf receipt.StripeStoreConfig, cb receipt.CircuitAPI) (*receipt.StripeStore, error)
		newStripeVerifyRes    func(recRaw interface{}) (*V1VerifyResponseStripe, error)
		expectedStatusCode    int
	}{
		"failure: should return unauthorized when GetPlanByAppServiceID returns an error": {
			body: V1ValidateStripeTransactionRequestInput{
				V1ValidateStripeTransactionRequest: V1ValidateStripeTransactionRequest{
					AppServiceID:    "test",
					PaymentIntentID: "test",
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{}, errors.New("error")).Once()
			},
			expectedStatusCode: http.StatusUnauthorized,
		},
		"failure: should return unauthorized when receiptNewStripeStore returns an error": {
			body: V1ValidateStripeTransactionRequestInput{
				V1ValidateStripeTransactionRequest: V1ValidateStripeTransactionRequest{
					AppServiceID:    "test-should-error",
					PaymentIntentID: "test",
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{}, nil).Once()
			},
			receiptNewStripeStore: mockReceiptNewStripeStoreFunc,
			expectedStatusCode:    http.StatusUnauthorized,
		},
		"failure: should return unauthorized when unExpired returns an error": {
			body: V1ValidateStripeTransactionRequestInput{
				V1ValidateStripeTransactionRequest: V1ValidateStripeTransactionRequest{
					AppServiceID:    "test-should-ok",
					PaymentIntentID: "test",
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{}, nil).Once()
				mockReceiptStripe = &receipt.StripeStore{}
			},
			receiptNewStripeStore: mockReceiptNewStripeStoreFunc,
			expectedStatusCode:    http.StatusUnauthorized,
		},
		"failure: should return unauthorized when newStripeVerifyRes returns an error": {
			body: V1ValidateStripeTransactionRequestInput{
				V1ValidateStripeTransactionRequest: V1ValidateStripeTransactionRequest{
					AppServiceID:    "test-should-ok",
					PaymentIntentID: "test",
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{}, nil).Once()
				mockReceiptStripe = &receipt.StripeStore{
					Data: receipt.RetrievePaymentIntentResponse{
						PaymentIntent: stripe.PaymentIntent{
							Status: "succeeded",
							ID:     "test-should-error",
						},
					},
				}
			},
			receiptNewStripeStore: mockReceiptNewStripeStoreFunc,
			newStripeVerifyRes:    mockNewStripeVerifyResFunc,
			expectedStatusCode:    http.StatusUnauthorized,
		},
		"success: should return ok when Stripe Transaction is correctly validated": {
			body: V1ValidateStripeTransactionRequestInput{
				V1ValidateStripeTransactionRequest: V1ValidateStripeTransactionRequest{
					AppServiceID:    "test-should-ok",
					PaymentIntentID: "test",
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{}, nil).Once()
				mockReceiptStripe = &receipt.StripeStore{
					Data: receipt.RetrievePaymentIntentResponse{
						PaymentIntent: stripe.PaymentIntent{
							Status: "succeeded",
							ID:     "test-should-ok",
						},
					},
				}
			},
			receiptNewStripeStore: mockReceiptNewStripeStoreFunc,
			newStripeVerifyRes:    mockNewStripeVerifyResFunc,
			expectedStatusCode:    http.StatusOK,
		},
	}

	for name, test := range tests {
		t.Run(name, func(t *testing.T) {
			var b bytes.Buffer
			if err := json.NewEncoder(&b).Encode(test.body); err != nil {
				t.Fatal(err)
			}
			body := io.NopCloser(&b)

			// Define globals
			receiptNewStripeStoreFunc = test.receiptNewStripeStore
			newStripeVerifyResFunc = test.newStripeVerifyRes

			w := httptest.NewRecorder()
			rh, r := request.NewWithRequest(httptest.NewRequest(http.MethodPost, "/receiptverify/validate-stripe-transaction", body), "receiptverify")
			rh.SetAPIKey(apikeys.APIKey{Brand: "test-brand"})
			rh.SetLoggingEntry(l)

			h := newReceiptVerifyHandler(test.deps.svc, s, test.deps.planClient, test.deps.amazonClient, test.deps.rokuClient, test.deps.samsungClient, test.deps.stripeClient, test.deps.googleHttpClient, test.deps.secrets, test.deps.appstoreApiKeyId, test.deps.appstoreApiIssuerId, test.deps.appstoresdkApiKeyId, test.deps.appstoresdkApiIssuerId, test.deps.featureClient)
			if test.runMocks != nil {
				test.runMocks()
			}
			handler := V1ValidateStripeTransactionValidator(route.Route{})(http.HandlerFunc(h.V1ValidateStripeTransaction))
			handler.ServeHTTP(w, r)
			res := w.Result()
			defer res.Body.Close()
			if res.StatusCode != test.expectedStatusCode {
				t.Errorf("expected %d, but got %d", test.expectedStatusCode, res.StatusCode)
			}
		})
	}
}

func TestV1StripeTransactionWithoutValidator(t *testing.T) {
	s, _ := stats.New("receiptverify", "v1", stats.WithDevMode())
	req, err := http.NewRequest("POST", "/receiptverify/validate-stripe-transaction", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	ff, _ := featureflag.MakeFeatureClient("", &config.Config{})
	h := newReceiptVerifyHandler(mocks.NewIAP(t), s, mocks.NewPlanClient(t), mocks.NewCircuitAPI(t), mocks.NewCircuitAPI(t), mocks.NewCircuitAPI(t), mocks.NewCircuitAPI(t), &http.Client{}, map[string]map[string]string{}, "", "", "", "", ff)

	handler := http.HandlerFunc(h.V1ValidateStripeTransaction)
	handler.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusBadRequest, rr.Code, "Should have returned status BadRequest")
}
