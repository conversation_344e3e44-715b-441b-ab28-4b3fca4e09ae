package httprouter

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"time"

	planclient "github.com/foxcorp-product/commerce-plan/client"
	"github.com/foxcorp-product/commerce-receiptverify/iap"
	"github.com/foxcorp-product/commerce-receiptverify/iap/receipt"
	"github.com/foxcorp-product/commerce-receiptverify/internal/featureflag"
	"github.com/foxcorp-product/commerce-receiptverify/internal/response"
	"github.com/foxcorp-product/entitlement-sdk/apikeys"
	"github.com/foxcorp-product/entitlement-sdk/inputvalidator"
	"github.com/foxcorp-product/entitlement-sdk/request"
)

var V1ReceiptVerifyValidator = inputvalidator.MakeRouteValidatorMiddleware[V1ReceiptVerifyRequestInput](
	inputvalidator.WithCustomErrorResponse(response.SendErrors),
	inputvalidator.WithoutErrorWrap(),
)

type IAP interface {
	GetReceipt(ctx context.Context, key apikeys.APIKey, rec iap.ReceiptID) (iap.Receipt, error)
}

type PlanClient interface {
	GetPlanByAppServiceID(ctx context.Context, in planclient.V1GetPlanByAppServiceIDInput) (planclient.Plan, error)
}

type CircuitAPI interface {
	DoWithContext(ctx context.Context, req *http.Request) (resp *http.Response, err error)
	PostWithContext(ctx context.Context, url, contentType string, body io.Reader) (resp *http.Response, err error)
}

type V1Handler struct {
	svc                    IAP
	planClient             PlanClient
	amazonClient           CircuitAPI
	rokuClient             CircuitAPI
	samsungClient          CircuitAPI
	stripeClient           CircuitAPI
	googleHttpClient       *http.Client
	stat                   Stats
	secrets                map[string]map[string]string
	appstoreApiKeyId       string
	appstoreApiIssuerId    string
	appstoresdkApiKeyId    string
	appstoresdkApiIssuerId string
	featureClient          featureflag.FeatureClient
}

func newReceiptVerifyHandler(svc IAP, stat Stats, planClient PlanClient, amazonClient CircuitAPI, rokuClient CircuitAPI, samsungClient CircuitAPI, stripeClient CircuitAPI, googleHttpClient *http.Client, secrets map[string]map[string]string, appstoreApiKeyId string,
	appstoreApiIssuerId string,
	appstoresdkApiKeyId string,
	appstoresdkApiIssuerId string,
	featureflag featureflag.FeatureClient,
) *V1Handler {
	return &V1Handler{
		svc:                    svc,
		stat:                   stat,
		planClient:             planClient,
		amazonClient:           amazonClient,
		rokuClient:             rokuClient,
		samsungClient:          samsungClient,
		stripeClient:           stripeClient,
		googleHttpClient:       googleHttpClient,
		secrets:                secrets,
		appstoreApiKeyId:       appstoreApiKeyId,
		appstoreApiIssuerId:    appstoreApiIssuerId,
		appstoresdkApiKeyId:    appstoresdkApiKeyId,
		appstoresdkApiIssuerId: appstoresdkApiIssuerId,
		featureClient:          featureflag,
	}
}

type V1ReceiptVerifyRequestInput struct {
	V1ReceiptVerifyRequest `in:"body=json" validate:"required"`
}

type V1ReceiptVerifyRequest struct {
	AppServiceID   string `json:"appServiceId"`
	Receipt        string `json:"receipt"`
	PlatformUserID string `json:"platformUserId"`
	AllowExpired   bool   `json:"allowExpired"`
}

func (h V1Handler) V1Verify(w http.ResponseWriter, r *http.Request) {
	var err error

	l := request.GetFromContext(r.Context()).GetLoggingEntry()
	ctx, span := h.stat.StartMethodSpan(r.Context(), "V1Handler.V1Verify")
	defer func() { span.FinishWithError(err) }()

	in, ok := inputvalidator.GetValidatedStruct[V1ReceiptVerifyRequestInput](r.Context())
	if !ok {
		response.SendErrors(w, http.StatusBadRequest, fmt.Errorf("bad request"))
		return
	}

	apikey := request.GetFromContext(ctx).GetAPIKey()

	res, err := h.svc.GetReceipt(ctx, apikey, iap.ReceiptID{
		Rec:            in.Receipt,
		AppServiceID:   in.AppServiceID,
		PlatformUserID: in.PlatformUserID,
	})
	if err != nil {
		if _, ok := err.(*receipt.BadReceiptError); ok {
			response.SendErrors(w, http.StatusBadRequest, err)
		} else {
			response.SendErrors(w, http.StatusUnauthorized, err)
		}
		l.Errorf("err", "iap.GetReceipts:"+err.Error())
		return
	}

	var rec interface{}
	// FIXME: This is a temporary patch to allow QA do testing
	if (in.AllowExpired || h.featureClient.GetUseMockServer(ctx)) &&
		os.Getenv("SERVICE_ENV") != "prod1" {
		rec = res.GetData()
	} else {
		rec, err = res.UnExpired()
	}

	if err != nil {
		l.Errorf("err", "receipt.UnExpired:"+err.Error())
		response.Send(w, http.StatusUnauthorized, V1VerifyResponse{})
		return
	}

	plan, err := h.planClient.GetPlanByAppServiceID(ctx, planclient.V1GetPlanByAppServiceIDInput{
		AppServiceID: in.AppServiceID,
	})
	if err != nil {
		l.Errorf("err", "verifyPlanFunc:"+err.Error())
		response.SendErrors(w, http.StatusUnauthorized, err)
		return
	}

	verifyRes, err := newV1VerifyRes(ctx, plan, rec)
	if err != nil {
		l.Errorf("err", "newV1VerifyRes:"+err.Error())
		response.SendErrors(w, http.StatusUnauthorized, err)
		return
	}

	// FIXME: This is a temporary patch to allow QA do testing
	if in.AllowExpired && os.Getenv("SERVICE_ENV") != "prod1" {
		verifyRes.EndDate = time.Now().AddDate(0, 1, 0).Format(defaultDateFormat)
	}

	store, _ := iap.GetStore(apikey)
	verifyRes.Store = store

	response.Send(w, http.StatusOK, verifyRes)
}
