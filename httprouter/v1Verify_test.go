package httprouter

import (
	"bytes"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	planclient "github.com/foxcorp-product/commerce-plan/client"
	"github.com/foxcorp-product/commerce-receiptverify/config"
	"github.com/foxcorp-product/commerce-receiptverify/iap/receipt"
	"github.com/foxcorp-product/commerce-receiptverify/internal/featureflag"
	"github.com/foxcorp-product/commerce-receiptverify/mocks"
	"github.com/foxcorp-product/entitlement-sdk/apikeys"
	"github.com/foxcorp-product/entitlement-sdk/logger"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/foxcorp-product/entitlement-sdk/route"
	"github.com/foxcorp-product/entitlement-sdk/stats"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestV1Verify(t *testing.T) {
	// Generate the mocks for dependencies
	svcMock := mocks.NewIAP(t)
	planClientMock := mocks.NewPlanClient(t)
	amazonClientMock := mocks.NewCircuitAPI(t)
	rokuClientMock := mocks.NewCircuitAPI(t)
	samsungClientMock := mocks.NewCircuitAPI(t)
	stripeClientMock := mocks.NewCircuitAPI(t)
	featureClientMock, _ := featureflag.MakeFeatureClient("", &config.Config{})

	s, _ := stats.New("receiptverify", "v1", stats.WithDevMode())
	l, _ := logger.New()
	tests := map[string]struct {
		body               V1ReceiptVerifyRequestInput
		deps               V1Handler
		runMocks           func()
		expectedStatusCode int
	}{
		"failure: should return unauthorized when invalid Receipt with Unauthorized response": {
			body: V1ReceiptVerifyRequestInput{
				V1ReceiptVerifyRequest: V1ReceiptVerifyRequest{
					AppServiceID: "test",
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				svcMock.On("GetReceipt", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("internal error")).Once()
			},
			expectedStatusCode: http.StatusUnauthorized,
		},
		"failure: should return bad request when invalid Receipt with Bad response": {
			body: V1ReceiptVerifyRequestInput{
				V1ReceiptVerifyRequest: V1ReceiptVerifyRequest{
					AppServiceID: "test",
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				svcMock.On("GetReceipt", mock.Anything, mock.Anything, mock.Anything).Return(nil, receipt.NewBadReceiptError(errors.New("bad receipt"))).Once()
			},
			expectedStatusCode: http.StatusBadRequest,
		},
		"failure: should return unauthorized when Unexpired returns an error": {
			body: V1ReceiptVerifyRequestInput{
				V1ReceiptVerifyRequest: V1ReceiptVerifyRequest{
					AppServiceID:   "test",
					PlatformUserID: "userid",
					Receipt:        "receipt",
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				featureClient:          featureClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				receipt := mocks.NewReceipt(t)
				svcMock.On("GetReceipt", mock.Anything, mock.Anything, mock.Anything).Return(receipt, nil).Once()
				receipt.On("UnExpired").Return(nil, errors.New("unexpired error")).Once()
			},
			expectedStatusCode: http.StatusUnauthorized,
		},
		"failure: should return unauthorized when plan returns an error": {
			body: V1ReceiptVerifyRequestInput{
				V1ReceiptVerifyRequest: V1ReceiptVerifyRequest{
					AppServiceID:   "test",
					PlatformUserID: "userid",
					Receipt:        "receipt",
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				featureClient:          featureClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				receipt := mocks.NewReceipt(t)
				svcMock.On("GetReceipt", mock.Anything, mock.Anything, mock.Anything).Return(receipt, nil).Once()
				receipt.On("UnExpired").Return("go", nil).Once()
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{}, errors.New("plan error")).Once()
			},
			expectedStatusCode: http.StatusUnauthorized,
		},
		"failure: should return unauthorized when verifyRes returns an error": {
			body: V1ReceiptVerifyRequestInput{
				V1ReceiptVerifyRequest: V1ReceiptVerifyRequest{
					AppServiceID:   "test",
					PlatformUserID: "userid",
					Receipt:        "receipt",
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				featureClient:          featureClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				receipt := mocks.NewReceipt(t)
				svcMock.On("GetReceipt", mock.Anything, mock.Anything, mock.Anything).Return(receipt, nil).Once()
				receipt.On("UnExpired").Return("go", nil).Once()
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(planclient.Plan{}, nil).Once()
			},
			expectedStatusCode: http.StatusUnauthorized,
		},
		"success: should return ok when verified the payload": {
			body: V1ReceiptVerifyRequestInput{
				V1ReceiptVerifyRequest: V1ReceiptVerifyRequest{
					AppServiceID:   "test",
					PlatformUserID: "userid",
					Receipt:        "receipt",
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				featureClient:          featureClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				receiptMock := mocks.NewReceipt(t)
				svcMock.On("GetReceipt", mock.Anything, mock.Anything, mock.Anything).Return(receiptMock, nil).Once()
				receiptMock.On("UnExpired").Return(receipt.AmazonResponse{
					ReceiptID:          "receipt-id",
					ProductType:        "product-type",
					ProductID:          "product-id",
					PurchaseDate:       10001,
					RenewalDate:        10002,
					FreeTrialEndDate:   10003,
					GracePeriodEndDate: 10004,
					CancelDate:         10005,
					TestTransaction:    true,
					BetaProduct:        true,
					ParentProductID:    "parent-product-id",
					Quantity:           1,
					Term:               "1 Month",
					TermSku:            "term-sku",
				}, nil).Once()
				plan := planclient.Plan{
					AppID:        "plan-appid",
					AppServiceID: "appServiceId",
					Price: []planclient.Price{
						{
							RetailPrice:  1.99,
							CurrencyCode: "USD",
						},
					},
				}
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(plan, nil).Once()
			},
			expectedStatusCode: http.StatusOK,
		},
		"success: should return ok when verified the payload with AllowExpired": {
			body: V1ReceiptVerifyRequestInput{
				V1ReceiptVerifyRequest: V1ReceiptVerifyRequest{
					AppServiceID:   "test",
					PlatformUserID: "userid",
					Receipt:        "receipt",
					AllowExpired:   true,
				},
			},
			deps: V1Handler{
				svc:                    svcMock,
				planClient:             planClientMock,
				amazonClient:           amazonClientMock,
				rokuClient:             rokuClientMock,
				samsungClient:          samsungClientMock,
				stripeClient:           stripeClientMock,
				stat:                   s,
				secrets:                map[string]map[string]string{},
				appstoreApiKeyId:       "test",
				appstoreApiIssuerId:    "test",
				appstoresdkApiKeyId:    "test",
				appstoresdkApiIssuerId: "test",
			},
			runMocks: func() {
				receiptMock := mocks.NewReceipt(t)
				svcMock.On("GetReceipt", mock.Anything, mock.Anything, mock.Anything).Return(receiptMock, nil).Once()
				receiptMock.On("GetData").Return(receipt.AmazonResponse{
					ReceiptID:          "receipt-id",
					ProductType:        "product-type",
					ProductID:          "product-id",
					PurchaseDate:       10001,
					RenewalDate:        10002,
					FreeTrialEndDate:   10003,
					GracePeriodEndDate: 10004,
					CancelDate:         10005,
					TestTransaction:    true,
					BetaProduct:        true,
					ParentProductID:    "parent-product-id",
					Quantity:           1,
					Term:               "1 Month",
					TermSku:            "term-sku",
				}, nil).Once()
				plan := planclient.Plan{
					AppID:        "plan-appid",
					AppServiceID: "appServiceId",
					Price: []planclient.Price{
						{
							RetailPrice:  1.99,
							CurrencyCode: "USD",
						},
					},
				}
				planClientMock.On("GetPlanByAppServiceID", mock.Anything, mock.Anything).Return(plan, nil).Once()
			},
			expectedStatusCode: http.StatusOK,
		},
	}

	for name, test := range tests {
		t.Run(name, func(t *testing.T) {
			var b bytes.Buffer
			if err := json.NewEncoder(&b).Encode(test.body); err != nil {
				t.Fatal(err)
			}
			body := io.NopCloser(&b)

			w := httptest.NewRecorder()
			rh, r := request.NewWithRequest(httptest.NewRequest(http.MethodPost, "/receiptverify", body), "receiptverify")
			rh.SetAPIKey(apikeys.APIKey{
				APIKey:   "test-key",
				Platform: "amazon_fire_tv",
			})
			rh.SetLoggingEntry(l)

			h := newReceiptVerifyHandler(test.deps.svc, s, test.deps.planClient, test.deps.amazonClient, test.deps.rokuClient, test.deps.samsungClient, test.deps.stripeClient, test.deps.googleHttpClient, test.deps.secrets, test.deps.appstoreApiKeyId, test.deps.appstoreApiIssuerId, test.deps.appstoresdkApiKeyId, test.deps.appstoresdkApiIssuerId, test.deps.featureClient)
			if test.runMocks != nil {
				test.runMocks()
			}
			handler := V1ReceiptVerifyValidator(route.Route{})(http.HandlerFunc(h.V1Verify))
			handler.ServeHTTP(w, r)
			res := w.Result()
			defer res.Body.Close()
			assert.Equal(t, test.expectedStatusCode, res.StatusCode, w.Body.String())
		})
	}
}

func TestV1VerifyWithoutInputValidator(t *testing.T) {
	s, _ := stats.New("receiptverify", "v1", stats.WithDevMode())
	req, err := http.NewRequest("POST", "/receiptverify", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	ff, _ := featureflag.MakeFeatureClient("", &config.Config{})
	h := newReceiptVerifyHandler(mocks.NewIAP(t), s, mocks.NewPlanClient(t), mocks.NewCircuitAPI(t), mocks.NewCircuitAPI(t), mocks.NewCircuitAPI(t), mocks.NewCircuitAPI(t), &http.Client{}, map[string]map[string]string{}, "", "", "", "", ff)

	handler := http.HandlerFunc(h.V1Verify)
	handler.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusBadRequest, rr.Code, "Should have returned status BadRequest")
}
