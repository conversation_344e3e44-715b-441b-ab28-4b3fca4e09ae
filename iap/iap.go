package iap

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"

	planclient "github.com/foxcorp-product/commerce-plan/client"
	"github.com/foxcorp-product/commerce-receiptverify/iap/receipt"
	"github.com/foxcorp-product/entitlement-sdk/apikeys"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/foxcorp-product/entitlement-sdk/stats"
)

// Receipt is a contract that needs to be implemented by an IAP receipt
type Receipt interface {
	UnExpired() (interface{}, error)
	GetData() interface{}
}

// ReceiptID contains receipt data to identify receipt
type ReceiptID struct {
	Rec            string
	AppServiceID   string
	PlatformUserID string
}

type CircuitAPI interface {
	DoWithContext(ctx context.Context, req *http.Request) (resp *http.Response, err error)
	PostWithContext(ctx context.Context, url, contentType string, body io.Reader) (resp *http.Response, err error)
}

type PlanClient interface {
	GetPlanByAppServiceID(ctx context.Context, in planclient.V1GetPlanByAppServiceIDInput) (planclient.Plan, error)
}

// IAP represents the IAP service behavior
type IAP interface {
	GetReceipt(ctx context.Context, key apikeys.APIKey, rec ReceiptID) (Receipt, error)
}

// foxIAP implements IAP interface with the default business logic
type foxIAP struct {
	secrets           map[string]map[string]string
	stats             *stats.StandardStats
	planClient        PlanClient
	rokuClient        CircuitAPI
	amazonClient      CircuitAPI
	samsungClient     CircuitAPI
	stripeProxyClient CircuitAPI
	googleHTTPClient  *http.Client
}

// New is the constructor of foxIAP
func New(stat *stats.StandardStats, s map[string]map[string]string, planClient PlanClient, rokuClient CircuitAPI, amazonClient CircuitAPI, samsungProxyClient CircuitAPI, stripeProxyClient CircuitAPI, googleHTTPClient *http.Client) IAP {
	return &foxIAP{
		secrets:           s,
		stats:             stat,
		planClient:        planClient,
		rokuClient:        rokuClient,
		amazonClient:      amazonClient,
		samsungClient:     samsungProxyClient,
		stripeProxyClient: stripeProxyClient,
		googleHTTPClient:  googleHTTPClient,
	}
}

// no secret required for stripe, we are using the proxy
func (f *foxIAP) getSecretByStore(store Store, app string) (string, error) {
	if store == StripeStore || store == SamsungStore {
		return "", nil
	}

	secret, ok := f.secrets[string(store)][app]
	if !ok {
		return "", fmt.Errorf("secret not configured for store=%s app=%s", store, app)
	}

	return secret, nil
}

// GetReceipt returns the user's receipt for the current store (store is detected by API Key)
func (f *foxIAP) GetReceipt(ctx context.Context, key apikeys.APIKey, rec ReceiptID) (Receipt, error) {
	// get store
	l := request.GetFromContext(ctx).GetLoggingEntry()
	s, err := GetStore(key)
	if err != nil {
		l.Errorf("GetReceipt.GetStore err: %v", err.Error())
		return nil, receipt.NewBadReceiptError(err)
	}
	request.GetFromContext(ctx).SetLoggingEntry(l.WithField("store", s))
	l = request.GetFromContext(ctx).GetLoggingEntry()

	// get secret
	secret, err := f.getSecretByStore(s, key.Brand)
	if err != nil {
		l.Errorf("GetReceipt.getSecretByStore err: %v", err.Error())
		return nil, receipt.NewBadReceiptError(err)
	}

	switch s {
	case AppStore:
		conf := receipt.AppStoreConfig{
			ReceiptData:  rec.Rec,
			AppServiceID: rec.AppServiceID,
		}

		r, err := receipt.NewAppStore(ctx, f.stats, secret, conf)
		if err == nil { // no error with standard key, return normally
			return r, nil
		}

		// reached here if verification error, try once more with the dev secret
		secret, _ := f.getSecretByStore(AppleDev, key.Brand)
		if secret == "" {
			return nil, err // return original err
		}

		r, devErr := receipt.NewAppStore(ctx, f.stats, secret, conf)
		if devErr != nil {
			l.Errorf("GetReceipt.NewAppStore err: %v", err.Error())
			return nil, err // return original err
		}

		return r, nil
	case RokuStore:
		conf := receipt.RokuStoreConfig{
			ReceiptID: rec.Rec,
		}

		r, err := receipt.NewRokuStore(ctx, f.stats, secret, conf, f.rokuClient)
		if err != nil {
			l.Errorf("GetReceipt.NewRokuStore err: %v", err.Error())
			return nil, err
		}

		return r, nil
	case PlayStore:
		payload := receipt.PlayStorePayload{
			Encrypted: rec.Rec,
		}
		var r *receipt.PlayStore
		r, err = receipt.NewPlayStore(ctx, f.stats, secret, payload, f.planClient, f.googleHTTPClient)
		if err != nil {
			l.Errorf("GetReceipt.NewPlayStore err: %v", err.Error())
			return nil, err
		}

		return r, nil
	case AmazonStore:
		conf := receipt.AmazonConfig{
			ReceiptID:    rec.Rec,
			UserID:       rec.PlatformUserID,
			AppServiceID: rec.AppServiceID,
		}
		r, err := receipt.NewAmazon(ctx, f.stats, secret, conf, f.amazonClient)
		if err != nil {
			l.Errorf("GetReceipt.NewAmazon err: %v", err.Error())
			return nil, err
		}
		return r, nil
	case StripeStore:
		conf := receipt.StripeStoreConfig{
			PaymentIntentID: rec.Rec,
			AppServiceID:    rec.AppServiceID,
		}
		r, err := receipt.NewStripeStore(ctx, f.stats, conf, f.stripeProxyClient)
		if err != nil {
			l.Errorf("GetReceipt.NewStripeStore err: %v", err.Error())
			return nil, err
		}
		return r, nil
	case SamsungStore:
		conf := receipt.SamsungStoreConfig{
			AppServiceID: rec.AppServiceID,
			InvoiceID:    rec.Rec,
			CustomID:     rec.PlatformUserID,
			CountryCode:  "US", // enabling only US-based Samsung users for now
		}
		r, err := receipt.NewSamsungStore(ctx, f.stats, conf, f.samsungClient)
		if err != nil {
			l.Errorf("GetReceipt.NewSamsungStore err: %v", err.Error())
			return nil, err
		}
		return r, nil

	default:
		return nil, errors.New("invalid store")
	}
}
