package receipt

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/awa/go-iap/amazon"
	planclient "github.com/foxcorp-product/commerce-plan/client"
	"github.com/foxcorp-product/entitlement-sdk/logger"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/foxcorp-product/entitlement-sdk/stats"
)

const AmazonUrl string = "https://appstore-sdk.amazon.com"

// The AmazonResponse type has the response properties
type AmazonResponse struct {
	ReceiptID          string             `json:"receiptId"`
	ProductType        string             `json:"productType"`
	ProductID          string             `json:"productId"`
	PurchaseDate       int64              `json:"purchaseDate"`
	AutoRenewing       bool               `json:"autoRenewing"`
	RenewalDate        int64              `json:"renewalDate"`
	FreeTrialEndDate   int64              `json:"freeTrialEndDate"`
	GracePeriodEndDate int64              `json:"gracePeriodEndDate"`
	CancelDate         int64              `json:"cancelDate"`
	CancelReason       int64              `json:"cancelReason"`
	TestTransaction    bool               `json:"testTransaction"`
	BetaProduct        bool               `json:"betaProduct"`
	ParentProductID    string             `json:"parentProductId"`
	Quantity           int64              `json:"quantity"`
	Term               string             `json:"term"`
	TermSku            string             `json:"termSku"`
	Promotions         []amazon.Promotion `json:"promotions"`
	ActivePromotion    bool               `json:"activePromotion"` //used to know whether the user is currently in a promotion
	Charged            *V1Charged         `json:"charged,omitempty"`
}

type V1Charged struct {
	Amount   float64    `json:"amount"`
	Currency string     `json:"currency"`
	Date     *time.Time `json:"date"`
}

func (r *AmazonResponse) HasActivePromotion() bool {
	if len(r.Promotions) == 0 {
		return false
	}

	if time.Now().Before(time.UnixMilli(r.FreeTrialEndDate).UTC()) { //user's in free trial; promotions don't apply at this moment
		return false
	}

	for _, p := range r.Promotions {
		if p.PromotionStatus == amazon.InProgress {
			return true
		}
	}
	return false
}

func (r *AmazonResponse) CalculateChargedAmount(plan planclient.Plan) error {
	if len(plan.Price) == 0 {
		return fmt.Errorf("plan %q has no price", plan.AppServiceID)
	}
	// we can only use the first price
	price := plan.Price[0]
	// some useful time variables
	today := TruncateToDay(time.Now())
	// default to the retail price
	r.Charged = &V1Charged{
		Amount:   price.RetailPrice,
		Currency: price.CurrencyCode,
		Date:     &today,
	}

	FreeTrial := time.Now().Before(time.UnixMilli(r.FreeTrialEndDate).UTC())
	if FreeTrial {
		r.Charged.Amount = 0
	}

	// if there is no intro offer, we can return
	if price.IntroOffer == nil {
		return nil
	}

	if r.HasActivePromotion() {
		r.Charged.Amount = price.IntroOffer.Price
		return nil
	}

	return nil
}

func (r *AmazonResponse) GetPurchaseDate() time.Time {
	return parseAmazonStoreDate(r.PurchaseDate)
}

func (r *AmazonResponse) GetExpirationDate() time.Time {
	return parseAmazonStoreDate(r.RenewalDate)
}

func parseAmazonStoreDate(t int64) time.Time {
	if t == 0 {
		return time.Now()
	}
	return time.Unix(0, t*int64(time.Millisecond))
}

// The IAPResponseError typs has error message and status.
type IAPResponseError struct {
	Message string `json:"message"`
	Status  bool   `json:"status"`
}

// AmazonConfig ... Contains the Data we need to pass to Amazon Client
type AmazonConfig struct {
	ReceiptID    string `json:"receiptID"`
	UserID       string `json:"userID"`
	AppServiceID string `json:"appServiceID"`
}

// validate ... Contains the Data we need to pass to Amazon Client
func (ac *AmazonConfig) validate() error {
	if ac.ReceiptID == "" {
		return errors.New("receiptID is required for AmazonStore Receipt")
	}

	if ac.UserID == "" {
		return errors.New("userID is required for AmazonStore Receipt")
	}

	if ac.AppServiceID == "" {
		return errors.New("appServiceID is required for AmazonStore Receipt")
	}

	return nil
}

// Amazon ... Struct that defines the Data from Receipt and attahes the Logger
type AmazonStore struct {
	Data   AmazonResponse
	log    logger.Logger
	cb     CircuitAPI
	stats  Stats
	secret string
}

// NewAmazon creates a Receipt
func NewAmazon(ctx context.Context, stat Stats, secret string, conf AmazonConfig, cb CircuitAPI) (*AmazonStore, error) {
	if err := conf.validate(); err != nil {
		return nil, NewBadReceiptError(err)
	}

	r := AmazonStore{
		log:    request.GetFromContext(ctx).GetLoggingEntry(),
		secret: secret,
		stats:  stat,
		cb:     cb,
	}

	// try origin
	err := r.download(ctx, conf)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// UnExpired will check to see if the receipt is still valid
// @link https://developer.amazon.com/docs/in-app-purchasing/iap-rvs-for-android-apps.html#cancel-date-and-renewal-date
func (r *AmazonStore) UnExpired() (interface{}, error) {
	// check if expired
	if r.Data.CancelDate != 0 {
		return nil, fmt.Errorf("transaction was cancelled or had expired on %d", r.Data.CancelDate)
	}

	return r.Data, nil
}

func (r *AmazonStore) GetData() interface{} {
	r.Data.ActivePromotion = r.Data.HasActivePromotion()
	return r.Data
}

// Expires ... Checks for the renewal date that we receive from Amazon appStore
func (r *AmazonStore) Expires() int64 {
	return r.Data.RenewalDate
}

// download downloads the receipt data from origin
func (r *AmazonStore) download(ctx context.Context, conf AmazonConfig) error {
	var err error
	var span stats.Span

	_, span = r.stats.StartMethodSpan(ctx, "amazon.Download")
	defer func() { span.FinishWithError(err) }()

	client := amazon.New(r.secret)
	client.URL = amazon.ProductionURL
	// we changed client.Verify(ctx, conf.UserID, conf.ReceiptID) to get grace period from amazon
	res, err := r.verify(ctx, conf.UserID, conf.ReceiptID)
	if err != nil {
		return err
	}

	r.Data = res
	return nil
}

func (r *AmazonStore) verify(ctx context.Context, userID, receiptID string) (AmazonResponse, error) {
	l := request.GetFromContext(ctx).GetLoggingEntry()

	result := AmazonResponse{}
	url := fmt.Sprintf("%v/version/1.0/verifyReceiptId/developer/%v/user/%v/receiptId/%v", AmazonUrl, r.secret, userID, receiptID)
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return result, err
	}

	resp, err := r.cb.DoWithContext(ctx, req)
	if err != nil {
		return result, err
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		responseError := IAPResponseError{}
		err = json.NewDecoder(resp.Body).Decode(&responseError)
		if err != nil {
			return result, err
		}
		return result, errors.New(responseError.Message)
	}
	err = json.NewDecoder(resp.Body).Decode(&result)
	l.Debugf("amazon verify response: %+v", result)
	return result, err
}
