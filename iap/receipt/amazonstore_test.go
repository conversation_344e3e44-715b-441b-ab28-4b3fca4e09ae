package receipt

import (
	"github.com/awa/go-iap/amazon"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	planclient "github.com/foxcorp-product/commerce-plan/client"
)

func TestCalculateChargedAmount(t *testing.T) {
	tests := map[string]struct {
		amazonResp     AmazonResponse
		plan           planclient.Plan
		expectedError  bool
		expectedAmount float64
	}{
		"No price in plan": {
			amazonResp: AmazonResponse{
				PurchaseDate: time.Now().AddDate(0, -1, 0).UnixMilli(),
				RenewalDate:  time.Now().AddDate(0, 1, 0).UnixMilli(),
				Term:         "1 month",
			},
			plan: planclient.Plan{
				AppServiceID: "test-service",
				Price:        []planclient.Price{},
			},
			expectedError: true,
		},
		"Standard price": {
			amazonResp: AmazonResponse{
				PurchaseDate: time.Now().AddDate(0, -1, 0).UnixMilli(),
				RenewalDate:  time.Now().AddDate(0, 1, 0).UnixMilli(),
				Term:         "1 month",
			},
			plan: planclient.Plan{
				AppServiceID: "test-service",
				Price: []planclient.Price{
					{
						RetailPrice:  9.99,
						CurrencyCode: "USD",
					},
				},
			},
			expectedError:  false,
			expectedAmount: 9.99,
		},
		"Introductory offer": {
			amazonResp: AmazonResponse{
				PurchaseDate: time.Now().AddDate(0, 0, -7).UnixMilli(),
				RenewalDate:  time.Now().AddDate(0, 0, 21).UnixMilli(),
				Term:         "1 month",
				Promotions: []amazon.Promotion{
					{
						PromotionStatus: amazon.InProgress,
						PromotionType:   amazon.IntroductoryPrice,
					},
				},
			},
			plan: planclient.Plan{
				AppServiceID: "test-service",
				Price: []planclient.Price{
					{
						RetailPrice:  9.99,
						CurrencyCode: "USD",
						IntroOffer: &planclient.IntroOffer{
							Price: 4.99,
							Duration: planclient.Duration{
								Count: 1,
								Unit:  "month",
							},
						},
					},
				},
			},
			expectedError:  false,
			expectedAmount: 4.99,
		},
		"One-time introductory offer": {
			amazonResp: AmazonResponse{
				PurchaseDate: time.Now().AddDate(0, -2, 0).UnixMilli(),
				RenewalDate:  time.Now().AddDate(0, 1, 0).UnixMilli(),
				Term:         "1 month",
			},
			plan: planclient.Plan{
				AppServiceID: "test-service",
				Price: []planclient.Price{
					{
						RetailPrice:  9.99,
						CurrencyCode: "USD",
						IntroOffer: &planclient.IntroOffer{
							Price: 4.99,
							Duration: planclient.Duration{
								Count: 1,
								Unit:  "month",
							},
							OneTime: true,
						},
					},
				},
			},
			expectedError:  false,
			expectedAmount: 9.99,
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			err := tt.amazonResp.CalculateChargedAmount(tt.plan)
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedAmount, tt.amazonResp.Charged.Amount)
			}
		})
	}
}

func TestAmazonResponse_CalculateChargedAmount(t *testing.T) {
	t.Run("plan does not have intro offer and receipt has free trial", func(t *testing.T) {
		r := &AmazonResponse{
			ReceiptID:        "qa3hSoWO0zRowTQNcQ0XaoZiZzjjI5Jq8E3rhTxWOfkPtYBUMetybLz6Wx6KSoGyNBprOguO8jeaeuG1zUmkIIZe7RgM3Q6fSOZd",
			ProductType:      "SUBSCRIPTION",
			ProductID:        "5GWr8I8v7e",
			PurchaseDate:     1746637941979,
			TestTransaction:  true,
			Quantity:         1,
			Term:             "1 Month",
			TermSku:          "com.amazon.subs1_term",
			RenewalDate:      5747242741979,
			FreeTrialEndDate: 5747242741979,
		}
		p := planclient.Plan{
			OneTime:      false,
			FreeTrial:    0,
			AppServiceID: "5GWr8I8v7e",
			AppFamily:    "testAppFamily",
			Price: []planclient.Price{
				{
					RetailPrice:  5.99,
					CurrencyCode: "USD",
				},
			},
		}
		err := r.CalculateChargedAmount(p)
		assert.NoError(t, err)
		assert.Equal(t, 0.00, r.Charged.Amount)
	})

	t.Run("plan have intro offer and receipt has free trial", func(t *testing.T) {
		r := &AmazonResponse{
			ReceiptID:        "qa3hSoWO0zRowTQNcQ0XaoZiZzjjI5Jq8E3rhTxWOfkPtYBUMetybLz6Wx6KSoGyNBprOguO8jeaeuG1zUmkIIZe7RgM3Q6fSOZd",
			ProductType:      "SUBSCRIPTION",
			ProductID:        "5GWr8I8v7e",
			PurchaseDate:     1746637941979,
			TestTransaction:  true,
			Quantity:         1,
			Term:             "1 Month",
			TermSku:          "com.amazon.subs1_term",
			RenewalDate:      5747242741979,
			FreeTrialEndDate: 5747242741979,
		}
		p := planclient.Plan{
			OneTime:      false,
			FreeTrial:    0,
			AppServiceID: "5GWr8I8v7e",
			AppFamily:    "testAppFamily",
			Price: []planclient.Price{
				{
					RetailPrice:  5.99,
					CurrencyCode: "USD",
					IntroOffer: &planclient.IntroOffer{
						Price: 2.99,
						Duration: planclient.Duration{
							Count: 1,
							Unit:  "month",
						},
					},
				},
			},
		}
		err := r.CalculateChargedAmount(p)
		assert.NoError(t, err)
		assert.Equal(t, 0.00, r.Charged.Amount)
	})

	t.Run("plan has intro offer but receipt does not have active promotion", func(t *testing.T) {
		r := &AmazonResponse{
			ReceiptID:       "qa3hSoWO0zRowTQNcQ0XaoZiZzjjI5Jq8E3rhTxWOfkPtYBUMetybLz6Wx6KSoGyNBprOguO8jeaeuG1zUmkIIZe7RgM3Q6fSOZd",
			ProductType:     "SUBSCRIPTION",
			ProductID:       "5GWr8I8v7e",
			PurchaseDate:    1738262973000,
			TestTransaction: true,
			Quantity:        1,
			Term:            "1 Month",
			TermSku:         "com.amazon.subs1_term",
		}
		err := r.CalculateChargedAmount(planclient.Plan{
			OneTime:      false,
			FreeTrial:    0,
			AppServiceID: "5GWr8I8v7e",
			AppFamily:    "testAppFamily",
			Price: []planclient.Price{
				{
					RetailPrice:  5.99,
					CurrencyCode: "USD",
					IntroOffer: &planclient.IntroOffer{
						Price: 2.99,
						Duration: planclient.Duration{
							Count: 1,
							Unit:  "month",
						},
					},
				},
			},
		})
		assert.NoError(t, err)
		assert.Equal(t, 5.99, r.Charged.Amount)
	})

	t.Run("plan has intro offer and receipt has active promotion", func(t *testing.T) {
		r := &AmazonResponse{
			ReceiptID:       "qa3hSoWO0zRowTQNcQ0XaoZiZzjjI5Jq8E3rhTxWOfkPtYBUMetybLz6Wx6KSoGyNBprOguO8jeaeuG1zUmkIIZe7RgM3Q6fSOZd",
			ProductType:     "SUBSCRIPTION",
			ProductID:       "5GWr8I8v7e",
			PurchaseDate:    time.Now().UnixMilli(),
			TestTransaction: true,
			Quantity:        1,
			Term:            "1 Month",
			TermSku:         "com.amazon.subs1_term",
			Promotions: []amazon.Promotion{
				{
					PromotionStatus: amazon.InProgress,
					PromotionType:   amazon.IntroductoryPrice,
				},
			},
		}
		err := r.CalculateChargedAmount(planclient.Plan{
			OneTime:      false,
			FreeTrial:    0,
			AppServiceID: "5GWr8I8v7e",
			AppFamily:    "testAppFamily",
			Price: []planclient.Price{
				{
					RetailPrice:  5.99,
					CurrencyCode: "USD",
					IntroOffer: &planclient.IntroOffer{
						Price: 2.99,
						Duration: planclient.Duration{
							Count: 1,
							Unit:  "month",
						},
					},
				},
			},
		})
		assert.NoError(t, err)
		assert.Equal(t, 2.99, r.Charged.Amount)
	})

	t.Run("plan has intro offer and receipt has completed promotion", func(t *testing.T) {
		r := &AmazonResponse{
			ReceiptID:       "qa3hSoWO0zRowTQNcQ0XaoZiZzjjI5Jq8E3rhTxWOfkPtYBUMetybLz6Wx6KSoGyNBprOguO8jeaeuG1zUmkIIZe7RgM3Q6fSOZd",
			ProductType:     "SUBSCRIPTION",
			ProductID:       "5GWr8I8v7e",
			PurchaseDate:    1738267067000,
			TestTransaction: true,
			Quantity:        1,
			Term:            "1 Day",
			TermSku:         "com.amazon.subs1_term",
			Promotions: []amazon.Promotion{
				{
					PromotionStatus: amazon.Completed,
					PromotionType:   amazon.IntroductoryPrice,
				},
			},
		}
		err := r.CalculateChargedAmount(planclient.Plan{
			OneTime:      false,
			FreeTrial:    0,
			AppServiceID: "5GWr8I8v7e",
			AppFamily:    "testAppFamily",
			Price: []planclient.Price{
				{
					RetailPrice:  5.99,
					CurrencyCode: "USD",
					IntroOffer: &planclient.IntroOffer{
						Price: 2.99,
						Duration: planclient.Duration{
							Count: 1,
							Unit:  "month",
						},
					},
				},
			},
		})
		assert.NoError(t, err)
		assert.Equal(t, 5.99, r.Charged.Amount)
	})
}
