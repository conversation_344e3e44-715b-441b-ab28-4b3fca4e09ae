package receipt

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/foxcorp-product/entitlement-sdk/logger"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/foxcorp-product/entitlement-sdk/stats"
	"google.golang.org/appengine/log"

	"github.com/awa/go-iap/appstore"
	"github.com/awa/go-iap/appstore/api"
	"github.com/golang-jwt/jwt/v4"
)

const (
	statusActive = 0
	extraHrMs    = 7200000
)

var AppleRootCADer []byte

type AppStoreConfig struct {
	ReceiptData  string `json:"receiptData"`
	AppServiceID string `json:"appServiceID"`
}

func (ac *AppStoreConfig) validate() error {
	if ac.ReceiptData == "" {
		return errors.New("receiptData is required")
	}

	if ac.AppServiceID == "" {
		return errors.New("appServiceID is required")
	}

	return nil
}

type AppStoreSVC struct {
	Data         appstore.IAPResponse
	AppServiceID string
	log          logger.Logger
	stats        Stats
	secret       string
}

// NewAppStore creates a Receipt
func NewAppStore(ctx context.Context, stat Stats, secret string, conf AppStoreConfig) (*AppStoreSVC, error) {
	if err := conf.validate(); err != nil {
		return nil, NewBadReceiptError(err)
	}

	r := AppStoreSVC{
		AppServiceID: conf.AppServiceID,
		log:          request.GetFromContext(ctx).GetLoggingEntry(),
		secret:       secret,
		stats:        stat,
	}

	// try origin
	err := r.download(ctx, conf)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

func NewAppStoreTransactionHistory(ctx context.Context, bundleId, transactionId, appStoreApiKeyId, appStoreIssId, appStoreApiKey string) ([]*api.JWSTransaction, error) {
	log := request.GetFromContext(ctx).GetLoggingEntry()

	// try prod first
	c := &api.StoreConfig{
		KeyContent: []byte(appStoreApiKey),
		KeyID:      appStoreApiKeyId,
		BundleID:   bundleId,
		Issuer:     appStoreIssId,
		Sandbox:    false,
	}

	transactions, err := getAppStoreHistoryParsedTransactions(ctx, c, transactionId)
	if err == nil {
		return transactions, nil
	}

	log.Infof("getAppStoreHistoryParsedTransactions error: %v, trying sandbox for txId: %v", err.Error(), transactionId)

	// reached here on error, try again in sandbox
	c.Sandbox = true
	transactions, sandboxErr := getAppStoreHistoryParsedTransactions(ctx, c, transactionId)
	if sandboxErr == nil {
		return transactions, nil
	}

	// errors in both prod and sandbox, return prod error
	log.Errorf("error retrieving appstore transaction history for bundleId: %v, txId: %v, error1: %v, error2: %v", bundleId, transactionId, err.Error(), sandboxErr.Error())
	return nil, err
}

func getAppStoreHistoryParsedTransactions(ctx context.Context, c *api.StoreConfig, transactionId string) ([]*api.JWSTransaction, error) {
	log := request.GetFromContext(ctx).GetLoggingEntry()
	start := time.Now().UnixMilli()

	a := api.NewStoreClient(c)
	responses, err := a.GetTransactionHistory(ctx, transactionId, nil)
	log.Infof("a.GetTransactionHistory completed in %v millis", time.Now().UnixMilli()-start)
	if err != nil {
		log.Errorf("a.GetTransactionHistory error: %v, txId: %v", err.Error(), transactionId)
		return nil, err
	}

	allPayloads := []*api.JWSTransaction{}

	// collect all paginated request transactions into a single slice
	for _, response := range responses {
		// payloads, err := a.ParseSignedTransactions(response.SignedTransactions)
		payloads, err := parseUnverifiedTransactions(response.SignedTransactions)
		if err != nil {
			log.Errorf("parseUnverifiedTransactions error: %v, txId: %v", err.Error(), transactionId)
			return nil, err
		}
		allPayloads = append(allPayloads, payloads...)
	}

	log.Infof("getAppStoreHistoryParsedTransactions retrieved %v transactions in %v millis", len(allPayloads), time.Now().UnixMilli()-start)

	return allPayloads, nil
}

// extract payloads without validating signature, only use if source of JWS transactions are absolutely known
func parseUnverifiedTransactions(transactions []string) ([]*api.JWSTransaction, error) {
	payloads := []*api.JWSTransaction{}
	p := jwt.Parser{}
	for _, tx := range transactions {
		payload := api.JWSTransaction{}
		_, _, err := p.ParseUnverified(tx, &payload)
		if err != nil {
			return nil, err
		}
		// fmt.Printf("---raw tx ----\n%+v\n", tx)
		// fmt.Printf("---parsed ----\n%+v\n", payload)
		payloads = append(payloads, &payload)
	}
	return payloads, nil
}

func ValidateAppStoreTransaction(signedTransaction string, useMock bool) (transaction *api.JWSTransaction, err error) {
	if signedTransaction == "" {
		return nil, errors.New("signed transaction is empty")
	}

	defer func() {
		if r := recover(); r != nil {
			err = errors.New("invalid signedTransaction")
		}
	}()

	//if you use mock, we skip the apple transaction authenticity validation
	if useMock {
		transaction = &api.JWSTransaction{}
		_, err = jwt.ParseWithClaims(signedTransaction, transaction, nil)
		if err != nil && err.Error() != "no Keyfunc was provided." {
			return nil, err
		}
		return transaction, nil
	}
	// get the root cert from apple website
	c := api.StoreConfig{}
	a := api.NewStoreClient(&c)
	// parse the signed transaction
	transaction, err = a.ParseSignedTransaction(signedTransaction)
	if err != nil {
		return
	}
	// verify independently if apple root cert is correct for transaction, it was also verified by awa library
	err = verifyAppleRootCert(signedTransaction)
	if err != nil {
		return
	}

	return
}

// compare if root cert in JWS x5c header is same as the root cert from apple website
func verifyAppleRootCert(signedTransaction string) error {
	tokenArr := strings.Split(signedTransaction, ".")
	headerByte, err := base64.RawStdEncoding.DecodeString(tokenArr[0])
	if err != nil {
		fmt.Printf("header:[%v]\n", string(tokenArr[0]))
		return err
	}

	type Header struct {
		Alg string   `json:"alg"`
		X5c []string `json:"x5c"`
	}
	var header Header
	err = json.Unmarshal(headerByte, &header)
	if err != nil {
		return err
	}

	certByte, err := base64.StdEncoding.DecodeString(header.X5c[2])
	if err != nil {
		return err
	}

	if !bytes.Equal(AppleRootCADer, certByte) {
		return errors.New("signed transaction root certificate verification error")
	}

	return nil
}

// UnExpired will check to see if the receipt is still valid
// @link https://developer.apple.com/documentation/appstorereceipts/responsebody/latest_receipt_info
func (r *AppStoreSVC) UnExpired() (interface{}, error) {
	if r.Data.Status != statusActive {
		return appstore.InApp{}, fmt.Errorf("invalid receipt status code:%d", r.Data.Status)
	}

	if len(r.Data.LatestReceiptInfo) == 0 {
		return appstore.InApp{}, errors.New("latest_receipt_info is empty")
	}

	// check if expired
	now := time.Now().UnixMilli()
	exp, iapp := r.expires()
	if exp < now {
		return appstore.InApp{}, fmt.Errorf("transaction was cancelled or had expired on %d", exp)
	}

	return iapp, nil
}

func (r *AppStoreSVC) GetData() interface{} {
	rs, _ := r.findReceiptByAppServiceID()
	return rs
}

func (r *AppStoreSVC) expires() (int64, appstore.InApp) {
	if len(r.Data.LatestReceiptInfo) == 0 {
		return 0, appstore.InApp{}
	}

	found, err := r.findReceiptByAppServiceID()
	if err != nil {
		r.log.Error(err)
		return 0, appstore.InApp{}
	}

	// case expire date is empty then is ppv
	expiresMs := found.ExpiresDateMS
	if expiresMs == "" {
		return time.Now().UnixMilli() + int64(extraHrMs), found
	}

	expires, err := strconv.ParseInt(expiresMs, 10, 64)
	if err != nil {
		r.log.Error(err)
		return 0, appstore.InApp{}
	}

	return expires, found
}

// iterate through all receipt entries and return the latest transaction
func (r *AppStoreSVC) findReceiptByAppServiceID() (appstore.InApp, error) {
	var latestExpires int64 = 0
	var latestIndex int = -1

	for i, v := range r.Data.LatestReceiptInfo {
		if v.ProductID != r.AppServiceID {
			continue
		}

		expiresMs := v.ExpiresDateMS
		if expiresMs == "" { // if expire time isn't set, it's probably ppv, use purchase time instead
			expiresMs = v.PurchaseDateMS
		}

		expires, err := strconv.ParseInt(expiresMs, 10, 64)
		if err != nil {
			r.log.Error(err)
			return appstore.InApp{}, err
		}
		if expires > latestExpires {
			latestExpires = expires
			latestIndex = i
		}
	}

	if latestIndex >= 0 {
		return r.Data.LatestReceiptInfo[latestIndex], nil
	}

	return appstore.InApp{}, fmt.Errorf("receipt with productID %s  not found in latest_receipt_info", r.AppServiceID)
}

func (r *AppStoreSVC) download(ctx context.Context, conf AppStoreConfig) error {
	var err error
	var span stats.Span

	_, span = r.stats.StartMethodSpan(ctx, "appStore.Download")
	defer func() { span.FinishWithError(err) }()

	req := appstore.IAPRequest{
		Password:               r.secret,
		ReceiptData:            conf.ReceiptData,
		ExcludeOldTransactions: false, // we'll need the entire transaction history when summarizing receipt status
	}
	res := appstore.IAPResponse{}
	client := appstore.New()
	err = client.Verify(ctx, req, &res)
	if err != nil {
		if strings.Contains(err.Error(), "500") {
			r.log.Error("retrying verification due to a 500 code status error")
			if err == nil {
				goto outside
			}
		}
		return err
	}
outside:
	if err := appstore.HandleError(res.Status); err != nil {
		return err
	}
	r.Data = res
	return nil
}

//Retry will be implemented when convert this to a circuit breaker
//
//func retryIapRequest(ctx context.Context, req appstore.IAPRequest, res *appstore.IAPResponse, cl *appstore.Client) error {
//	var err error
//	for i := 1; i <= cache.Retries; i++ {
//		request.GetFromContext(ctx).GetLoggingEntry().Errorf("verification retry number: %d", i)
//		if err = cl.Verify(ctx, req, res); err == nil {
//			return nil
//		}
//	}
//	return err
//}

func GetRootCert() []byte {
	ctx := context.Background()
	resp, err := http.Get("https://www.apple.com/certificateauthority/AppleRootCA-G3.cer")
	if err != nil || resp.StatusCode != http.StatusOK {
		log.Errorf(ctx, "error accessing apple root certificate: %v", err)
		return nil
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf(ctx, "error reading apple root certificate: %v", err)
		return nil
	}

	return body
}
