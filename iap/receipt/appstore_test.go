package receipt

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestValidateAppStoreTransaction tests the ValidateAppStoreTransaction function
func TestValidateAppStoreTransaction(t *testing.T) {
	tests := map[string]struct {
		signedTransaction string
		useMock           bool
		expectedError     string
	}{
		"empty signed transaction": {
			signedTransaction: "",
			expectedError:     "signed transaction is empty",
		},
		"mock mode with invalid transaction": {
			signedTransaction: "invalidTransaction",
			useMock:           true,
			expectedError:     "token contains an invalid number of segments",
		},
		"non-mock mode with invalid transaction": {
			signedTransaction: "invalidTransaction",
			useMock:           false,
			expectedError:     "invalid character '\\u008a' looking for beginning of value",
		},
		"non-mock mode with invalid transaction which throw a panic": {
			signedTransaction: "eyJlcnJvckNvZGUiOm51bGwsImVycm9yRGV0YWlscyI6bnVsbCwiZXJyb3JNZXNzYWdlIjpudWxsLCJzdGF0dXMiOjAsInRyYW5zYWN0aW9uSWQiOiJCU2Z2ZUxLOSIsIm9yaWdpbmFsVHJhbnNhY3Rpb25JZCI6IkJTZnZlTEs5IiwiYW1vdW50Ijo1OS45OSwiY2FuY2VsbGVkIjpmYWxzZSwiY2FuY2VsbGVkVHJhbnNhY3Rpb25JZHMiOltdLCJjaGFubmVsSWQiOjEyMywiY2hhbm5lbE5hbWUiOiJjaGFubmVsTmFtZSIsImNvdXBvbkNvZGUiOm51bGwsImN1cnJlbmN5IjoiVVNEIiwiZXhwaXJhdGlvbkRhdGUiOiIvRGF0ZSgxNzQ2OTk5NDYyOTQxKzAwMDApLyIsImlzRW50aXRsZWQiOnRydWUsIm9yaWdpbmFsUHVyY2hhc2VEYXRlIjoiL0RhdGUoMTc0MzExMTQ2Mjk0MSswMDAwKS8iLCJwYXJ0bmVyUmVmZXJlbmNlSWQiOm51bGwsInByb2R1Y3RJZCI6ImNvbS5mb3huYXRpb24ucm9rdS5tb250aGx5LmZyZWV0cmlhbCIsInByb2R1Y3ROYW1lIjoiIiwicHVyY2hhc2VEYXRlIjoiL0RhdGUoMTc0MzExMTQ2Mjk0MSswMDAwKS8iLCJwdXJjaGFzZVN0YXR1cyI6IkFjdGl2ZSIsInB1cmNoYXNlVHlwZSI6IlNhbGUiLCJxdWFudGl0eSI6MSwicm9rdUN1c3RvbWVySWQiOiJlMWEwMzc3Zi0xNzM3LTQzMDUtYWNlZi1lNTc1YmZjZDg3NDQiLCJ0YXgiOjAuOTksInRvdGFsIjoxMC45OH0",
			useMock:           false,
			expectedError:     "invalid signedTransaction",
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			transaction, err := ValidateAppStoreTransaction(tt.signedTransaction, tt.useMock)

			if tt.expectedError != "" {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
				assert.Nil(t, transaction)
				return
			}

			assert.NoError(t, err)
			assert.NotNil(t, transaction)
		})
	}
}
