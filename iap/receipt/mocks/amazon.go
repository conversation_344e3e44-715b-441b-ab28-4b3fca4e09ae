package mocks

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/awa/go-iap/amazon"
	"net/http"
	"time"

	"github.com/foxcorp-product/entitlement-sdk/mockserver"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/go-chi/chi/v5"
)

func AmazonMockRouter() chi.Router {
	r := chi.NewMux()
	r.Route("/version/1.0/verifyReceiptId/developer/{secret}/user/{userId}/receiptId", func(r chi.Router) {
		r.HandleFunc("/{receiptId}", amazonValidateTransactionHandler)
	})
	return r
}

var chiParam = chi.URLParam

type V1ValidateAmazonTransactionRequest struct {
	Receipt      string `json:"receipt"`
	AmazonUserId string `json:"amazonUserId"`
}

// IAPResponse represents the Amazon Receipt Verify response
type IAPResponse struct {
	ReceiptID          string `json:"receiptId"`
	ProductType        string `json:"productType"`
	ProductID          string `json:"productId"`
	PurchaseDate       int64  `json:"purchaseDate"`
	RenewalDate        int64  `json:"renewalDate,omitempty"`
	FreeTrialEndDate   int64  `json:"freeTrialEndDate,omitempty"`
	GracePeriodEndDate int64  `json:"gracePeriodEndDate,omitempty"`
	CancelDate         int64  `json:"cancelDate,omitempty"`
	CancelReason       int64  `json:"cancelReason,omitempty"`
	TestTransaction    bool   `json:"testTransaction"`
	BetaProduct        bool   `json:"betaProduct"`
	ParentProductID    string `json:"parentProductId,omitempty"`
	Quantity           int    `json:"quantity"`
	Term               string `json:"term,omitempty"`
	TermSku            string `json:"termSku,omitempty"`
	AutoRenewing       bool   `json:"autoRenewing"`
	FulfillmentDate    int64  `json:"fulfillmentDate,omitempty"`
	FulfillmentResult  string `json:"fulfillmentResult,omitempty"`
	Promotions         []amazon.Promotion
}

func amazonValidateTransactionHandler(w http.ResponseWriter, r *http.Request) {
	l := request.GetFromContext(r.Context()).GetLoggingEntry()
	in, err := getRequestInput(r)
	if err != nil {
		l.Error("amazonValidateTransactionHandler: missing userId or receiptId")
		mockserver.SendJSONTemplate(w, r, http.StatusBadRequest, `{"message":"bad mock request", "status":false}`)
		return
	}

	res := IAPResponse{
		ReceiptID:       in.Receipt,
		ProductType:     "SUBSCRIPTION",
		ProductID:       "com.amazon.default",
		PurchaseDate:    time.Now().UnixMilli(),
		RenewalDate:     time.Now().AddDate(0, 0, 30).UnixMilli(),
		TestTransaction: true,
		Quantity:        1,
		AutoRenewing:    true,
	}

	// Try to parse the mock receipt as base64 encoded JSON
	l.Debugf("amazonValidateTransactionHandler: decoding receipt %s", in.Receipt)
	jsonData, err := base64.StdEncoding.DecodeString(in.Receipt)
	if err == nil {
		l.Debugf("amazonValidateTransactionHandler: decoded receipt %s. unmarshalling...", jsonData)
		var customRes IAPResponse
		if err := json.Unmarshal(jsonData, &customRes); err == nil {
			l.Debugf("amazonValidateTransactionHandler: unmarshalled receipt %v", customRes)
			customRes.ReceiptID = in.Receipt
			res = customRes
		}
	}

	resStringTemplate, err := json.Marshal(&res)
	if err != nil {
		mockserver.SendJSONTemplate(w, r, http.StatusInternalServerError, `{"message":"unable to marshal amazon mock response","status":false}`)
		return
	}

	mockserver.SendJSONTemplate(w, r, http.StatusOK, string(resStringTemplate))
}

func getRequestInput(req *http.Request) (V1ValidateAmazonTransactionRequest, error) {
	var in V1ValidateAmazonTransactionRequest
	userID := chiParam(req, "userId")
	if userID == "" {
		return in, fmt.Errorf("missing userId")
	}
	receipt := chiParam(req, "receiptId")
	if receipt == "" {
		return in, fmt.Errorf("missing receiptId")
	}
	in.AmazonUserId = userID
	in.Receipt = receipt
	return in, nil
}
