package mocks

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestAmazonMockValidateTransactionHandler(t *testing.T) {
	aw := httptest.NewRecorder()
	req := httptest.NewRequest(http.MethodGet, "/version/1.0/verifyReceiptId/developer/fakesecret/user/1234/receiptId/5678", nil)

	router := AmazonMockRouter()
	router.ServeHTTP(aw, req)

	require.Equal(t, http.StatusOK, aw.Code)
	require.Equal(t, "application/json", aw.Header().Get("Content-Type"))
}

func TestAmazonValidateTransactionHandler(t *testing.T) {
	t.Run("should have freetrial in 7 days", func(t *testing.T) {
		freeTrialDays := 7

		now := time.Now()
		receipt := IAPResponse{
			ProductType:      "SUBSCRIPTION",
			ProductID:        "com.amazon.test",
			PurchaseDate:     now.UnixMilli(),
			RenewalDate:      now.AddDate(0, 0, 30).UnixMilli(),
			FreeTrialEndDate: now.AddDate(0, 0, freeTrialDays).UnixMilli(),
			TestTransaction:  true,
			AutoRenewing:     true,
		}

		receiptBytes, _ := json.Marshal(receipt)
		encodedReceipt := base64.StdEncoding.EncodeToString(receiptBytes)

		expectedTrialDate := receipt.FreeTrialEndDate
		testPath := fmt.Sprintf("/version/1.0/verifyReceiptId/developer/fakesecret/user/1234/receiptId/%s", encodedReceipt)

		aw := httptest.NewRecorder()
		req := httptest.NewRequest(http.MethodGet, testPath, nil)

		router := AmazonMockRouter()
		router.ServeHTTP(aw, req)

		require.Equal(t, http.StatusOK, aw.Code)
		resBody := &IAPResponse{}
		err := json.Unmarshal(aw.Body.Bytes(), resBody)
		require.NoError(t, err)
		assert.Equal(t, expectedTrialDate, resBody.FreeTrialEndDate)
	})

	t.Run("should have no freetrial", func(t *testing.T) {
		aw := httptest.NewRecorder()
		req := httptest.NewRequest(http.MethodGet, "/version/1.0/verifyReceiptId/developer/fakesecret/user/1234/receiptId/fakeReceipt", nil)

		router := AmazonMockRouter()
		router.ServeHTTP(aw, req)

		require.Equal(t, http.StatusOK, aw.Code)
		resBody := &IAPResponse{}
		err := json.Unmarshal(aw.Body.Bytes(), resBody)
		require.NoError(t, err)
		assert.Zero(t, resBody.FreeTrialEndDate, "Free trial end date should be zero for standard receipt")
	})

	t.Run("should have cancel date and reason", func(t *testing.T) {
		now := time.Now()
		cancelReason := int64(2)
		cancelDate := now.AddDate(0, 0, 10).UnixMilli()

		receipt := IAPResponse{
			ProductType:     "SUBSCRIPTION",
			ProductID:       "com.amazon.test",
			PurchaseDate:    now.UnixMilli(),
			RenewalDate:     now.AddDate(0, 0, 30).UnixMilli(),
			CancelDate:      cancelDate,
			CancelReason:    cancelReason,
			TestTransaction: true,
			AutoRenewing:    false,
		}

		receiptBytes, _ := json.Marshal(receipt)
		encodedReceipt := base64.StdEncoding.EncodeToString(receiptBytes)

		testPath := fmt.Sprintf("/version/1.0/verifyReceiptId/developer/fakesecret/user/1234/receiptId/%s", encodedReceipt)

		aw := httptest.NewRecorder()
		req := httptest.NewRequest(http.MethodGet, testPath, nil)

		router := AmazonMockRouter()
		router.ServeHTTP(aw, req)

		require.Equal(t, http.StatusOK, aw.Code)
		resBody := &IAPResponse{}
		err := json.Unmarshal(aw.Body.Bytes(), resBody)
		require.NoError(t, err)
		assert.Equal(t, cancelDate, resBody.CancelDate)
		assert.Equal(t, cancelReason, resBody.CancelReason)
	})

	t.Run("should have grace period", func(t *testing.T) {
		now := time.Now()
		gracePeriodEndDate := now.AddDate(0, 0, 14).UnixMilli()

		receipt := IAPResponse{
			ProductType:        "SUBSCRIPTION",
			ProductID:          "com.amazon.test",
			PurchaseDate:       now.AddDate(0, 0, -30).UnixMilli(),
			RenewalDate:        now.AddDate(0, 0, -5).UnixMilli(), // Past renewal
			GracePeriodEndDate: gracePeriodEndDate,
			TestTransaction:    true,
			AutoRenewing:       true,
		}

		receiptBytes, _ := json.Marshal(receipt)
		encodedReceipt := base64.StdEncoding.EncodeToString(receiptBytes)

		testPath := fmt.Sprintf("/version/1.0/verifyReceiptId/developer/fakesecret/user/1234/receiptId/%s", encodedReceipt)

		aw := httptest.NewRecorder()
		req := httptest.NewRequest(http.MethodGet, testPath, nil)

		router := AmazonMockRouter()
		router.ServeHTTP(aw, req)

		require.Equal(t, http.StatusOK, aw.Code)
		resBody := &IAPResponse{}
		err := json.Unmarshal(aw.Body.Bytes(), resBody)
		require.NoError(t, err)
		assert.Equal(t, gracePeriodEndDate, resBody.GracePeriodEndDate)
	})

	t.Run("should handle complex receipt", func(t *testing.T) {
		now := time.Now()
		receipt := IAPResponse{
			ProductType:        "SUBSCRIPTION",
			ProductID:          "com.amazon.premium",
			PurchaseDate:       now.AddDate(0, -1, 0).UnixMilli(),
			RenewalDate:        now.AddDate(0, 0, 10).UnixMilli(),
			FreeTrialEndDate:   now.AddDate(0, 0, -20).UnixMilli(),
			GracePeriodEndDate: now.AddDate(0, 0, 24).UnixMilli(),
			CancelDate:         now.AddDate(0, 0, 10).UnixMilli(),
			CancelReason:       3,
			TestTransaction:    true,
			AutoRenewing:       false,
			Term:               "1 Year",
			TermSku:            "com.amazon.premium_annual",
			ParentProductID:    "com.amazon.family",
			Quantity:           2,
		}

		receiptBytes, _ := json.Marshal(receipt)
		encodedReceipt := base64.StdEncoding.EncodeToString(receiptBytes)

		testPath := fmt.Sprintf("/version/1.0/verifyReceiptId/developer/fakesecret/user/1234/receiptId/%s", encodedReceipt)

		aw := httptest.NewRecorder()
		req := httptest.NewRequest(http.MethodGet, testPath, nil)

		router := AmazonMockRouter()
		router.ServeHTTP(aw, req)

		require.Equal(t, http.StatusOK, aw.Code)
		resBody := &IAPResponse{}
		err := json.Unmarshal(aw.Body.Bytes(), resBody)
		require.NoError(t, err)

		assert.Equal(t, receipt.ProductType, resBody.ProductType)
		assert.Equal(t, receipt.ProductID, resBody.ProductID)
		assert.Equal(t, receipt.PurchaseDate, resBody.PurchaseDate)
		assert.Equal(t, receipt.RenewalDate, resBody.RenewalDate)
		assert.Equal(t, receipt.FreeTrialEndDate, resBody.FreeTrialEndDate)
		assert.Equal(t, receipt.GracePeriodEndDate, resBody.GracePeriodEndDate)
		assert.Equal(t, receipt.CancelDate, resBody.CancelDate)
		assert.Equal(t, receipt.CancelReason, resBody.CancelReason)
		assert.Equal(t, receipt.TestTransaction, resBody.TestTransaction)
		assert.Equal(t, receipt.AutoRenewing, resBody.AutoRenewing)
		assert.Equal(t, receipt.Term, resBody.Term)
		assert.Equal(t, receipt.TermSku, resBody.TermSku)
		assert.Equal(t, receipt.ParentProductID, resBody.ParentProductID)
		assert.Equal(t, receipt.Quantity, resBody.Quantity)
	})
}
