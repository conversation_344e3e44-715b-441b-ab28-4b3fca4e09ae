package mocks

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"strings"

	"github.com/foxcorp-product/entitlement-sdk/mockserver"
	"github.com/go-chi/chi/v5"
)

// defaultSubscription<PERSON>son default mocked JSO<PERSON> when getting subscription info from the API
const defaultSubscriptionJson = `
{
  "kind": "androidpublisher#subscriptionPurchaseV2",
  "startTime": "2022-07-18T22:59:57.277Z",
  "regionCode": "US",
  "subscriptionState": "SUBSCRIPTION_STATE_ACTIVE",
  "latestOrderId": "GPA.3345-8075-6320-91316..2",
  "acknowledgementState": "ACKNOWLEDGEMENT_STATE_ACKNOWLEDGED",
  "lineItems": [
    {
      "productId": "com.fng.foxnation.yearly",
      "expiryTime": "2025-07-25T22:54:44.417Z",
      "autoRenewingPlan": {
        "autoRenewEnabled": true
      },
      "offerDetails": {
        "basePlanId": "p1y"
      }
    }
  ]
}
`

// defaultSubscriptionO<PERSON><PERSON>son default mocked <PERSON>SO<PERSON> when getting subscription offer info from the API. To keep it smaller,
// some field were omitted
const defaultSubscriptionOfferJson = `
{
  "basePlanId": "p1y",
  "offerId": "freetrial",
  "otherRegionsConfig": {
    "otherRegionsNewSubscriberAvailability": true
  },
  "packageName": "com.fng.foxnation",
  "phases": [
    {
      "duration": "P7D",
      "otherRegionsConfig": {
        "free": {}
      },
      "recurrenceCount": 1,
      "regionalConfigs": [
        {
          "free": {},
          "regionCode": "AE"
        },
        {
          "free": {},
          "regionCode": "US"
        },
        {
          "free": {},
          "regionCode": "ZW"
        }
      ]
    }
  ],
  "productId": "com.fng.foxnation.yearly",
  "regionalConfigs": [
    {
      "newSubscriberAvailability": true,
      "regionCode": "AE"
    },
    {
      "newSubscriberAvailability": true,
      "regionCode": "US"
    },
    {
      "newSubscriberAvailability": true,
      "regionCode": "ZW"
    }
  ],
  "state": "ACTIVE"
}
`

// resetContextMiddleware resets the internal chi RouteContext. This way we make sure that every request is not keeping
// any previous state. Without this, chi was having problems to route a call made after another call.
// Example: verify sub worked but then getting sub offer was failing.
func resetContextMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := chi.NewRouteContext()
		newCtx := context.WithValue(context.Background(), chi.RouteCtxKey, ctx)
		r = r.WithContext(newCtx)

		next.ServeHTTP(w, r)
	})
}

// GoogleAPIMockRouter returns a router to mock Google api calls
func GoogleAPIMockRouter() chi.Router {
	r := chi.NewMux()
	r.Use(resetContextMiddleware)
	r.Route("/androidpublisher/v3/applications", func(r chi.Router) {
		r.HandleFunc("/{packageName}/purchases/subscriptionsv2/tokens/{token}", googleValidateTransaction)
		r.HandleFunc("/{packageName}/subscriptions/{productId}/basePlans/{basePlanId}/offers/{offerId}", googleSubscriptionOffer)
	})
	return r
}

// GoogleAuthMockRouter returns a router to mock Google auth calls and basically ignore them
func GoogleAuthMockRouter() chi.Router {
	r := chi.NewMux()
	r.Use(resetContextMiddleware)
	r.Post("/token", func(w http.ResponseWriter, r *http.Request) {
		fmt.Fprint(w, "{}")
	})
	return r
}

func googleValidateTransaction(w http.ResponseWriter, r *http.Request) {
	packageName, err := url.QueryUnescape(chi.URLParam(r, "packageName"))
	if err != nil {
		mockserver.SendJSONTemplate(w, r, http.StatusOK, defaultSubscriptionJson)
		return
	}

	j, err := checkEncodedJSONInString(packageName)
	if err != nil {
		log.Println("googleValidateTransaction.checkEncodedJSONInString: " + err.Error())
		mockserver.SendJSONTemplate(w, r, http.StatusOK, defaultSubscriptionJson)
		return
	}

	mockserver.SendJSONTemplate(w, r, http.StatusOK, j)
}

func googleSubscriptionOffer(w http.ResponseWriter, r *http.Request) {
	basePlanID, err := url.QueryUnescape(chi.URLParam(r, "basePlanId"))
	if err != nil {
		mockserver.SendJSONTemplate(w, r, http.StatusOK, defaultSubscriptionOfferJson)
		return
	}

	j, err := checkEncodedJSONInString(basePlanID)
	if err != nil {
		log.Println("googleSubscriptionOffer.checkEncodedJSONInString: " + err.Error())
		mockserver.SendJSONTemplate(w, r, http.StatusOK, defaultSubscriptionOfferJson)
		return
	}

	mockserver.SendJSONTemplate(w, r, http.StatusOK, j)
}

// checkEncodedJSONInString checks if a string has an "encoded:" prefix, which means it's an encoded JSON that must be
// used as a mock response. If that's not the case, a default response is returned
func checkEncodedJSONInString(str string) (string, error) {
	// if starts with "encoded:" then decode the base64 encoded string
	if !strings.HasPrefix(str, "encoded:") {
		return "", fmt.Errorf("str is not encoded: %v", str)
	}

	// remove the "encoded:" prefix
	encodedStr := str[8:]

	// decode the base64 encoded receipt
	decodedStr, err := base64.StdEncoding.DecodeString(encodedStr)
	if err != nil {
		return "", fmt.Errorf("error base64 decoding: %v", err)
	}

	// unmarshal the json
	var js json.RawMessage
	err = json.Unmarshal(decodedStr, &js)
	if err != nil {
		return "", fmt.Errorf("error unmarshalling json: %v", err)
	}

	return string(decodedStr), nil
}
