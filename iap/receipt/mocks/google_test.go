package mocks

import (
	"encoding/base64"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGoogleValidateTransaction(t *testing.T) {
	raw := `
{
  "kind": "androidpublisher#subscriptionPurchaseV2",
  "startTime": "2022-07-07T07:07:07.277Z",
  "regionCode": "US",
  "subscriptionState": "SUBSCRIPTION_STATE_ACTIVE",
  "latestOrderId": "GPA.3345-8075-6320-91316..2",
  "acknowledgementState": "ACKNOWLEDGEMENT_STATE_ACKNOWLEDGED",
  "lineItems": [
    {
      "productId": "com.fng.foxnation.yearly",
      "expiryTime": "2026-12-12T12:12:12.417Z",
      "autoRenewingPlan": {
        "autoRenewEnabled": true
      },
      "offerDetails": {
        "basePlanId": "p1y",
        "offerId": "freetrial",
        "offerTags": [
          "promo"
        ]
      }
    }
  ]
}
`

	tests := []struct {
		name       string
		input      func() string
		invalidReq func(*http.Request)
		output     string
	}{
		{
			name:  "Invalid URL encoding",
			input: func() string { return "replace" },
			invalidReq: func(r *http.Request) {
				r.URL.Path = strings.Replace(r.URL.Path, "replace", "%", -1)
			},
			output: defaultSubscriptionJson,
		},
		{
			name:   "Invalid JSON encoded",
			input:  func() string { return "notavalidjson" },
			output: defaultSubscriptionJson,
		},
		{
			name: "Valid JSON encoded",
			input: func() string {
				return "encoded:" + base64.StdEncoding.EncodeToString([]byte(raw))
			},
			output: raw,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rw := httptest.NewRecorder()
			r := httptest.NewRequest(http.MethodGet,
				"/androidpublisher/v3/applications/"+tt.input()+"/purchases/subscriptionsv2/tokens/fakeToken",
				nil)

			if tt.invalidReq != nil {
				tt.invalidReq(r)
			}

			router := GoogleAPIMockRouter()
			router.ServeHTTP(rw, r)

			require.Equal(t, http.StatusOK, rw.Code)
			require.Equal(t, "application/json", rw.Header().Get("Content-Type"))
			require.Equal(t, tt.output, rw.Body.String())
		})
	}
}

func TestGoogleSubscriptionOffer(t *testing.T) {
	raw := `
{
  "basePlanId": "p1y",
  "offerId": "freetrial",
  "otherRegionsConfig": {
    "otherRegionsNewSubscriberAvailability": true
  },
  "packageName": "com.fng.foxnation",
  "phases": [
    {
      "duration": "P7D",
      "otherRegionsConfig": {
        "free": {}
      },
      "recurrenceCount": 1,
      "regionalConfigs": [
        {
          "free": {},
          "regionCode": "US"
        }
      ]
    }
  ],
  "productId": "com.fng.foxnation.yearly",
  "regionalConfigs": [
    {
      "newSubscriberAvailability": true,
      "regionCode": "US"
    }
  ],
  "state": "ACTIVE"
}
`

	tests := []struct {
		name       string
		input      func() string
		invalidReq func(*http.Request)
		output     string
	}{
		{
			name:  "Invalid URL encoding",
			input: func() string { return "replace" },
			invalidReq: func(r *http.Request) {
				r.URL.Path = strings.Replace(r.URL.Path, "replace", "%", -1)
			},
			output: defaultSubscriptionOfferJson,
		},
		{
			name:   "Invalid JSON encoded",
			input:  func() string { return "notavalidjson" },
			output: defaultSubscriptionOfferJson,
		},
		{
			name: "Valid JSON encoded",
			input: func() string {
				return "encoded:" + base64.StdEncoding.EncodeToString([]byte(raw))
			},
			output: raw,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rw := httptest.NewRecorder()
			r := httptest.NewRequest(http.MethodGet,
				"/androidpublisher/v3/applications/fakePackageName/subscriptions/fakeProductId/basePlans/"+tt.input()+"/offers/fakeOfferId",
				nil)

			if tt.invalidReq != nil {
				tt.invalidReq(r)
			}

			router := GoogleAPIMockRouter()
			router.ServeHTTP(rw, r)

			require.Equal(t, http.StatusOK, rw.Code)
			require.Equal(t, "application/json", rw.Header().Get("Content-Type"))
			require.Equal(t, tt.output, rw.Body.String())
		})
	}
}

func TestGoogle_validateAuthCalls(t *testing.T) {
	rw := httptest.NewRecorder()
	r := httptest.NewRequest(http.MethodPost,
		"/token",
		nil)

	router := GoogleAuthMockRouter()
	router.ServeHTTP(rw, r)

	require.Equal(t, http.StatusOK, rw.Code)
	require.Equal(t, "{}", rw.Body.String())
}

func TestCheckEncodedJSONInString(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected func(*testing.T, string, error)
	}{
		{
			name:  "Failure - string doesn't have 'encoded:' prefix",
			input: "randomstring",
			expected: func(t *testing.T, resp string, err error) {
				assert.Empty(t, resp)
				require.Error(t, err)
				assert.Equal(t, "str is not encoded: randomstring", err.Error())
			},
		},
		{
			name:  "Failure - string is not base64 encoded",
			input: "encoded:randomstring@#$",
			expected: func(t *testing.T, resp string, err error) {
				assert.Empty(t, resp)
				require.Error(t, err)
				assert.Contains(t, err.Error(), "error base64 decoding: illegal base64 data at input byte")
			},
		},
		{
			name:  "Failure - string is not an encoded JSON",
			input: "encoded:randomstring",
			expected: func(t *testing.T, resp string, err error) {
				assert.Empty(t, resp)
				require.Error(t, err)
				assert.Contains(t, err.Error(), "error unmarshalling json: invalid character")
			},
		},
		{
			name:  "Success",
			input: "encoded:e30=", // {}
			expected: func(t *testing.T, resp string, err error) {
				require.NoError(t, err)
				assert.Equal(t, "{}", resp)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := checkEncodedJSONInString(tt.input)
			tt.expected(t, resp, err)
		})
	}
}
