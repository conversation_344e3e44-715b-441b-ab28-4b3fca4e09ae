package mocks

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"net/http"

	"github.com/foxcorp-product/entitlement-sdk/mockserver"
	"github.com/go-chi/chi/v5"
)

func RokuMocksRouter() chi.Router {
	r := chi.NewMux()
	r.Route("/listen/transaction-service.svc", func(r chi.Router) {
		r.<PERSON>leFunc("/validate-transaction/{secret}/expired-receipt", rokuValidateExpiredTransaction)
		r.<PERSON><PERSON>unc("/validate-transaction/{secret}/{receipt}", rokuValidateTransaction)
	})
	return r
}

func rokuValidateExpiredTransaction(w http.ResponseWriter, r *http.Request) {
	mockserver.SendJSONTemplate(w, r, http.StatusOK, `
    {
            "errorCode": null,
            "errorDetails": null,
            "errorMessage": null,
            "status": 0,
            "OriginalTransactionId": "expired-receipt",
            "amount": 59.99,
            "cancelled": false,
            "cancelledTransactionIds": [],
            "channelId": 123,
            "channelName": "channelName",
            "couponCode": null,
            "currency": "USD",
            "expirationDate": "/Date(1734529270000+0000)/",
            "isEntitled": false,
            "originalPurchaseDate": "/Date(1734529270000+0000)/",
            "partnerReferenceId": null,
            "productId": "productID",
            "productName": "productName",
            "purchaseDate": "/Date(1734529270000+0000)/",
            "purchaseStatus": "Active",
            "purchaseType": null,
            "quantity": 1,
            "rokuCustomerId": "rokuCustomerID",
            "tax": 0.99,
            "total": 10.98,
            "transactionId": "expired-receipt"
    }
  `)
}

// checkEncodedJSON allows a request to mock any base64 json encoded are a receipt response with the prefix "encoded:"
func checkEncodedJSON(r *http.Request) (string, error) {
	receipt := chi.URLParam(r, "receipt")
	// if starts with "encoded:" then decode the base64 encoded receipt
	if len(receipt) < 8 || receipt[:8] != "encoded:" {
		return "", fmt.Errorf("receipt is not encoded: %v", receipt)
	}

	// remove the "encoded:" prefix
	encodedReceipt := receipt[8:]

	// decode the base64 encoded receipt
	decodedReceipt, err := base64.StdEncoding.DecodeString(encodedReceipt)
	if err != nil {
		return "", fmt.Errorf("error base64 decoding: %v", err)
	}

	// unmarshal the json
	var js json.RawMessage
	err = json.Unmarshal(decodedReceipt, &js)
	if err != nil {
		return "", fmt.Errorf("error unmarshaling json: %v", err)
	}

	return string(decodedReceipt), nil
}

func rokuValidateTransaction(w http.ResponseWriter, r *http.Request) {
	j, err := checkEncodedJSON(r)
	if err == nil {
		mockserver.SendJSONTemplate(w, r, http.StatusOK, j)
		return
	}

	log.Println("rokuValidateTransaction.checkEncodedJSON: " + err.Error())

	mockserver.SendJSONTemplate(w, r, http.StatusOK, `
    {
            "errorCode": null,
            "errorDetails": null,
            "errorMessage": null,
            "status": 0,
            "OriginalTransactionId": "{{param . "receipt"}}",
            "amount": 59.99,
            "cancelled": false,
            "cancelledTransactionIds": [],
            "channelId": 123,
            "channelName": "channelName",
            "couponCode": null,
            "currency": "USD",
            "expirationDate": "/Date(1923831332000+0000)/",
            "isEntitled": true,
            "originalPurchaseDate": "/Date(1734529270000+0000)/",
            "partnerReferenceId": null,
            "productId": "productID",
            "productName": "productName",
            "purchaseDate": "/Date(1734529270000+0000)/",
            "purchaseStatus": "Active",
            "purchaseType": null,
            "quantity": 1,
            "rokuCustomerId": "rokuCustomerID",
            "tax": 0.99,
            "total": 10.98,
            "transactionId": "{{param . "receipt"}}"
    }
  `)
}
