package mocks

import (
	"encoding/base64"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/foxcorp-product/commerce-receiptverify/iap/receipt"
	"github.com/stretchr/testify/require"
)

func TestRoku_validateTransaction(t *testing.T) {
	rw := httptest.NewRecorder()
	r := httptest.NewRequest(http.MethodGet, "/listen/transaction-service.svc/validate-transaction/fakesecret/1234", nil)

	router := RokuMocksRouter()
	router.ServeHTTP(rw, r)

	require.Equal(t, http.StatusOK, rw.Code)
	require.Equal(t, "application/json", rw.Header().Get("Content-Type"))

	var response receipt.RokuResponse
	require.Equal(t, http.StatusOK, rw.Code)
	require.NoError(t, json.Unmarshal(rw.Body.Bytes(), &response))

	require.Equal(t, "1234", response.TransactionID)
	require.True(t, response.IsEntitled)
}

func TestRoku_validateTransactionExpiredReceipt(t *testing.T) {
	rw := httptest.NewRecorder()
	r := httptest.NewRequest(http.MethodGet, "/listen/transaction-service.svc/validate-transaction/fakesecret/expired-receipt", nil)

	router := RokuMocksRouter()
	router.ServeHTTP(rw, r)

	require.Equal(t, http.StatusOK, rw.Code)
	require.Equal(t, "application/json", rw.Header().Get("Content-Type"))

	var response receipt.RokuResponse
	require.Equal(t, http.StatusOK, rw.Code)
	require.NoError(t, json.Unmarshal(rw.Body.Bytes(), &response))

	require.Equal(t, "expired-receipt", response.TransactionID)
	require.False(t, response.IsEntitled)
}

func TestRoku_validateJsonEncodedTransaction(t *testing.T) {
	raw := `{
		"errorCode": null,
		"errorDetails": null,
		"errorMessage": null,
		"status": 0,
		"OriginalTransactionId": "456",
		"amount": 159.99,
		"cancelled": false,
		"cancelledTransactionIds": [],
		"channelId": 1123,
		"channelName": "channelName",
		"couponCode": null,
		"currency": "USD",
		"expirationDate": "/Date(1923831332000+0000)/",
		"isEntitled": true,
		"originalPurchaseDate": "/Date(1734529270000+0000)/",
		"partnerReferenceId": null,
		"productId": "productID",
		"productName": "productName",
		"purchaseDate": "/Date(1734529270000+0000)/",
		"purchaseStatus": "Active",
		"purchaseType": null,
		"quantity": 1,
		"rokuCustomerId": "rokuCustomerID",
		"tax": 0.99,
		"total": 10.98,
		"transactionId": "123"
	}`

	encodedReceipt := "encoded:" + base64.StdEncoding.EncodeToString([]byte(raw))

	rw := httptest.NewRecorder()
	r := httptest.NewRequest(http.MethodGet,
		"/listen/transaction-service.svc/validate-transaction/fakesecret/"+encodedReceipt,
		nil)

	router := RokuMocksRouter()
	router.ServeHTTP(rw, r)

	require.Equal(t, http.StatusOK, rw.Code)
	require.Equal(t, "application/json", rw.Header().Get("Content-Type"))

	require.Equal(t, http.StatusOK, rw.Code)
	require.Equal(t, raw, rw.Body.String())
}
