package receipt

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	planclient "github.com/foxcorp-product/commerce-plan/client"
	"github.com/foxcorp-product/entitlement-sdk/logger"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/foxcorp-product/entitlement-sdk/stats"

	"github.com/awa/go-iap/playstore"
	"google.golang.org/api/androidpublisher/v3"
)

const (
	GoogleAPIURL  = "https://androidpublisher.googleapis.com"
	GoogleAuthURL = "https://oauth2.googleapis.com"

	SubscriptionOfferActiveState = "ACTIVE"
	UnitedStatesRegionCode       = "US"
)

// IPlayStoreClient defines the methods of the playstore.Client that will be used here. Having this, we can mock the
// client and create meaningful unit tests.
type IPlayStoreClient interface {
	VerifyProduct(context.Context, string, string, string) (*androidpublisher.ProductPurchase, error)
	VerifySubscriptionV2(context.Context, string, string) (*androidpublisher.SubscriptionPurchaseV2, error)
	GetSubscriptionOffer(context.Context, string, string, string, string) (*androidpublisher.SubscriptionOffer, error)
}

type PlayStoreConfig struct {
	Token          string `json:"token"`
	PackageName    string `json:"packageName"`
	SubscriptionID string `json:"subscriptionID"`
}

type PlayStorePayload struct {
	Encrypted string `json:"encrypted"`
}

func (pc *PlayStoreConfig) validate() error {
	if pc.Token == "" || pc.PackageName == "" || pc.SubscriptionID == "" {
		return errors.New("token, subsID, PackageName & Secret are required for Playstore Receipt")
	}
	return nil
}

type PlayStoreData struct {
	Subscription      androidpublisher.SubscriptionPurchaseV2
	SubscriptionOffer *androidpublisher.SubscriptionOffer
	Purchase          androidpublisher.ProductPurchase
	onetime           bool
}

type PlayStore struct {
	Data            PlayStoreData
	playStoreClient IPlayStoreClient
	log             logger.Logger
	stats           Stats
}

type PlanClient interface {
	GetPlanByAppServiceID(ctx context.Context, in planclient.V1GetPlanByAppServiceIDInput) (planclient.Plan, error)
}

type PlayStoreResponse struct {
	OrderId                   string
	ProductID                 string
	PurchaseDate              int64
	ExpiryDate                int64
	AutoRenewing              bool
	OneTime                   bool
	IsFreeTrial               bool   // Used by googleconsumer during purchase flow to understand if the subscription is using a free trial
	UserInitiatedCancellation bool   // Used by googleconsumer during subscription cancelled to understand if the user initiated the cancellation
	LinkedPurchaseToken       string // Used by googleconsumer during subscription purchased to understand if the purchase was originated as a result of an Upgrade/Downgrade
	ResumeAtDate              string // Used by googleconsumer during subscription pause scheduled, required by braze
	Amount                    float64
	Currency                  string
	IsFlexibleOfferApplied    bool // Used by googleconsumer during subscription purchased to understand if the subscription is using a flexible offer
}

func NewPlayStoreResponse(ctx context.Context, res PlayStoreData) PlayStoreResponse {
	if res.onetime {
		return PlayStoreResponse{
			OrderId:      res.Purchase.OrderId,
			ProductID:    res.Purchase.ProductId,
			PurchaseDate: res.Purchase.PurchaseTimeMillis,
			OneTime:      true,
		}
	}

	log := request.GetFromContext(ctx).GetLoggingEntry()
	var autoRenew bool
	var isFlexibleOfferApplied bool
	var expiryDate, purchaseDate time.Time
	var amount int64
	var currency string
	var err error
	if len(res.Subscription.LineItems) > 0 {
		lineItem := res.Subscription.LineItems[0]

		if lineItem.AutoRenewingPlan != nil {
			autoRenew = lineItem.AutoRenewingPlan.AutoRenewEnabled

			if lineItem.AutoRenewingPlan.RecurringPrice != nil {
				currency = lineItem.AutoRenewingPlan.RecurringPrice.CurrencyCode
				recurringPrice := moneyToCents(lineItem.AutoRenewingPlan.RecurringPrice)
				amount = calculateAmount(recurringPrice, res.Subscription.LatestOrderId, res.SubscriptionOffer)
				isFlexibleOfferApplied = recurringPrice != amount
			}
		}

		expiryDate, err = time.Parse(time.RFC3339, lineItem.ExpiryTime)
		if err != nil {
			log.Errorf("ExpiryTime parsing error: %v", err)
		}
	}

	purchaseDate, err = time.Parse(time.RFC3339, res.Subscription.StartTime)
	if err != nil {
		log.Errorf("StartTime parsing error: %v", err)
	}

	var userInitiatedCancellation bool
	if res.Subscription.CanceledStateContext != nil && res.Subscription.CanceledStateContext.UserInitiatedCancellation != nil {
		userInitiatedCancellation = true
	}

	var resumeAtDate string
	if res.Subscription.PausedStateContext != nil {
		resumeAtDate = res.Subscription.PausedStateContext.AutoResumeTime
	}

	isFreeTrial := amount == 0
	return PlayStoreResponse{
		OrderId:                   StripOrderIDSuffix(res.Subscription.LatestOrderId),
		AutoRenewing:              autoRenew,
		PurchaseDate:              purchaseDate.UnixMilli(),
		ExpiryDate:                expiryDate.UnixMilli(),
		OneTime:                   false,
		IsFreeTrial:               isFreeTrial,
		UserInitiatedCancellation: userInitiatedCancellation,
		LinkedPurchaseToken:       res.Subscription.LinkedPurchaseToken,
		ResumeAtDate:              resumeAtDate,
		Amount:                    CentsToFloat(amount),
		Currency:                  currency,
		IsFlexibleOfferApplied:    isFlexibleOfferApplied,
	}
}

// moneyToCents converts the Money struct to an int64 amount in cents.
func moneyToCents(m *androidpublisher.Money) int64 {
	centsFromUnits := m.Units * 100
	centsFromNanos := int64(m.Nanos) / 10_000_000
	return centsFromUnits + centsFromNanos
}

// CentsToFloat converts an int64 amount in cents to a float64 amount.
func CentsToFloat(valueInCents int64) float64 {
	return float64(valueInCents) / 100.0
}

func (p *PlayStoreResponse) GetPurchaseDate() time.Time {
	return parseMilisDate(p.PurchaseDate)
}

func (p *PlayStoreResponse) GetExpirationDate() time.Time {
	return parseMilisDate(p.ExpiryDate)
}

func (p *PlayStoreResponse) GetResumeAtDate(format string) *string {
	resumeAtDate, err := time.Parse(time.RFC3339, p.ResumeAtDate)
	if err != nil {
		return nil
	}

	return ToPtr(resumeAtDate.Format(format))
}

func parseMilisDate(milis int64) time.Time {
	return time.Unix(0, milis*int64(time.Millisecond))
}

// NewPlayStore creates a Receipt
func NewPlayStore(ctx context.Context, stat Stats, secret string, payload PlayStorePayload, planClient PlanClient, mockedHttpClient *http.Client) (*PlayStore, error) {
	var err error
	_, span := stat.StartMethodSpan(ctx, "playStore.NewPlayStore")
	defer func() { span.FinishWithError(err) }()

	bytes, err := base64.StdEncoding.DecodeString(payload.Encrypted)
	if err != nil {
		return nil, err
	}
	type rawPayload struct {
		PackageName   string `json:"packageName"`
		ProductID     string `json:"productId"`
		PurchaseToken string `json:"purchaseToken"`
	}
	var raw rawPayload
	err = json.Unmarshal(bytes, &raw)
	if err != nil {
		return nil, err
	}
	conf := PlayStoreConfig{
		SubscriptionID: raw.ProductID,
		PackageName:    raw.PackageName,
		Token:          raw.PurchaseToken,
	}

	if err := conf.validate(); err != nil {
		return nil, NewBadReceiptError(err)
	}

	p, err := planClient.GetPlanByAppServiceID(ctx, planclient.V1GetPlanByAppServiceIDInput{
		AppServiceID: conf.SubscriptionID,
	})
	if err != nil {
		return nil, err
	}

	playStoreClient, err := getPlayStoreClient(ctx, stat, secret, mockedHttpClient)
	if err != nil {
		return nil, err
	}

	r := PlayStore{
		log:             request.GetFromContext(ctx).GetLoggingEntry(),
		stats:           stat,
		playStoreClient: playStoreClient,
	}

	if p.OneTime {
		r.Data.onetime = true
	}

	// try origin
	err = r.download(ctx, conf)
	if err != nil {
		return nil, err
	}

	err = r.getSubscriptionOffer(ctx, conf.PackageName, r.Data.Subscription)
	if err != nil {
		return nil, err
	}

	return &r, nil
}

// UnExpired will check to see if the receipt is still valid
// @link https://developers.google.com/android-publisher/api-ref/purchases/subscriptions
func (r *PlayStore) UnExpired() (interface{}, error) {
	// Check for One-Time purchase
	if r.Data.onetime {
		// Check for consumption

		if r.Data.Purchase.ConsumptionState == 1 {
			return nil, fmt.Errorf("transaction already consumed")
		}

		switch r.Data.Purchase.PurchaseState {
		case 0: // purchased
			return r.Data, nil
		case 1: // canceled
			return nil, fmt.Errorf("transaction purchase canceled")
		case 2: // pending
			return nil, fmt.Errorf("transaction purchase still pending")
		default:
			return nil, fmt.Errorf("transaction purchase state unknown")
		}
	}

	if r.Data.Subscription.CanceledStateContext != nil &&
		r.Data.Subscription.CanceledStateContext.UserInitiatedCancellation != nil {
		r.log.Infof("transaction was cancelled by user on %s, but entitlements are provided until cancellation period that is on %d",
			r.Data.Subscription.CanceledStateContext.UserInitiatedCancellation.CancelTime, r.Expires())
	}

	now := time.Now().UnixNano() / 1e6
	if r.Expires() < now {
		return nil, fmt.Errorf("transaction had expired on %d", r.Expires())
	}

	return r.Data, nil
}

func (r *PlayStore) Expires() int64 {
	if len(r.Data.Subscription.LineItems) == 0 {
		return 0
	}

	expires, err := time.Parse(time.RFC3339, r.Data.Subscription.LineItems[0].ExpiryTime)
	if err != nil {
		return 0
	}

	return expires.UnixMilli()
}

func (r *PlayStore) GetData() interface{} {
	return r.Data
}

// getPlayStoreClient returns a new instance of the playStore client based on a secret (service account) and a http client
func getPlayStoreClient(ctx context.Context, stat Stats, secret string, httpClient *http.Client) (*playstore.Client, error) {
	var err error

	_, span := stat.StartMethodSpan(ctx, "playStore.getPlayStoreClient")
	defer func() { span.FinishWithError(err) }()

	// Decode secret
	b, err := base64.StdEncoding.DecodeString(secret)
	if err != nil {
		return nil, err
	}

	if httpClient == nil {
		return playstore.New(b)
	}

	return playstore.NewWithClient(b, httpClient)
}

func (r *PlayStore) download(ctx context.Context, conf PlayStoreConfig) error {
	var err error
	var span stats.Span

	_, span = r.stats.StartMethodSpan(ctx, "playStore.Download")
	defer func() { span.FinishWithError(err) }()

	span.SetTag("input.packageName", conf.PackageName)
	span.SetTag("input.SubscriptionId", conf.SubscriptionID)

	// Check for One-Time Purchase plan
	if r.Data.onetime {
		resp, err := r.playStoreClient.VerifyProduct(ctx, conf.PackageName, conf.SubscriptionID, conf.Token)
		if err != nil {
			return err
		}
		r.Data.Purchase = *resp
		return nil
	}
	resp, err := r.playStoreClient.VerifySubscriptionV2(ctx, conf.PackageName, conf.Token)
	if err != nil {
		return err
	}
	r.Data.Subscription = *resp
	return nil
}

func (r *PlayStore) getSubscriptionOffer(ctx context.Context, packageName string, subscription androidpublisher.SubscriptionPurchaseV2) error {
	if len(subscription.LineItems) == 0 ||
		subscription.LineItems[0].OfferDetails == nil ||
		subscription.LineItems[0].OfferDetails.OfferId == "" {
		// There is no offer for the subscription
		return nil
	}

	var err error
	var span stats.Span

	_, span = r.stats.StartMethodSpan(ctx, "playStore.GetSubscriptionOffer")
	defer func() { span.FinishWithError(err) }()

	productID := subscription.LineItems[0].ProductId
	offerDetails := subscription.LineItems[0].OfferDetails
	resp, err := r.playStoreClient.GetSubscriptionOffer(ctx, packageName, productID, offerDetails.BasePlanId, offerDetails.OfferId)
	if err != nil {
		return err
	}

	r.Data.SubscriptionOffer = resp
	return nil
}

// calculateAmount calculates the amount to be charged for the subscription. It takes into account the free trials offers
// and introductory pricing offers.
func calculateAmount(recurringPricing int64, orderID string, subscriptionOffer *androidpublisher.SubscriptionOffer) int64 {
	if subscriptionOffer == nil || len(subscriptionOffer.Phases) != 1 {
		return recurringPricing
	}

	phase := subscriptionOffer.Phases[0]
	regionalConfig := getRegionalConfig(phase)
	if regionalConfig == nil {
		return recurringPricing
	}

	renewalCycle := GetRenewalCycle(orderID)
	switch {
	case regionalConfig.Free != nil:
		return handleFreeTrial(recurringPricing, renewalCycle)
	case regionalConfig.Price != nil:
		return handleFixedPrice(recurringPricing, regionalConfig.Price, phase.RecurrenceCount, renewalCycle)
	case regionalConfig.AbsoluteDiscount != nil:
		return handleAbsoluteDiscount(recurringPricing, regionalConfig.AbsoluteDiscount, phase.RecurrenceCount, renewalCycle)
	case regionalConfig.RelativeDiscount > 0:
		return handleRelativeDiscount(recurringPricing, regionalConfig.RelativeDiscount, phase.RecurrenceCount, renewalCycle)
	default:
		return recurringPricing
	}
}

// getRegionalConfig returns the regional config for the US region. If no config is found, it returns nil.
func getRegionalConfig(phase *androidpublisher.SubscriptionOfferPhase) *androidpublisher.RegionalSubscriptionOfferPhaseConfig {
	for _, rConfig := range phase.RegionalConfigs {
		if rConfig.RegionCode == UnitedStatesRegionCode {
			return rConfig
		}
	}
	return nil
}

// handleFreeTrial calculates the price after applying a free trial. The free trial is applied only if the renewal cycle
// is -1. If that's not the case, the original price is returned.
// renewalCycle is the current renewal cycle. -1 means the subscription is new.
func handleFreeTrial(recurringPricing int64, renewalCycle int64) int64 {
	if renewalCycle == -1 {
		return 0
	}
	return recurringPricing
}

// handleFixedPrice calculates the price after applying a fixed price (aka absolute amount). The fixed price is applied
// only if the renewal cycle is less than the recurrence count. If that's not the case, the original price is returned.
// price is absolute price the user pays (overriding the original price)
// recurrenceCount is the total number of recurrences for the subscriptionOffer.
// renewalCycle is the current renewal cycle. It starts at -1, that's the reason for renewalCycle+1
func handleFixedPrice(recurringPricing int64, price *androidpublisher.Money, recurrenceCount, renewalCycle int64) int64 {
	if renewalCycle+1 < recurrenceCount {
		return moneyToCents(price)
	}
	return recurringPricing
}

// handleAbsoluteDiscount calculates the price after applying an absolute discount (aka fixed discount). The discount is
// applied only if the renewal cycle is less than the recurrence count. If that's not the case, the original price is returned.
// discount is the amount of money subtracted from the original price
// recurrenceCount is the total number of recurrences for the subscriptionOffer.
// renewalCycle is the current renewal cycle. It starts at -1, that's the reason for renewalCycle+1
func handleAbsoluteDiscount(recurringPricing int64, discount *androidpublisher.Money, recurrenceCount, renewalCycle int64) int64 {
	if renewalCycle+1 < recurrenceCount {
		return recurringPricing - moneyToCents(discount)
	}
	return recurringPricing
}

// handleRelativeDiscount calculates the price after applying a relative discount (aka percentage discount). The discount
// is applied only if the renewal cycle is less than the recurrence count. If that's not the case, the original price
// is returned.
// relativeDiscount is a float64 representing the percentage discount (e.g., 0.2 for 20%). The value is strictly larger
// than 0 and strictly smaller than 1.
// recurrenceCount is the total number of recurrences for the subscriptionOffer.
// renewalCycle is the current renewal cycle. It starts at -1, that's the reason for renewalCycle+1
func handleRelativeDiscount(recurringPricing int64, relativeDiscount float64, recurrenceCount, renewalCycle int64) int64 {
	if renewalCycle+1 < recurrenceCount {
		discountCents := int64(math.Round(float64(recurringPricing) * relativeDiscount))
		return recurringPricing - discountCents
	}
	return recurringPricing
}

// GetRenewalCycle returns the renewal cycle from the orderID. If the orderId has no cycle suffix, it returns -1. Ex.:
// - No renewal yet: GPA.1234-1234-1234-12345 - renewal cycle: -1
// - First renewal:  GPA.1234-1234-1234-12345..0 - renewal cycle: 0
// - Second renewal: GPA.1234-1234-1234-12345..1 - renewal cycle: 1
func GetRenewalCycle(orderId string) int64 {
	parts := strings.Split(orderId, "..")
	if len(parts) == 2 {
		cycle, err := strconv.Atoi(parts[1])
		if err == nil {
			return int64(cycle)
		}
	}
	return -1
}

// StripOrderIDSuffix removes the "..##" suffix from the orderID, if exists.
func StripOrderIDSuffix(orderID string) string {
	parts := strings.SplitN(orderID, "..", 2)
	return parts[0]
}
