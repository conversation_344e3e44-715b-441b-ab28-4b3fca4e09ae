package receipt

import (
	"context"
	"errors"
	"testing"

	mockreceiptverify "github.com/foxcorp-product/commerce-receiptverify/mocks/mockgen"
	"github.com/foxcorp-product/entitlement-sdk/logger"
	"github.com/foxcorp-product/entitlement-sdk/stats"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"google.golang.org/api/androidpublisher/v3"
)

func TestDownload(t *testing.T) {
	type downloadMocks struct {
		MockStats           *mockreceiptverify.MockStats
		MockPlayStoreClient *mockreceiptverify.MockIPlayStoreClient
	}

	l, _ := logger.New()

	tests := map[string]struct {
		mocks    func(downloadMocks)
		conf     PlayStoreConfig
		onetime  bool
		expected func(*testing.T, PlayStoreData, error)
	}{
		"VerifyProduct returns an error": {
			mocks: func(m downloadMocks) {
				m.MockStats.EXPECT().StartMethodSpan(gomock.Any(), gomock.Eq("playStore.Download")).
					Return(context.Background(), stats.Span{})
				m.MockPlayStoreClient.EXPECT().
					VerifyProduct(gomock.Any(), gomock.Eq("testPackageName"), gomock.Eq("testSubscriptionId"), gomock.Eq("testToken")).
					Return(nil, errors.New("something went wrong"))
			},
			onetime: true,
			conf: PlayStoreConfig{
				Token:          "testToken",
				PackageName:    "testPackageName",
				SubscriptionID: "testSubscriptionId",
			},
			expected: func(t *testing.T, data PlayStoreData, err error) {
				require.Error(t, err)
				assert.Equal(t, "something went wrong", err.Error())
				assert.Empty(t, data.Purchase.OrderId)
			},
		},
		"VerifyProduct works OK": {
			mocks: func(m downloadMocks) {
				m.MockStats.EXPECT().StartMethodSpan(gomock.Any(), gomock.Eq("playStore.Download")).
					Return(context.Background(), stats.Span{})
				m.MockPlayStoreClient.EXPECT().
					VerifyProduct(gomock.Any(), gomock.Eq("testPackageName"), gomock.Eq("testSubscriptionId"), gomock.Eq("testToken")).
					Return(&androidpublisher.ProductPurchase{
						OrderId: "testOrderId",
					}, nil)
			},
			onetime: true,
			conf: PlayStoreConfig{
				Token:          "testToken",
				PackageName:    "testPackageName",
				SubscriptionID: "testSubscriptionId",
			},
			expected: func(t *testing.T, data PlayStoreData, err error) {
				assert.NoError(t, err)
				assert.Equal(t, "testOrderId", data.Purchase.OrderId)
			},
		},
		"VerifySubscriptionV2 returns an error": {
			mocks: func(m downloadMocks) {
				m.MockStats.EXPECT().StartMethodSpan(gomock.Any(), gomock.Eq("playStore.Download")).
					Return(context.Background(), stats.Span{})
				m.MockPlayStoreClient.EXPECT().
					VerifySubscriptionV2(gomock.Any(), gomock.Eq("testPackageName"), gomock.Eq("testToken")).
					Return(nil, errors.New("something went wrong"))
			},
			conf: PlayStoreConfig{
				Token:          "testToken",
				PackageName:    "testPackageName",
				SubscriptionID: "testSubscriptionId",
			},
			expected: func(t *testing.T, data PlayStoreData, err error) {
				require.Error(t, err)
				assert.Equal(t, "something went wrong", err.Error())
				assert.Empty(t, data.Subscription.LatestOrderId)
			},
		},
		"VerifySubscriptionV2 works OK": {
			mocks: func(m downloadMocks) {
				m.MockStats.EXPECT().StartMethodSpan(gomock.Any(), gomock.Eq("playStore.Download")).
					Return(context.Background(), stats.Span{})
				m.MockPlayStoreClient.EXPECT().
					VerifySubscriptionV2(gomock.Any(), gomock.Eq("testPackageName"), gomock.Eq("testToken")).
					Return(&androidpublisher.SubscriptionPurchaseV2{
						LatestOrderId: "testLatestOrderId",
					}, nil)
			},
			conf: PlayStoreConfig{
				Token:          "testToken",
				PackageName:    "testPackageName",
				SubscriptionID: "testSubscriptionId",
			},
			expected: func(t *testing.T, data PlayStoreData, err error) {
				assert.NoError(t, err)
				assert.Equal(t, "testLatestOrderId", data.Subscription.LatestOrderId)
			},
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			// Mocks creation
			ctrl := gomock.NewController(t)
			m := downloadMocks{
				MockStats:           mockreceiptverify.NewMockStats(ctrl),
				MockPlayStoreClient: mockreceiptverify.NewMockIPlayStoreClient(ctrl),
			}
			tt.mocks(m)

			playStore := PlayStore{
				Data: PlayStoreData{
					onetime: tt.onetime,
				},
				stats:           m.MockStats,
				playStoreClient: m.MockPlayStoreClient,
				log:             l,
			}

			// Method call
			err := playStore.download(context.Background(), tt.conf)

			// Assertions
			tt.expected(t, playStore.Data, err)
		})
	}
}

func TestGetSubscriptionOffer(t *testing.T) {
	type getSubscriptionOfferMocks struct {
		MockStats           *mockreceiptverify.MockStats
		MockPlayStoreClient *mockreceiptverify.MockIPlayStoreClient
	}

	l, _ := logger.New()

	tests := map[string]struct {
		mocks        func(getSubscriptionOfferMocks)
		subscription androidpublisher.SubscriptionPurchaseV2
		expected     func(*testing.T, PlayStoreData, error)
	}{
		"subscription has no LineItems": {
			mocks: func(_ getSubscriptionOfferMocks) {},
			subscription: androidpublisher.SubscriptionPurchaseV2{
				LineItems: nil,
			},
			expected: func(t *testing.T, data PlayStoreData, err error) {
				assert.NoError(t, err)
				assert.Nil(t, data.SubscriptionOffer)
			},
		},
		"subscription has no OfferDetails": {
			mocks: func(_ getSubscriptionOfferMocks) {},
			subscription: androidpublisher.SubscriptionPurchaseV2{
				LineItems: []*androidpublisher.SubscriptionPurchaseLineItem{
					{},
				},
			},
			expected: func(t *testing.T, data PlayStoreData, err error) {
				assert.NoError(t, err)
				assert.Nil(t, data.SubscriptionOffer)
			},
		},
		"subscription has no OfferId": {
			mocks: func(_ getSubscriptionOfferMocks) {},
			subscription: androidpublisher.SubscriptionPurchaseV2{
				LineItems: []*androidpublisher.SubscriptionPurchaseLineItem{
					{
						OfferDetails: &androidpublisher.OfferDetails{
							OfferId: "",
						},
					},
				},
			},
			expected: func(t *testing.T, data PlayStoreData, err error) {
				assert.NoError(t, err)
				assert.Nil(t, data.SubscriptionOffer)
			},
		},
		"GetSubscriptionOffer returns an error": {
			mocks: func(m getSubscriptionOfferMocks) {
				m.MockStats.EXPECT().StartMethodSpan(gomock.Any(), gomock.Eq("playStore.GetSubscriptionOffer")).
					Return(context.Background(), stats.Span{})
				m.MockPlayStoreClient.EXPECT().
					GetSubscriptionOffer(gomock.Any(), gomock.Eq("testPackageName"), gomock.Eq("testProductId"), gomock.Eq("testBasePlanId"), gomock.Eq("testOfferId")).
					Return(nil, errors.New("something went wrong"))
			},
			subscription: androidpublisher.SubscriptionPurchaseV2{
				LineItems: []*androidpublisher.SubscriptionPurchaseLineItem{
					{
						ProductId: "testProductId",
						OfferDetails: &androidpublisher.OfferDetails{
							BasePlanId: "testBasePlanId",
							OfferId:    "testOfferId",
						},
					},
				},
			},
			expected: func(t *testing.T, data PlayStoreData, err error) {
				require.Error(t, err)
				assert.Equal(t, "something went wrong", err.Error())
				assert.Nil(t, data.SubscriptionOffer)
			},
		},
		"GetSubscriptionOffer works OK": {
			mocks: func(m getSubscriptionOfferMocks) {
				m.MockStats.EXPECT().StartMethodSpan(gomock.Any(), gomock.Eq("playStore.GetSubscriptionOffer")).
					Return(context.Background(), stats.Span{})
				m.MockPlayStoreClient.EXPECT().
					GetSubscriptionOffer(gomock.Any(), gomock.Eq("testPackageName"), gomock.Eq("testProductId"), gomock.Eq("testBasePlanId"), gomock.Eq("testOfferId")).
					Return(&androidpublisher.SubscriptionOffer{
						BasePlanId:  "testBasePlanId",
						OfferId:     "testOfferId",
						PackageName: "testPackageName",
						State:       "ACTIVE",
					}, nil)
			},
			subscription: androidpublisher.SubscriptionPurchaseV2{
				LineItems: []*androidpublisher.SubscriptionPurchaseLineItem{
					{
						ProductId: "testProductId",
						OfferDetails: &androidpublisher.OfferDetails{
							BasePlanId: "testBasePlanId",
							OfferId:    "testOfferId",
						},
					},
				},
			},
			expected: func(t *testing.T, data PlayStoreData, err error) {
				assert.NoError(t, err)
				assert.NotNil(t, data.SubscriptionOffer)
			},
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			// Mocks creation
			ctrl := gomock.NewController(t)
			m := getSubscriptionOfferMocks{
				MockStats:           mockreceiptverify.NewMockStats(ctrl),
				MockPlayStoreClient: mockreceiptverify.NewMockIPlayStoreClient(ctrl),
			}
			tt.mocks(m)

			playStore := PlayStore{
				stats:           m.MockStats,
				playStoreClient: m.MockPlayStoreClient,
				log:             l,
			}

			// Method call
			err := playStore.getSubscriptionOffer(context.Background(), "testPackageName", tt.subscription)

			// Assertions
			tt.expected(t, playStore.Data, err)
		})
	}
}

func TestNewPlayStoreResponse(t *testing.T) {
	orderID := "GPA.1234-5678-9012-34567"
	tests := map[string]struct {
		input    PlayStoreData
		expected PlayStoreResponse
	}{
		"One time purchase": {
			input: PlayStoreData{
				onetime: true,
				Purchase: androidpublisher.ProductPurchase{
					OrderId:            orderID,
					ProductId:          "testProductID",
					PurchaseTimeMillis: 1738847441990,
				},
			},
			expected: PlayStoreResponse{
				OrderId:      orderID,
				ProductID:    "testProductID",
				PurchaseDate: 1738847441990,
				OneTime:      true,
			},
		},
		"Subscription": {
			input: PlayStoreData{
				Subscription: androidpublisher.SubscriptionPurchaseV2{
					StartTime:     "2025-02-05T22:10:55.381Z",
					LatestOrderId: orderID,
					LineItems: []*androidpublisher.SubscriptionPurchaseLineItem{
						{
							AutoRenewingPlan: &androidpublisher.AutoRenewingPlan{
								AutoRenewEnabled: true,
								RecurringPrice: &androidpublisher.Money{
									CurrencyCode: "USD",
									Nanos:        990000000,
									Units:        2,
								},
							},
							ExpiryTime: "2025-03-05T22:10:55.381Z",
						},
					},
					CanceledStateContext: &androidpublisher.CanceledStateContext{
						UserInitiatedCancellation: &androidpublisher.UserInitiatedCancellation{},
					},
					PausedStateContext: &androidpublisher.PausedStateContext{
						AutoResumeTime: "2025-03-05T22:10:55.381Z",
					},
				},
			},
			expected: PlayStoreResponse{
				OrderId:                   orderID,
				AutoRenewing:              true,
				PurchaseDate:              1738793455381,
				ExpiryDate:                1741212655381,
				ResumeAtDate:              "2025-03-05T22:10:55.381Z",
				OneTime:                   false,
				IsFreeTrial:               false,
				UserInitiatedCancellation: true,
				LinkedPurchaseToken:       "",
				Amount:                    2.99,
				Currency:                  "USD",
			},
		},
		"Subscription - free trial": {
			input: PlayStoreData{
				Subscription: androidpublisher.SubscriptionPurchaseV2{
					StartTime:     "2025-02-05T22:10:55.381Z",
					LatestOrderId: orderID,
					LineItems: []*androidpublisher.SubscriptionPurchaseLineItem{
						{
							AutoRenewingPlan: &androidpublisher.AutoRenewingPlan{
								AutoRenewEnabled: true,
								RecurringPrice: &androidpublisher.Money{
									CurrencyCode: "USD",
									Nanos:        990000000,
									Units:        2,
								},
							},
							ExpiryTime: "2025-03-05T22:10:55.381Z",
						},
					},
				},
				SubscriptionOffer: &androidpublisher.SubscriptionOffer{
					Phases: []*androidpublisher.SubscriptionOfferPhase{
						{
							RegionalConfigs: []*androidpublisher.RegionalSubscriptionOfferPhaseConfig{
								{
									RegionCode: UnitedStatesRegionCode,
									Free:       &androidpublisher.RegionalSubscriptionOfferPhaseFreePriceOverride{},
								},
							},
						},
					},
				},
			},
			expected: PlayStoreResponse{
				OrderId:                orderID,
				AutoRenewing:           true,
				PurchaseDate:           1738793455381,
				ExpiryDate:             1741212655381,
				OneTime:                false,
				IsFreeTrial:            true,
				Amount:                 0,
				Currency:               "USD",
				IsFlexibleOfferApplied: true,
			},
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			res := NewPlayStoreResponse(context.Background(), tt.input)

			assert.Equal(t, tt.expected, res)
		})
	}
}

func TestMoneyToCents(t *testing.T) {
	tests := map[string]struct {
		input          *androidpublisher.Money
		expectedAmount int64
	}{
		"moneyToAmountAndCurrency 1": {
			input: &androidpublisher.Money{
				CurrencyCode: "USD",
				Nanos:        990000000,
				Units:        2,
			},
			expectedAmount: 299,
		},
		"moneyToAmountAndCurrency 2": {
			input: &androidpublisher.Money{
				CurrencyCode: "USD",
				Nanos:        000000000,
				Units:        3,
			},
			expectedAmount: 300,
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			assert.Equal(t, tt.expectedAmount, moneyToCents(tt.input))
		})
	}
}

func TestStripOrderIDSuffix(t *testing.T) {
	tests := map[string]struct {
		input    string
		expected string
	}{
		"orderID with suffix": {
			input:    "GPA.1234-5678-9012-34567..0",
			expected: "GPA.1234-5678-9012-34567",
		},
		"orderID without suffix": {
			input:    "GPA.1234-5678-9012-34567",
			expected: "GPA.1234-5678-9012-34567",
		},
		"empty input": {
			input:    "",
			expected: "",
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			result := StripOrderIDSuffix(tt.input)
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestCalculateAmount(t *testing.T) {
	recurringPricing := int64(999)
	orderID := "GPA.1234-1234-1234-12345"
	price := &androidpublisher.Money{
		Units: 3,
		Nanos: 550000000,
	}

	tests := map[string]struct {
		subscriptionOffer *androidpublisher.SubscriptionOffer
		expected          int64
	}{
		"No subscription offer": {
			subscriptionOffer: nil,
			expected:          recurringPricing,
		},
		"Subscription offer contains more than one phase": {
			subscriptionOffer: &androidpublisher.SubscriptionOffer{
				Phases: []*androidpublisher.SubscriptionOfferPhase{
					{},
					{},
				},
			},
			expected: recurringPricing,
		},
		"Invalid regional config": {
			subscriptionOffer: &androidpublisher.SubscriptionOffer{
				Phases: []*androidpublisher.SubscriptionOfferPhase{
					{
						RegionalConfigs: []*androidpublisher.RegionalSubscriptionOfferPhaseConfig{
							{
								RegionCode: "AE",
							},
						},
					},
				},
			},
			expected: recurringPricing,
		},
		"Free trial offer": {
			subscriptionOffer: &androidpublisher.SubscriptionOffer{
				Phases: []*androidpublisher.SubscriptionOfferPhase{
					{
						RegionalConfigs: []*androidpublisher.RegionalSubscriptionOfferPhaseConfig{
							{
								RegionCode: UnitedStatesRegionCode,
								Free:       &androidpublisher.RegionalSubscriptionOfferPhaseFreePriceOverride{},
							},
						},
					},
				},
			},
			expected: 0,
		},
		"Fixed price offer": {
			subscriptionOffer: &androidpublisher.SubscriptionOffer{
				Phases: []*androidpublisher.SubscriptionOfferPhase{
					{
						RegionalConfigs: []*androidpublisher.RegionalSubscriptionOfferPhaseConfig{
							{
								RegionCode: UnitedStatesRegionCode,
								Price:      price,
							},
						},
						RecurrenceCount: 1,
					},
				},
			},
			expected: 355,
		},
		"Absolute discount offer": {
			subscriptionOffer: &androidpublisher.SubscriptionOffer{
				Phases: []*androidpublisher.SubscriptionOfferPhase{
					{
						RegionalConfigs: []*androidpublisher.RegionalSubscriptionOfferPhaseConfig{
							{
								RegionCode:       UnitedStatesRegionCode,
								AbsoluteDiscount: price,
							},
						},
						RecurrenceCount: 1,
					},
				},
			},
			expected: 644,
		},
		"Relative discount offer": {
			subscriptionOffer: &androidpublisher.SubscriptionOffer{
				Phases: []*androidpublisher.SubscriptionOfferPhase{
					{
						RegionalConfigs: []*androidpublisher.RegionalSubscriptionOfferPhaseConfig{
							{
								RegionCode:       UnitedStatesRegionCode,
								RelativeDiscount: 0.2,
							},
						},
						RecurrenceCount: 1,
					},
				},
			},
			expected: 799,
		},
		"Phase has configuration (should never happen)": {
			subscriptionOffer: &androidpublisher.SubscriptionOffer{
				Phases: []*androidpublisher.SubscriptionOfferPhase{
					{
						RegionalConfigs: []*androidpublisher.RegionalSubscriptionOfferPhaseConfig{
							{
								RegionCode: UnitedStatesRegionCode,
							},
						},
					},
				},
			},
			expected: 999,
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			result := calculateAmount(recurringPricing, orderID, tt.subscriptionOffer)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetRegionalConfig(t *testing.T) {
	tests := map[string]struct {
		input    *androidpublisher.SubscriptionOfferPhase
		expected *androidpublisher.RegionalSubscriptionOfferPhaseConfig
	}{
		"Valid region config found": {
			input: &androidpublisher.SubscriptionOfferPhase{
				RegionalConfigs: []*androidpublisher.RegionalSubscriptionOfferPhaseConfig{
					{
						RegionCode: "US",
						Price: &androidpublisher.Money{
							CurrencyCode: "USD",
							Units:        10,
						},
					},
				},
			},
			expected: &androidpublisher.RegionalSubscriptionOfferPhaseConfig{
				RegionCode: "US",
				Price: &androidpublisher.Money{
					CurrencyCode: "USD",
					Units:        10,
				},
			},
		},
		"No matching region config": {
			input: &androidpublisher.SubscriptionOfferPhase{
				RegionalConfigs: []*androidpublisher.RegionalSubscriptionOfferPhaseConfig{
					{
						RegionCode: "AE",
						Price: &androidpublisher.Money{
							CurrencyCode: "AED",
							Units:        20,
						},
					},
				},
			},
			expected: nil,
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			result := getRegionalConfig(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestHandleFreeTrial(t *testing.T) {
	recurringPricing := int64(999)
	tests := map[string]struct {
		renewalCycle int64
		expected     int64
	}{
		"Free trial (renewal cycle -1)": {
			renewalCycle: -1,
			expected:     0,
		},
		"Regular pricing (renewal cycle 0)": {
			renewalCycle: 0,
			expected:     999,
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			result := handleFreeTrial(recurringPricing, tt.renewalCycle)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestHandleFixedPrice(t *testing.T) {
	recurringPricing := int64(999)
	fixedPrice := &androidpublisher.Money{
		Units: 5,
		Nanos: 500000000,
	}
	tests := map[string]struct {
		price           *androidpublisher.Money
		recurrenceCount int64
		renewalCycle    int64
		expected        int64
	}{
		"Single payment (renewal cycle within recurrence count)": {
			price:           fixedPrice,
			recurrenceCount: 1,
			renewalCycle:    -1,
			expected:        550,
		},
		"Single payment (renewal cycle exceeds recurrence count)": {
			price:           fixedPrice,
			recurrenceCount: 1,
			renewalCycle:    0,
			expected:        999,
		},
		"Recurring payment (renewal cycle within recurrence count)": {
			price:           fixedPrice,
			recurrenceCount: 2,
			renewalCycle:    0,
			expected:        550,
		},
		"Recurring payment (renewal cycle exceeds recurrence count)": {
			price:           fixedPrice,
			recurrenceCount: 2,
			renewalCycle:    2,
			expected:        999,
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			result := handleFixedPrice(recurringPricing, tt.price, tt.recurrenceCount, tt.renewalCycle)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestHandleAbsoluteDiscount(t *testing.T) {
	recurringPricing := int64(999)
	discount := &androidpublisher.Money{
		Units: 3,
		Nanos: 000000000,
	}
	tests := map[string]struct {
		absoluteDiscount *androidpublisher.Money
		recurrenceCount  int64
		renewalCycle     int64
		expected         int64
	}{
		"Single payment (renewal cycle within recurrence count)": {
			absoluteDiscount: discount,
			recurrenceCount:  1,
			renewalCycle:     -1,
			expected:         699,
		},
		"Single payment (renewal cycle exceeds recurrence count)": {
			absoluteDiscount: discount,
			recurrenceCount:  1,
			renewalCycle:     0,
			expected:         999,
		},
		"Recurring payment (renewal cycle within recurrence count)": {
			absoluteDiscount: discount,
			recurrenceCount:  2,
			renewalCycle:     0,
			expected:         699,
		},
		"Recurring payment (renewal cycle exceeds recurrence count)": {
			absoluteDiscount: discount,
			recurrenceCount:  2,
			renewalCycle:     2,
			expected:         999,
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			result := handleAbsoluteDiscount(recurringPricing, tt.absoluteDiscount, tt.recurrenceCount, tt.renewalCycle)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestHandleRelativeDiscount(t *testing.T) {
	recurringPricing := int64(1000)
	discount := 0.2
	tests := map[string]struct {
		relativeDiscount float64
		recurrenceCount  int64
		renewalCycle     int64
		expected         int64
	}{
		"Single payment (renewal cycle within recurrence count)": {
			relativeDiscount: discount,
			recurrenceCount:  1,
			renewalCycle:     -1,
			expected:         800,
		},
		"Single payment (renewal cycle exceeds recurrence count)": {
			relativeDiscount: discount,
			recurrenceCount:  1,
			renewalCycle:     0,
			expected:         1000,
		},
		"Recurring payment (renewal cycle within recurrence count)": {
			relativeDiscount: discount,
			recurrenceCount:  2,
			renewalCycle:     0,
			expected:         800,
		},
		"Recurring payment (renewal cycle exceeds recurrence count)": {
			relativeDiscount: discount,
			recurrenceCount:  2,
			renewalCycle:     2,
			expected:         1000,
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			result := handleRelativeDiscount(recurringPricing, tt.relativeDiscount, tt.recurrenceCount, tt.renewalCycle)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetRenewalCycle(t *testing.T) {
	tests := map[string]struct {
		orderId  string
		expected int64
	}{
		"No renewal cycle suffix": {
			orderId:  "GPA.1234-1234-1234-12345",
			expected: -1,
		},
		"First renewal cycle": {
			orderId:  "GPA.1234-1234-1234-12345..0",
			expected: 0,
		},
		"Second renewal cycle": {
			orderId:  "GPA.1234-1234-1234-12345..1",
			expected: 1,
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			result := GetRenewalCycle(tt.orderId)
			assert.Equal(t, tt.expected, result)
		})
	}
}
