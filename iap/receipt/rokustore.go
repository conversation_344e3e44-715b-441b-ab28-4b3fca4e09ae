package receipt

import (
	"bytes"
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/foxcorp-product/entitlement-sdk/circuitbreaker"
	"github.com/foxcorp-product/entitlement-sdk/logger"
	"github.com/foxcorp-product/entitlement-sdk/request"
	"github.com/foxcorp-product/entitlement-sdk/stats"
)

const (
	rokuValidateTransaction = "validate-transaction"
	rokuTransactionService  = "listen/transaction-service.svc"
	RokuURL                 = "https://apipub.roku.com"
)

// Data format received as a unique .NET Serialized JSON attribute.
// Example: "/Date(62135596800000+0000)/", So we to extract the UNIX time from it
var rokuDateReg = regexp.MustCompile("[^0-9]+")

type RokuResponse struct {
	ErrorCode               interface{} `json:"errorCode"`
	ErrorDetails            interface{} `json:"errorDetails"`
	ErrorMessage            string      `json:"errorMessage"`
	Status                  int64       `json:"status"`
	OriginalTransactionID   string      `json:"OriginalTransactionId"`
	Amount                  float64     `json:"amount"`
	Cancelled               bool        `json:"cancelled"`
	CancelledTransactionIds []string    `json:"cancelledTransactionIds"`
	ChannelID               int         `json:"channelId"`
	ChannelName             string      `json:"channelName"`
	CouponCode              interface{} `json:"couponCode"`
	Currency                string      `json:"currency"`
	ExpirationDate          string      `json:"expirationDate"`
	IsEntitled              bool        `json:"isEntitled"`
	OriginalPurchaseDate    string      `json:"originalPurchaseDate"`
	PartnerReferenceID      interface{} `json:"partnerReferenceId"`
	ProductID               string      `json:"productId"`
	ProductName             string      `json:"productName"`
	PurchaseDate            string      `json:"purchaseDate"`
	PurchaseStatus          string      `json:"purchaseStatus"`
	PurchaseType            interface{} `json:"purchaseType"`
	Quantity                int64       `json:"quantity"`
	RokuCustomerID          string      `json:"rokuCustomerId"`
	Tax                     float64     `json:"tax"`
	Total                   float64     `json:"total"`
	TransactionID           string      `json:"transactionId"`
}

type CircuitAPI interface {
	DoWithContext(ctx context.Context, req *http.Request) (resp *http.Response, err error)
	PostWithContext(ctx context.Context, url, contentType string, body io.Reader) (resp *http.Response, err error)
}

type Stats interface {
	StartMethodSpan(ctx context.Context, resource string, opts ...stats.StartSpanOption) (context.Context, stats.Span)
	StartHTTPSpan(operationName stats.OperationName, resource string, r *http.Request, opts ...stats.StartSpanOption) (stats.Span, *http.Request)
	StartSpan(ctx context.Context, spanType string, operation stats.OperationName, resource string, opts ...stats.StartSpanOption) (context.Context, stats.Span)
	Timing(name string, value time.Duration, rate float64, tags ...string) error
	Gauge(name string, value float64, rate float64, tags ...string) error
	Incr(name string, rate float64, tags ...string) error
	Count(name string, value int64, rate float64, tags ...string) error
	TimeInMilliseconds(name string, value float64, rate float64, tags ...string) error
	WrapHTTPClient(operationName stats.OperationName, resourceProvider func(*http.Request) string, client stats.HTTPClient, opts ...stats.StartSpanOption) (stats.HTTPClient, stats.FinishFunc)
	Flush() error
}

// GetExpirationDate returns parsed expiration date
func (r *RokuResponse) GetExpirationDate() (time.Time, error) {
	return parseRokuStoreDate(r.ExpirationDate)
}

// GetPurchaseDate returns parsed purchase date
func (r *RokuResponse) GetPurchaseDate() (time.Time, error) {
	return parseRokuStoreDate(r.PurchaseDate)
}

func (r *RokuResponse) Validate() error {
	if r.ErrorMessage != "" {
		return NewUnauthorizedReceiptError(fmt.Errorf("error getting receipt: %v", r.ErrorMessage))
	}
	return nil
}

func (r *RokuResponse) IsPurchase() bool {
	// in some cases we've encountered inconsistency in formats, so normalize both ids first
	return normalizeRokuTransactionID(r.TransactionID) == normalizeRokuTransactionID(r.OriginalTransactionID)
}

func normalizeRokuTransactionID(id string) string {
	return strings.ToLower(strings.ReplaceAll(id, "-", ""))
}

type RokuStoreConfig struct {
	ReceiptID        string `json:"receiptID"`
	DecodedReceiptID string
}

func (rc *RokuStoreConfig) validate() error {
	if rc.ReceiptID == "" {
		return errors.New("receiptID is required for RokuStore Receipt")
	}

	// case roku validate transaction
	if rc.ReceiptID == rc.DecodedReceiptID {
		return nil
	}

	decodedReceiptID, err := base64.RawStdEncoding.DecodeString(rc.ReceiptID)
	if err != nil {
		rc.DecodedReceiptID = rc.ReceiptID
		return nil
	}

	if !utf8.Valid(decodedReceiptID) {
		return errors.New("receipt is not in a valid format")
	}

	rc.DecodedReceiptID = string(decodedReceiptID)

	return nil
}

// RokuStore implements Receipt interface
type RokuStoreSVC struct {
	Data       RokuResponse
	log        logger.Logger
	stats      Stats
	secret     string
	rokuClient CircuitAPI
}

// NewRokuStore creates a RokuStore
func NewRokuStore(ctx context.Context, stat Stats, secret string, conf RokuStoreConfig, rokuClient CircuitAPI) (*RokuStoreSVC, error) {
	if err := conf.validate(); err != nil {
		return nil, NewBadReceiptError(err)
	}

	r := RokuStoreSVC{
		log:        request.GetFromContext(ctx).GetLoggingEntry(),
		secret:     secret,
		stats:      stat,
		rokuClient: rokuClient,
	}

	// try origin
	err := r.download(ctx, secret, conf)
	if err != nil {
		return nil, err
	}

	if err := r.Data.Validate(); err != nil {
		return nil, err
	}

	return &r, nil
}

// UnExpired will check to see if the receipt is still valid
// @link https://developer.roku.com/docs/developer-program/roku-pay/roku-web-service.md
func (r *RokuStoreSVC) UnExpired() (interface{}, error) {
	if r.Data.Status != 0 {
		r.log.Error("Invalid transactionID", r.Data.ErrorMessage)
		return nil, errors.New("invalid transactionID")
	}

	if r.Data.PurchaseStatus == "Inactive" {
		return nil, fmt.Errorf("receipt is inactive")
	}

	// check if Expiration Date field is present in the response
	if len(r.Data.ExpirationDate) == 0 {
		return r.Data, nil
	}

	// check if receipt is expired or cancelled
	now := time.Now().UnixNano() / 1e6
	if exp := r.expires(); exp < now && r.Data.Cancelled {
		return nil, fmt.Errorf("transaction was cancelled or had expired on %d", exp)
	}

	return r.Data, nil
}

func (r *RokuStoreSVC) GetData() interface{} {
	return r.Data
}

func (r *RokuStoreSVC) expires() int64 {
	if len(r.Data.ExpirationDate) == 0 {
		return 0
	}

	expires, err := parseRokuStoreDate(r.Data.ExpirationDate)
	if err != nil {
		r.log.Error(err)
		return 0
	}

	return expires.Unix()
}

func (r *RokuStoreSVC) download(ctx context.Context, secret string, conf RokuStoreConfig) error {
	var err error
	var span stats.Span

	_, span = r.stats.StartMethodSpan(ctx, "roku.Download")
	defer func() { span.FinishWithError(err) }()
	l := request.GetFromContext(ctx).GetLoggingEntry()

	addlHeaders := http.Header{}
	addlHeaders.Add("Accept", "application/json")
	// This is the endpoint from RokuPay that validates the receipt
	// Example: https://apipub.roku.com/listen/transaction-service.svc/validate-transaction/0E8675135866AD43A5A9A7AC00CAF92A5642/A75648C5-6CFA-4097-B7F3-AC140000C2DA
	url := fmt.Sprintf("%s/%s/%s/%s/%s", RokuURL, rokuTransactionService, rokuValidateTransaction, secret, conf.DecodedReceiptID)
	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		l.Errorf("roku get receipt error: %v", err)
		return err
	}

	req.Header = addlHeaders
	res, err := r.rokuClient.DoWithContext(ctx, req)
	if err != nil {
		l.Errorf("roku get receipt error: %v", err)
		return err
	}

	if err != nil {
		return err
	}

	defer res.Body.Close()

	if res.StatusCode >= 400 {
		return fmt.Errorf("roku Store Server Error: %d", res.StatusCode)
	}

	// make sure to read the full body from stream
	if res.Body != nil {
		defer func() {
			_ = res.Body.Close()
		}()

		var body []byte
		body, err = io.ReadAll(res.Body)
		if err != nil {
			return fmt.Errorf("%s: %v", "unable to read full body", err)
		}
		res.Body = io.NopCloser(bytes.NewBuffer(body))
		// log every response from Roku
		l.WithField("receipt", string(body)).Infof("roku download response")
	}

	if err = circuitbreaker.Unmarshal(res, &r.Data); err != nil {
		l.Error("Error during Unmarshal for Roku Response ", err)
		return err
	}

	return nil
}

// parseRokuStoreDate parses the given date in "/Date(1647106139294+0000)/" format
func parseRokuStoreDate(date string) (time.Time, error) {
	dateRaw := rokuDateReg.ReplaceAllString(strings.ReplaceAll(date, "+0000", ""), "")
	timestamp, err := strconv.ParseInt(dateRaw, 10, 64)
	if err != nil {
		return time.Time{}, err
	}

	return time.Unix(0, timestamp*int64(time.Millisecond)), nil
}
