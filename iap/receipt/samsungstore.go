package receipt

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"time"

	"github.com/foxcorp-product/entitlement-sdk/logger"
	"github.com/foxcorp-product/entitlement-sdk/request"
)

const (
	samsungProxyInvoiceVerify = "/v1/samsung/receipt-verify"
	samsungProxyListPurchases = "/v1/samsung/list-purchases"
	SamsungProxyService       = "samsungproxy"
	SamsungProxyURL           = "http://samsungproxy.internal.svc.cluster.local:8080"
)

var (
	jsonMarshal           = json.Marshal
	Utc14TimeStringLayout = "20060102150405"
)

// SamsungStore Configuration
type SamsungStoreConfig struct {
	AppServiceID string `json:"appServiceId"`
	InvoiceID    string `json:"invoiceId"`
	CustomID     string `json:"customId"`
	CountryCode  string `json:"countryCode"`
}

// Requests & Response
type ListPurchasesResponse struct {
	CPStatus       string          `json:"CPStatus"`
	CPResult       string          `json:"CPResult"`
	TotalCount     int             `json:"TotalCount"`
	CheckValue     string          `json:"CheckValue"`
	InvoiceDetails []InvoiceDetail `json:"InvoiceDetails"`
}

type VerifySamsungInvoiceRequest struct {
	InvoiceID   string `json:"invoiceId"`
	CustomID    string `json:"customId"`
	CountryCode string `json:"countryCode"`
}

type ListSamsungInvoicesRequest struct {
	CustomID    string `json:"customID"`
	CountryCode string `json:"countryCode"`
	ItemType    string `json:"itemType"`
}

type SamsungVerifyResponse struct {
	CPStatus  string         `json:"CPStatus"`
	CPResult  string         `json:"CPResult"`
	InvoiceID string         `json:"InvoiceID"`
	Invoice   *InvoiceDetail `json:"Invoice"`
}

type InvoiceDetail struct {
	Seq              int              `json:"Seq"`
	InvoiceID        string           `json:"InvoiceID"`
	ItemID           string           `json:"ItemID"`
	ItemTitle        string           `json:"ItemTitle"`
	ItemType         int              `json:"ItemType"`
	OrderTime        string           `json:"OrderTime"`
	Period           int              `json:"Period"`
	Price            float64          `json:"Price"`
	OrderCurrencyID  string           `json:"OrderCurrencyID"`
	CancelStatus     bool             `json:"CancelStatus"`
	AppliedStatus    bool             `json:"AppliedStatus"`
	AppliedTime      string           `json:"AppliedTime"`
	SubscriptionInfo SubscriptionInfo `json:"SubscriptionInfo"`
}

type SubscriptionInfo struct {
	SubscriptionID         string  `json:"SubscriptionId"`
	SubsStartTime          string  `json:"SubsStartTime"`
	SubsEndTime            string  `json:"SubsEndTime"`
	SubsStatus             string  `json:"SubsStatus"`
	LastPaymentAmount      float64 `json:"LastPaymentAmount"`
	LastPaymentTime        string  `json:"LastPaymentTime"`
	NextPaymentTime        string  `json:"NextPaymentTime"`
	IsFreeTrialPeriod      bool    `json:"IsFreeTrialPeriod"`
	CountryCode            string  `json:"CountryCode"`
	FlexibleOfferPrice     string  `json:"FlexibleOfferPrice"`
	IsFlexibleOfferApplied bool    `json:"IsFlexibleOfferApplied"`
}

func (rc *SamsungStoreConfig) validate() error {
	if rc.InvoiceID == "" {
		return errors.New("param: invoiceId (string) is required for SamsungStore Invoice")
	}
	if rc.CustomID == "" {
		return errors.New("param: customId (string) is required for SamsungStore Invoice")
	}
	if rc.AppServiceID == "" {
		return errors.New("param: appServiceId (string) is required for SamsungStore Invoice")
	}

	return nil
}

// SamsungStore implements Receipt interface
type SamsungStore struct {
	Data   SamsungVerifyResponse
	log    logger.Logger
	stats  Stats
	cb     CircuitAPI
	config SamsungStoreConfig
}

// NewSamsungStore creates a SamsungStore
func NewSamsungStore(ctx context.Context, stat Stats, conf SamsungStoreConfig, cb CircuitAPI) (*SamsungStore, error) {
	if err := conf.validate(); err != nil {
		return nil, err
	}

	log := request.GetFromContext(ctx).GetLoggingEntry()

	r := SamsungStore{
		log:    log,
		config: conf,
		stats:  stat,
		cb:     cb,
	}

	if err := r.getFromOrigin(ctx); err != nil {
		return nil, err
	}

	return &r, nil
}

//nolint:all
func (r *SamsungStore) getFromOrigin(ctx context.Context) error {
	if err := r.download(ctx); err != nil {
		return err
	}

	if err := r.checkAppServiceIdFromInvoice(ctx); err != nil {
		return err
	}

	_, err := r.UnExpired()
	if err != nil {
		return err
	}

	return nil
}

//nolint:all
func (r *SamsungStore) download(ctx context.Context) error {
	conf := r.config

	body := VerifySamsungInvoiceRequest{
		InvoiceID:   conf.InvoiceID,
		CustomID:    conf.CustomID,
		CountryCode: conf.CountryCode,
	}

	b, err := json.Marshal(body)
	if err != nil {
		return err
	}

	res, err := r.cb.PostWithContext(ctx, samsungProxyInvoiceVerify, "application/json", io.NopCloser(bytes.NewBuffer(b)))
	if err != nil {
		r.log.Error("samsung-proxy invoice verify:", err)
		return err
	}

	defer res.Body.Close()

	if res.StatusCode >= 300 {
		r.log.Error("samsung-proxy Error code", res.StatusCode)
		b, err := io.ReadAll(res.Body)
		if err != nil {
			r.log.Error("samsung-proxy Error", err)
		}
		r.log.Error("samsung-proxy Error", string(b))

		return fmt.Errorf("samsung-proxy Error code %d body %s", res.StatusCode, string(b))
	}

	if err = json.NewDecoder(res.Body).Decode(&r.Data); err != nil {
		r.log.Error("Error during Unmarshal for samsung-proxy Response ", err)
		return err
	}
	// Get invoice Data
	if err = r.getInvoice(ctx); err != nil {
		r.log.Error("Error during get invoice for samsung-proxy ", err)
		return err
	}

	return nil
}

func (r *SamsungStore) getInvoice(ctx context.Context) error {
	conf := r.config

	body := ListSamsungInvoicesRequest{
		CustomID:    conf.CustomID,
		CountryCode: conf.CountryCode,
	}

	b, err := jsonMarshal(body)
	if err != nil {
		return err
	}

	res, err := r.cb.PostWithContext(ctx, samsungProxyListPurchases, "application/json", io.NopCloser(bytes.NewBuffer(b)))
	if err != nil {
		r.log.Error("samsung-proxy invoice list:", err)
		return err
	}

	defer res.Body.Close()

	if res.StatusCode >= 300 {
		r.log.Error("samsung-proxy Error code", res.StatusCode)
		b, err := io.ReadAll(res.Body)
		if err != nil {
			r.log.Error("samsung-proxy Error", err)
		}
		r.log.Error("samsung-proxy Error", string(b))

		return fmt.Errorf("samsung-proxy Error code %d body %s", res.StatusCode, string(b))
	}
	var purchases ListPurchasesResponse

	if err = json.NewDecoder(res.Body).Decode(&purchases); err != nil {
		r.log.Error("Error during Unmarshal for samsung-proxy list purchases Response ", err)
		return err
	}

	for _, purchase := range purchases.InvoiceDetails {
		if purchase.InvoiceID == conf.InvoiceID {
			r.Data.Invoice = &purchase
			return nil
		}
	}
	return errors.New("invoice not found in samsung purchase list")
}

func (r *SamsungStore) checkAppServiceIdFromInvoice(context.Context) error {
	appServiceId := r.Data.Invoice.ItemID

	if appServiceId == "" {
		return NewBadReceiptError(errors.New("appServiceId not found in invoice"))
	}

	if appServiceId != r.config.AppServiceID {
		return NewBadReceiptError(fmt.Errorf("appServiceId from invoice does not match request: %s != %s", appServiceId, r.config.AppServiceID))
	}

	return nil
}

// UnExpired will check to see if the invoice is still valid
func (r *SamsungStore) UnExpired() (interface{}, error) {
	// check if the invoice has the success code
	// “100000”: Success - refer to: https://developer.samsung.com/smarttv/develop/guides/samsung-checkout/implementing-the-purchase-process.html
	if s := r.Data.CPStatus; s != "100000" {
		return nil, fmt.Errorf("invoice status is %s", s)
	}
	// check if the invoice has the success result
	// refer to same documentation, SUCCESS is the only valid result
	if s := r.Data.CPResult; s != "SUCCESS" {
		return nil, fmt.Errorf("invoice result is %s", s)
	}

	return r.Data, nil
}

func (r *SamsungStore) GetData() interface{} {
	return r.Data
}

func (r *SamsungVerifyResponse) GetStartDate() (time.Time, error) {
	i := r.Invoice
	if i == nil {
		return time.Time{}, errors.New("invoice not found")
	}
	s := i.SubscriptionInfo

	// startDate is a string in 14-digit UTC time format
	startDate := s.SubsStartTime

	// convert startDate to time.Time
	t, err := time.Parse(Utc14TimeStringLayout, startDate)
	if err != nil {
		return time.Time{}, err
	}

	return t, nil
}

func (r *SamsungVerifyResponse) GetEndDate() (time.Time, error) {
	i := r.Invoice
	if i == nil {
		return time.Time{}, errors.New("invoice not found")
	}
	s := i.SubscriptionInfo

	// endDate is a string in 14-digit UTC time format
	endDate := s.NextPaymentTime

	// convert endDate to time.Time
	t, err := time.Parse(Utc14TimeStringLayout, endDate)
	if err != nil {
		return time.Time{}, err
	}
	if s.SubsStatus == "00" {
		t2 := t.AddDate(0, 0, 1) // add 1 days as buffer if subscription status is active:00
		return t2, nil
	}
	return t, nil
}

func (r *SamsungVerifyResponse) GetNextPaymentDate() (time.Time, error) {
	i := r.Invoice
	if i == nil {
		return time.Time{}, errors.New("invoice not found")
	}
	s := i.SubscriptionInfo

	// nextPaymentDate is a string in 14-digit UTC time format
	nextPaymentDate := s.NextPaymentTime

	// convert nextPaymentDate to time.Time
	t, err := time.Parse(Utc14TimeStringLayout, nextPaymentDate)
	if err != nil {
		return time.Time{}, err
	}

	return t, nil
}
