package receipt

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/foxcorp-product/commerce-receiptverify/internal/stripeproxy"
	"github.com/foxcorp-product/entitlement-sdk/logger"
	"github.com/foxcorp-product/entitlement-sdk/request"

	"github.com/stripe/stripe-go/v74"
)

const (
	stripeProxyRetrieveIntent = "/v1/stripe/retrieve-payment-intent"
	StripeProxyServiceName    = "stripeproxy"
)

type (
	AppID           string
	PaymentIntentId string
)

type RetrievePaymentIntentResponse struct {
	AppId         AppID                `json:"appId"`
	PaymentIntent stripe.PaymentIntent `json:"paymentIntent"`
}

// GetEndDate returns parsed expiration date
func (r *RetrievePaymentIntentResponse) GetEndDate() *time.Time {
	// if invoice is nil its a one time purchase
	if i := r.PaymentIntent.Invoice; i == nil || i.Subscription == nil {
		return nil
	}

	end := time.Unix(r.PaymentIntent.Invoice.Subscription.CurrentPeriodEnd, 0)
	return &end
}

// GetStartDate returns parsed purchase date
func (r *RetrievePaymentIntentResponse) GetStartDate() (time.Time, error) {
	// if invoice is not nil, use the subscription start date
	i := r.PaymentIntent.Invoice
	if i != nil {
		if s := i.Subscription; s != nil && s.CurrentPeriodStart != 0 {
			return time.Unix(r.PaymentIntent.Invoice.Subscription.CurrentPeriodStart, 0), nil
		}
	}

	if c := r.PaymentIntent.Created; c != 0 {
		start := time.Unix(c, 0)
		return start, nil
	}

	return time.Time{}, errors.New("unable to determine start date")
}

type StripeStoreConfig struct {
	AppServiceID    string `json:"appServiceID"`
	PaymentIntentID string `json:"paymentIntentID"`
}

func (rc *StripeStoreConfig) validate() error {
	if rc.PaymentIntentID == "" {
		return errors.New("paymentIntentId (receipt) is required for StripeStore Receipt")
	}

	if rc.AppServiceID == "" {
		return errors.New("appServiceId is required for StripeStore Receipt")
	}

	return nil
}

func (r *StripeStore) checkAppServiceIdFromIntent(ctx context.Context) error {
	// PPV
	appServiceId := r.Data.PaymentIntent.Metadata["appServiceId"]

	// Subscription
	if i := r.Data.PaymentIntent.Invoice; i != nil && i.Subscription != nil {
		productId := r.Data.PaymentIntent.Invoice.Lines.Data[0].Plan.Product.ID
		if productId == "" {
			return NewBadReceiptError(errors.New("product id not found in stripe"))
		}
		product, err := r.stripeProxy.GetProductByServiceID(ctx, productId)
		if err != nil {
			return err
		}

		appServiceId = product.AppServiceID
	}

	if appServiceId == "" {
		return NewBadReceiptError(errors.New("appServiceId not found in payment intent"))
	}

	if appServiceId != r.config.AppServiceID {
		return NewBadReceiptError(fmt.Errorf("appServiceId from payment intent does not match request: %s != %s", appServiceId, r.config.AppServiceID))
	}

	return nil
}

type StripeProxy interface {
	GetProductByServiceID(ctx context.Context, id string) (*stripeproxy.ProductResponse, error)
}

// StripeStore implements Receipt interface
type StripeStore struct {
	Data        RetrievePaymentIntentResponse
	log         logger.Logger
	stats       Stats
	config      StripeStoreConfig
	cb          CircuitAPI
	stripeProxy StripeProxy
}

func (r *StripeStore) getFromOrigin(ctx context.Context) error {
	if err := r.download(ctx); err != nil {
		return err
	}

	if err := r.checkAppServiceIdFromIntent(ctx); err != nil {
		return err
	}

	// stripe payment intents never expire, just cache the successful receipt because the other states might be temporary
	_, err := r.UnExpired()
	if err != nil {
		return err
	}

	return nil
}

// NewStripeStore creates a StripeStore
func NewStripeStore(ctx context.Context, stat Stats, conf StripeStoreConfig, cb CircuitAPI) (*StripeStore, error) {
	if err := conf.validate(); err != nil {
		return nil, err
	}

	log := request.GetFromContext(ctx).GetLoggingEntry()

	p, err := stripeproxy.NewStripeProxy(ctx, stat)
	if err != nil {
		return nil, err
	}
	r := StripeStore{
		log:         log,
		config:      conf,
		stats:       stat,
		cb:          cb,
		stripeProxy: p,
	}

	// try origin
	if err := r.getFromOrigin(ctx); err != nil {
		return nil, err
	}

	return &r, nil
}

// UnExpired will check to see if the receipt is still valid
// keep in mind that stripe payment intents never expire
func (r *StripeStore) UnExpired() (interface{}, error) {
	if s := r.Data.PaymentIntent.Status; s != "succeeded" {
		return nil, fmt.Errorf("transaction status is %s", s)
	}

	if i := r.Data.PaymentIntent.Invoice; i != nil {
		if s := i.Subscription; s != nil {
			if s.Status != stripe.SubscriptionStatusActive {
				return nil, fmt.Errorf("subscription status is %s", s.Status)
			}
		}
	}

	return r.Data, nil
}

func (r *StripeStore) GetData() interface{} {
	return r.Data
}

type RetrievePaymentRequest struct {
	AppServiceID string          `json:"appServiceId"`
	PaymentID    PaymentIntentId `json:"paymentIntentId"`
}

func (r *StripeStore) download(ctx context.Context) error {
	conf := r.config
	addlHeaders := http.Header{}
	addlHeaders.Add("Accept", "application/json")

	body := RetrievePaymentRequest{
		AppServiceID: conf.AppServiceID,
		PaymentID:    PaymentIntentId(conf.PaymentIntentID),
	}

	b, err := json.Marshal(body)
	if err != nil {
		return err
	}

	req, err := http.NewRequest("POST", stripeProxyRetrieveIntent, io.NopCloser(bytes.NewBuffer(b)))
	if err != nil {
		return err
	}
	req.Header = addlHeaders
	res, err := r.cb.DoWithContext(ctx, req)
	if err != nil {
		r.log.Error("stripe-proxy retrieve payment intent:", err)
		return err
	}

	defer res.Body.Close()

	if res.StatusCode >= 300 {
		r.log.Error("stripe-proxy Error code", res.StatusCode)
		b, err := io.ReadAll(res.Body)
		if err != nil {
			r.log.Error("stripe-proxy Error", err)
		}
		r.log.Error("stripe-proxy Error", string(b))

		return fmt.Errorf("stripe-proxy Error code %d body %s", res.StatusCode, string(b))
	}

	if err = json.NewDecoder(res.Body).Decode(&r.Data); err != nil {
		r.log.Error("Error during Unmarshal for stripe-proxy Response ", err)
		return err
	}

	return nil
}
