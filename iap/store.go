package iap

import (
	"errors"

	"github.com/foxcorp-product/entitlement-sdk/apikeys"
)

// Store represents a type of IAP store
type Store string

const (
	UnknownStore   Store  = "unknownstore"
	AppStore       Store  = "appstore"
	AppleDev       Store  = "applesdk"
	RokuStore      Store  = "rokustore"
	AmazonStore    Store  = "amazonstore"
	PlayStore      Store  = "playstore"
	StripeStore    Store  = "stripe"
	SamsungStore   Store  = "tizen"
	AppStoreApi    Store  = "appstoreapi"
	AppStoreDevApi string = "appstoresdkapi"
	AppStoreApiKey string = "privateKey"
)

// deviceFamilyToStore allows us to map a deviceFamily to a particular store driver
var deviceFamilyToStore = map[string]Store{
	// apple
	"ios_handheld": AppStore,
	"apple_tvos":   AppStore,
	"appletv_ctv":  AppStore,
	"tvos_ctv":     AppStore,
	"ios_tablet":   AppStore,
	"ios":          AppStore,
	"tvos":         AppStore,

	// amazon
	"amazon_fire_tv": AmazonStore,
	"firetv_firetv":  AmazonStore,
	"firetv_ctv":     AmazonStore,
	"firetv":         AmazonStore,

	// roku
	"roku_lowpower_ctv": RokuStore,
	"roku_ctv":          RokuStore,
	"roku_os":           RokuStore,
	"roku":              RokuStore,

	// google
	"android_handheld": PlayStore,
	"android_tv":       PlayStore,
	"android_tablet":   PlayStore,
	"androidtv_ctv":    PlayStore,
	"chromecast_":      PlayStore,
	"chromecast_ctv":   PlayStore,
	"android":          PlayStore,
	"androidtv":        PlayStore,

	// stripe
	"web_":        StripeStore,
	"web_desktop": StripeStore,
	"web_tablet":  StripeStore,
	"web":         StripeStore,

	// samsung
	"samsung_ctv":   SamsungStore,
	"samsung_tizen": SamsungStore,
	"tizen":         SamsungStore,
}

func GetStore(key apikeys.APIKey) (Store, error) {
	store, ok := deviceFamilyToStore[key.Platform]
	if ok {
		return store, nil
	}

	return UnknownStore, errors.New("unknown store")
}
