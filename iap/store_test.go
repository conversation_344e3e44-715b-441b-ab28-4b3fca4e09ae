package iap

import (
	"testing"

	"github.com/foxcorp-product/entitlement-sdk/apikeys"
	"github.com/stretchr/testify/assert"
)

func TestGetStore(t *testing.T) {

	type tests struct {
		name           string
		param          apikeys.APIKey
		expectedOutput Store
		expectedError  bool
	}

	testCases := []tests{
		{
			name: "valid platform - ios",
			param: apikeys.APIKey{
				APIKey:   "test-key",
				Platform: "ios",
			},
			expectedOutput: AppStore,
			expectedError:  false,
		},
		{
			name: "valid platform - ios_handheld",
			param: apikeys.APIKey{
				APIKey:   "test-key",
				Platform: "ios_handheld",
			},
			expectedOutput: AppStore,
			expectedError:  false,
		},
		{
			name: "valid platform - tvos",
			param: apikeys.APIKey{
				APIKey:   "test-key",
				Platform: "tvos",
			},
			expectedOutput: AppStore,
			expectedError:  false,
		},
		{
			name: "valid platform - roku",
			param: apikeys.APIKey{
				APIKey:   "test-key",
				Platform: "roku",
			},
			expectedOutput: RokuStore,
			expectedError:  false,
		},
		{
			name: "valid platform - roku_os",
			param: apikeys.APIKey{
				APIKey:   "test-key",
				Platform: "roku_os",
			},
			expectedOutput: RokuStore,
			expectedError:  false,
		},
		{
			name: "valid platform - android",
			param: apikeys.APIKey{
				APIKey:   "test-key",
				Platform: "android",
			},
			expectedOutput: PlayStore,
			expectedError:  false,
		},
		{
			name: "valid platform - androidtv",
			param: apikeys.APIKey{
				APIKey:   "test-key",
				Platform: "androidtv",
			},
			expectedOutput: PlayStore,
			expectedError:  false,
		},
		{
			name: "valid platform - firetv",
			param: apikeys.APIKey{
				APIKey:   "test-key",
				Platform: "firetv",
			},
			expectedOutput: AmazonStore,
			expectedError:  false,
		},
		{
			name: "valid platform - web",
			param: apikeys.APIKey{
				APIKey:   "test-key",
				Platform: "web",
			},
			expectedOutput: StripeStore,
			expectedError:  false,
		},
		{
			name: "valid platform - web_desktop",
			param: apikeys.APIKey{
				APIKey:   "test-key",
				Platform: "web_desktop",
			},
			expectedOutput: StripeStore,
			expectedError:  false,
		},
		{
			name: "valid platform - tizen",
			param: apikeys.APIKey{
				APIKey:   "test-key",
				Platform: "tizen",
			},
			expectedOutput: SamsungStore,
			expectedError:  false,
		},
		{
			name: "unknown platform",
			param: apikeys.APIKey{
				APIKey:   "test-key",
				Platform: "unknown_platform",
			},
			expectedOutput: UnknownStore,
			expectedError:  true,
		},
		{
			name: "d2c_internal platform",
			param: apikeys.APIKey{
				APIKey:   "test-key",
				Platform: "d2c_internal",
			},
			expectedOutput: UnknownStore,
			expectedError:  true,
		},
		{
			name: "vizio platform",
			param: apikeys.APIKey{
				APIKey:   "test-key",
				Platform: "vizio",
			},
			expectedOutput: UnknownStore,
			expectedError:  true,
		},
		{
			name: "lg platform",
			param: apikeys.APIKey{
				APIKey:   "test-key",
				Platform: "lg",
			},
			expectedOutput: UnknownStore,
			expectedError:  true,
		},
		{
			name: "xbox platform",
			param: apikeys.APIKey{
				APIKey:   "test-key",
				Platform: "xbox",
			},
			expectedOutput: UnknownStore,
			expectedError:  true,
		},
		{
			name: "comcast platform",
			param: apikeys.APIKey{
				APIKey:   "test-key",
				Platform: "comcast",
			},
			expectedOutput: UnknownStore,
			expectedError:  true,
		},
		{
			name: "kepler platform",
			param: apikeys.APIKey{
				APIKey:   "test-key",
				Platform: "kepler",
			},
			expectedOutput: UnknownStore,
			expectedError:  true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			store, err := GetStore(tc.param)
			assert.Equal(t, tc.expectedOutput, store)
			if tc.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
