package featureflag

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/launchdarkly/go-sdk-common/v3/ldvalue"

	"github.com/foxcorp-product/entitlement-sdk/feature"
	ld "github.com/launchdarkly/go-server-sdk/v6"

	"github.com/foxcorp-product/commerce-receiptverify/config"
)

const (
	defaultLDClientTimeout = 10 * time.Second
)

// useLaunchDarklyClientInLocal by default,
// a noopFeatureClient will be used when the SERVICE_ENV variable is set to empty or local.
// When this environment variable USE_LD_CLIENT_LOCALLY is set to true, the noopFeatureClient will not override the LaunchDarkly client for local environment.
var useLaunchDarklyClientInLocal = os.Getenv("USE_LD_CLIENT_LOCALLY") == "true"

// noopFeatureClient defines a feature client for local testing
type noopFeatureClient[T feature.Configurable] struct{}

func (n noopFeatureClient[T]) StringFlag(_ context.Context, _ string, defaultVal string, opts ...feature.BuildUserContextAndIdentityOption) string {
	return defaultVal
}

func (n noopFeatureClient[T]) BoolFlag(_ context.Context, _ string, defaultVal bool, opts ...feature.BuildUserContextAndIdentityOption) bool {
	return defaultVal
}

func (n noopFeatureClient[T]) IntFlag(_ context.Context, _ string, defaultVal int, opts ...feature.BuildUserContextAndIdentityOption) int {
	return defaultVal
}

func (n noopFeatureClient[T]) Float64Flag(_ context.Context, _ string, defaultVal float64, opts ...feature.BuildUserContextAndIdentityOption) float64 {
	return defaultVal
}

func (n noopFeatureClient[T]) MultivariateFlag(ctx context.Context, key string, defaultVal ldvalue.Value, opts ...feature.BuildUserContextAndIdentityOption) ldvalue.Value {
	return defaultVal
}
func (n noopFeatureClient[T]) Shutdown() error { return nil }

type FeatureClient struct {
	feature feature.FeatureClient
}

func MakeFeatureClient(envName string, cfg *config.Config) (FeatureClient, error) {
	featOpts := []feature.FeatureOption[ld.Config]{
		feature.WithTimeout(defaultLDClientTimeout),
		feature.WithApiKey(cfg.LDApiKey),
		feature.WithProviderOptions(ld.Config{Offline: false}),
	}
	if (envName == "" || envName == "local") && !useLaunchDarklyClientInLocal {
		featOpts = append(featOpts, feature.WithProvider(&noopFeatureClient[ld.Config]{}))
	}

	ldc, err := feature.New(featOpts...)
	if err != nil {
		return FeatureClient{}, fmt.Errorf("unable to setup launchDarkly client: %w", err)
	}

	return FeatureClient{
		feature: ldc}, err
}

func (fc FeatureClient) GetUseMockServer(ctx context.Context) bool {
	// grab a feature flag value if exists (and is on), otherwise return default value.
	return fc.feature.BoolFlag(ctx, "use_mockserver", false)
}
