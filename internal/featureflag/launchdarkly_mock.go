package featureflag

import (
	"context"

	"github.com/foxcorp-product/entitlement-sdk/feature"
	"github.com/launchdarkly/go-sdk-common/v3/ldvalue"
)

type FeatureMockClient struct {
	S map[string]string
	B map[string]bool
	I map[string]int
	F map[string]float64
	M map[string]ldvalue.Value
}

func (c *FeatureMockClient) Shutdown() error {
	return nil
}

func (c *FeatureMockClient) IntFlag(ctx context.Context, key string, defaultVal int, opts ...feature.BuildUserContextAndIdentityOption) int {
	if val, ok := c.I[key]; ok {
		return val
	}
	return defaultVal
}

func (c *FeatureMockClient) Float64Flag(ctx context.Context, key string, defaultVal float64, opts ...feature.BuildUserContextAndIdentityOption) float64 {
	if val, ok := c.F[key]; ok {
		return val
	}
	return defaultVal
}

func (c *FeatureMockClient) StringFlag(ctx context.Context, key string, defaultVal string, opts ...feature.BuildUserContextAndIdentityOption) string {
	if val, ok := c.S[key]; ok {
		return val
	}
	return defaultVal
}

func (c *FeatureMockClient) BoolFlag(ctx context.Context, key string, defaultVal bool, opts ...feature.BuildUserContextAndIdentityOption) bool {
	if val, ok := c.B[key]; ok {
		return val
	}
	return defaultVal
}

func (c *FeatureMockClient) MultivariateFlag(ctx context.Context, key string, defaultVal ldvalue.Value, opts ...feature.BuildUserContextAndIdentityOption) ldvalue.Value {
	if val, ok := c.M[key]; ok {
		return val
	}
	return defaultVal
}
