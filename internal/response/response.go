package response

import (
	"fmt"
	"net/http"
)

type Error struct {
	Type    string `json:"errorType"`
	Message string `json:"errorMessage"`
}

func MakeFromError(statusCode int, errors ...error) Error {
	// as reference https://github.com/foxbroadcasting/go-helpers/blob/14f3f11396618f84333edee9320cecdb75255674/v10/handlererror/handlererror.go#L56
	if statusCode == 0 {
		statusCode = http.StatusTeapot
	}

	if len(errors) == 0 {
		return Error{
			Type: http.StatusText(statusCode),
		}
	}

	var err = errors[0]
	for idx := 1; idx < len(errors); idx++ {
		err = fmt.Errorf("%w: %w", err, errors[idx])
	}

	return Error{
		Type:    http.StatusText(statusCode),
		Message: err.Error(),
	}
}
