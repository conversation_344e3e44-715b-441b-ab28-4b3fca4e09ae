package response

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
)

var ErrUnableToEncode = errors.New("error to encode response")

// Send sends the response to the response writer
// Uses the Make method to generate the Response object, then writes to the response writer.
func Send(w http.ResponseWriter, statusCode int, results any) {
	var err error
	var bts []byte

	if results != nil {
		// Using json marshal to capture the error
		// If you using json encoder, this calls the w.WriteHeader making impossible to change the status code
		if bts, err = json.Marshal(results); err != nil {
			statusCode = http.StatusInternalServerError
			resp := MakeFromError(statusCode, fmt.Errorf("%w: %w", ErrUnableToEncode, err))
			bts, _ = json.Marshal(resp)
		}
	}

	w.WriteHeader(statusCode)
	_, _ = w.Write(bts)
}

// SendErrors sends errors to the response writer
// Uses the MakeFromError method to generate the Response with error's object, then writes to the response writer.
func SendErrors(w http.ResponseWriter, statusCode int, errors ...error) {
	resp := MakeFromError(statusCode, errors...)
	w.WriteHeader(statusCode)
	_ = json.NewEncoder(w).Encode(resp)
}
