package response

import (
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSend(t *testing.T) {
	var handler http.HandlerFunc
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		handler(w, r)
	}))
	defer ts.Close()

	type args struct {
		statusCode   int
		totalResults int
		results      []interface{}
	}

	tests := []struct {
		name    string
		args    args
		want    any
		wantErr bool
	}{
		{
			name: "Three results",
			args: args{
				statusCode:   200,
				totalResults: 3,
				results:      []interface{}{"foo", "bar", "baz"},
			},
			want: []interface{}{"foo", "bar", "baz"},
		},
		{
			name: "One result",
			args: args{
				statusCode:   200,
				totalResults: 1,
				results:      []interface{}{"foo"},
			},
			want: []interface{}{"foo"},
		},
		{
			name: "Empty results",
			args: args{
				statusCode:   200,
				totalResults: 0,
				results:      []interface{}{},
			},
			want: []interface{}{},
		},
		{
			name: "Marshal error",
			args: args{
				statusCode:   200,
				totalResults: 0,
				results:      []interface{}{make(chan struct{})},
			},
			want:    MakeFromError(500, errors.New("error to encode response: json: unsupported type: chan struct {}")),
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler = func(w http.ResponseWriter, r *http.Request) {
				Send(w, tt.args.statusCode, tt.args.results)
			}

			res, err := http.Get(ts.URL)
			if err != nil {
				t.Errorf("Send() request error: %v", err)
				return
			}

			defer func() { _ = res.Body.Close() }()

			if tt.wantErr {
				var out Error
				if err := json.NewDecoder(res.Body).Decode(&out); err != nil {
					t.Errorf("Send() json decode error: %v", err)
					return
				}
				assert.EqualValues(t, tt.want, out)
				return
			}

			var out any
			if err := json.NewDecoder(res.Body).Decode(&out); err != nil {
				t.Errorf("Send() json decode error: %v", err)
				return
			}
			assert.EqualValues(t, tt.want, out)
		})
	}
}

func TestSendErrors(t *testing.T) {
	var handler http.HandlerFunc
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		handler(w, r)
	}))
	defer ts.Close()
	type args struct {
		statusCode int
		errors     []error
	}

	tests := []struct {
		name  string
		args  args
		wantR Error
	}{
		{
			name: "Three errors",
			args: args{
				statusCode: 401,
				errors:     []error{errors.New("foo"), errors.New("bar"), errors.New("baz")},
			},
			wantR: MakeFromError(401, errors.New("foo"), errors.New("bar"), errors.New("baz")),
		},
		{
			name: "One error",
			args: args{
				statusCode: 401,
				errors:     []error{errors.New("foo")},
			},
			wantR: MakeFromError(401, errors.New("foo")),
		},
		{
			name: "Empty errors",
			args: args{
				statusCode: 404,
				errors:     []error{},
			},
			wantR: MakeFromError(404),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler = func(w http.ResponseWriter, r *http.Request) {
				SendErrors(w, tt.args.statusCode, tt.args.errors...)
			}

			res, err := http.Get(ts.URL)
			if err != nil {
				t.Errorf("SendErrors() request error: %v", err)
				return
			}

			defer func() { _ = res.Body.Close() }()

			var out Error
			if err = json.NewDecoder(res.Body).Decode(&out); err != nil {
				t.Errorf("SendErrors() json decode error: %v", err)
				return
			}

			assert.Equal(t, tt.wantR, out)
			assert.Equal(t, tt.wantR.Type, out.Type)
		})
	}
}
