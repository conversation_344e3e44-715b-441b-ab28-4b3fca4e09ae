package stripeproxy

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/foxcorp-product/entitlement-sdk/circuitbreaker"
	"github.com/foxcorp-product/entitlement-sdk/stats"

	"github.com/stripe/stripe-go/v74"
)

const (
	stripeProxyRetrieveProduct = "/v1/stripe/product"
	stripeProxyServiceName     = "stripeproxy"
)

type ProductResponse struct {
	AppServiceID string          `json:"appServiceId"`
	Product      *stripe.Product `json:"product"`
}

type Stats interface {
	StartMethodSpan(ctx context.Context, resource string, opts ...stats.StartSpanOption) (context.Context, stats.Span)
	StartHTTPSpan(operationName stats.OperationName, resource string, r *http.Request, opts ...stats.StartSpanOption) (stats.Span, *http.Request)
	StartSpan(ctx context.Context, spanType string, operation stats.OperationName, resource string, opts ...stats.StartSpanOption) (context.Context, stats.Span)
	Timing(name string, value time.Duration, rate float64, tags ...string) error
	Gauge(name string, value float64, rate float64, tags ...string) error
	Incr(name string, rate float64, tags ...string) error
	Count(name string, value int64, rate float64, tags ...string) error
	TimeInMilliseconds(name string, value float64, rate float64, tags ...string) error
	WrapHTTPClient(operationName stats.OperationName, resourceProvider func(*http.Request) string, client stats.HTTPClient, opts ...stats.StartSpanOption) (stats.HTTPClient, stats.FinishFunc)
	Flush() error
}

type CircuitAPI interface {
	GetWithContext(ctx context.Context, url string) (resp *http.Response, err error)
}

type StripeProxy struct {
	stats Stats
	cb    CircuitAPI
}

func NewStripeProxy(ctx context.Context, stat Stats) (StripeProxy, error) {
	cb, err := circuitbreaker.New(
		circuitbreaker.WithServiceName(stripeProxyServiceName),
		circuitbreaker.WithStatsdClient(stat),
		circuitbreaker.WithAPMTracer(),
	)
	if err != nil {
		return StripeProxy{}, err
	}

	return StripeProxy{
		stats: stat,
		cb:    cb,
	}, nil
}

func (p StripeProxy) GetProductByServiceID(ctx context.Context, id string) (*ProductResponse, error) {
	var err error
	var span stats.Span

	_, span = p.stats.StartMethodSpan(ctx, "stripeproxy.RetrieveProductByID")
	defer func() { span.FinishWithError(err) }()

	var rsp ProductResponse

	url := fmt.Sprintf("%s/?id=%s", stripeProxyRetrieveProduct, id)
	res, err := p.cb.GetWithContext(ctx, url)
	if err != nil {
		return nil, err
	}
	err = circuitbreaker.Unmarshal(res, rsp)
	if err != nil {
		return nil, err
	}

	return &rsp, nil
}
