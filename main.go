package main

import (
	"fmt"
	"os"
	"strings"

	"github.com/foxcorp-product/commerce-receiptverify/internal/response"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/foxcorp-product/commerce-receiptverify/config"
	"github.com/foxcorp-product/commerce-receiptverify/httprouter"
	"github.com/foxcorp-product/entitlement-sdk/configbuilder"
	"github.com/foxcorp-product/entitlement-sdk/httpserver"
	"github.com/foxcorp-product/entitlement-sdk/logger"
	"github.com/foxcorp-product/entitlement-sdk/middleware"
	"github.com/foxcorp-product/entitlement-sdk/stats"
	"github.com/gookit/validate"
)

const (
	defaultServiceName    = "receiptverify"
	defaultServiceVersion = "v1"
)

var (
	serviceEnv     = os.Getenv("SERVICE_ENV")
	serviceVersion = os.Getenv("SERVICE_VERSION")
	serviceName    = os.Getenv("SERVICE_NAME")
)

func main() {
	// setup serviceName and version based on env vars
	// done this way, since you can have multiple services/workers in the same repository
	if serviceName == "" {
		serviceName = defaultServiceName
	}
	if serviceVersion == "" {
		serviceVersion = defaultServiceVersion
	}

	// setting validation config works for config and for route validation
	validate.Config(func(opt *validate.GlobalOption) {
		opt.SkipOnEmpty = false
		// return all errors
		opt.StopOnError = false
	})

	// setup localstack and AWS config
	// If you want use localstack you must need to set this env var
	// AWS_LOCALSTACK = true; Also this only locally
	// Other Localstack options are available, please go check the documention on configbuilder
	var awsCfg aws.Config = configbuilder.LoadAWSConfig(
		configbuilder.WithLocalstack(),
		//check the other options
		//This option trace all http requests using AWS http client, track dynamo, s3, sns.
		configbuilder.WithAPMTracer(serviceName),
	)
	// create a instance of the config
	cfg := &config.Config{}

	cfgLogger, err := logger.New(
		logger.WithoutTimestamp(),
		logger.WithJsonFormatter(),
		logger.WithLogLevel("error"),
	)
	if err != nil {
		panic(err)
	}

	// build the config, passing the options you need.
	// Just to be sure, it is a MUST to always pass serviceName and serviceVersion, otherwise parameterstore and secretmanager will not work properly
	err = configbuilder.Build(cfg,
		configbuilder.WithAWSConfig(awsCfg),
		configbuilder.WithServiceName(serviceName),
		configbuilder.WithServiceVersion(serviceVersion),
		configbuilder.WithDefaultValues(config.DefaultValues),
		configbuilder.WithLocalValues(config.LocalValues),
		configbuilder.WithEnabledProviders(strings.Split(config.AllProviders, ",")...),
		configbuilder.WithLogger(cfgLogger),
	)
	if err != nil {
		panic(err)
	}

	// setup logger
	l, err := logger.New(
		logger.WithLogLevel(cfg.Loglevel),
		logger.WithJsonFormatter(),
		logger.WithoutTimestamp(),
	)
	if err != nil {
		panic(err)
	}

	stat, err := makeStats(serviceEnv, serviceName, serviceVersion, l)
	if err != nil {
		l.Fatalf("makeStats() err = %s", err.Error())
	}
	// setup handler dependencies
	deps, err := httprouter.MakeHandlerDeps(cfg, awsCfg, stat)
	if err != nil {
		panic(err)
	}

	// setup routes
	routes, err := httprouter.MakeRoutes(deps)
	if err != nil {
		panic(err)
	}
	customErrRes := middleware.WithCustomErrorResponse(response.SendErrors)

	// setup server middlewares
	middlewares := []middleware.MiddlewareFunc{
		middleware.MakeStandardAPIKey(l, false, customErrRes),
	}

	// setup new service
	s, err := httpserver.New(
		httpserver.WithLogger(l),
		httpserver.WithStats(stat),
		httpserver.WithServicerPort(cfg.ServicePort),
		httpserver.WithServiceName(serviceName),
		httpserver.WithServiceVersion(serviceVersion),
		httpserver.WithRoutes(routes),
		//Enable it if necessary.
		httpserver.WithMultiKeyJWTMiddleware(cfg.JWTPublicKeys.Merge(cfg.HydraPublicKeys), false),
		httpserver.WithMiddlewares(middlewares...),
		httpserver.WithChiInitializationV2(),
		httpserver.WithAPMTracer(),
	)
	if err != nil {
		panic(fmt.Sprintf("failed to start server: %v", err))
	}

	s.ListenAndServe()
}

func makeStats(envName, serviceName, serviceVersion string, log logger.Logger) (*stats.StandardStats, error) {
	switch envName {
	case "", "local":
		return stats.New(serviceName, serviceVersion, stats.WithDevMode())
	case "prod1", "stage1":
		return stats.New(serviceName, serviceVersion)
	default:
		stat, err := stats.New(serviceName, serviceVersion)
		if err != nil {
			log.Error(fmt.Errorf("error to initialize stats, this service won't send any metrics (fallback to dev mode): %w", err))
			return stats.New(serviceName, serviceVersion, stats.WithDevMode())
		}
		return stat, nil
	}
}
