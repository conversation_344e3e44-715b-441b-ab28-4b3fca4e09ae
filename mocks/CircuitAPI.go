package mocks

import (
	context "context"
	http "net/http"

	io "io"

	mock "github.com/stretchr/testify/mock"
)

// CircuitAPI is a mock type for the CircuitAPI type
type CircuitAPI struct {
	mock.Mock
}

// DoWithContext provides a mock function with given fields: ctx, req
func (_m *CircuitAPI) DoWithContext(ctx context.Context, req *http.Request) (*http.Response, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for DoWithContext")
	}

	var r0 *http.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *http.Request) (*http.Response, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *http.Request) *http.Response); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *http.Request) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PostWithContext provides a mock function with given fields: ctx, url, contentType, body
func (_m *CircuitAPI) PostWithContext(ctx context.Context, url string, contentType string, body io.Reader) (*http.Response, error) {
	ret := _m.Called(ctx, url, contentType, body)

	if len(ret) == 0 {
		panic("no return value specified for PostWithContext")
	}

	var r0 *http.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, io.Reader) (*http.Response, error)); ok {
		return rf(ctx, url, contentType, body)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, io.Reader) *http.Response); ok {
		r0 = rf(ctx, url, contentType, body)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, io.Reader) error); ok {
		r1 = rf(ctx, url, contentType, body)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewCircuitAPI creates a new instance of CircuitAPI. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCircuitAPI(t interface {
	mock.TestingT
	Cleanup(func())
}) *CircuitAPI {
	mock := &CircuitAPI{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
