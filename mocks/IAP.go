package mocks

import (
	context "context"

	apikeys "github.com/foxcorp-product/entitlement-sdk/apikeys"

	iap "github.com/foxcorp-product/commerce-receiptverify/iap"

	mock "github.com/stretchr/testify/mock"
)

// IAP is a mock type for the IAP type
type IAP struct {
	mock.Mock
}

// GetReceipt provides a mock function with given fields: ctx, key, rec
func (_m *IAP) GetReceipt(ctx context.Context, key apikeys.APIKey, rec iap.ReceiptID) (iap.Receipt, error) {
	ret := _m.Called(ctx, key, rec)

	if len(ret) == 0 {
		panic("no return value specified for GetReceipt")
	}

	var r0 iap.Receipt
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, apikeys.APIKey, iap.ReceiptID) (iap.Receipt, error)); ok {
		return rf(ctx, key, rec)
	}
	if rf, ok := ret.Get(0).(func(context.Context, apikeys.APIKey, iap.ReceiptID) iap.Receipt); ok {
		r0 = rf(ctx, key, rec)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(iap.Receipt)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, apikeys.APIKey, iap.ReceiptID) error); ok {
		r1 = rf(ctx, key, rec)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewIAP creates a new instance of IAP. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewIAP(t interface {
	mock.TestingT
	Cleanup(func())
}) *IAP {
	mock := &IAP{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
