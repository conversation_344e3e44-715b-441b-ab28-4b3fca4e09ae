package mocks

import (
	client "github.com/foxcorp-product/commerce-receiptverify/client"
	mock "github.com/stretchr/testify/mock"
)

// Options is a mock type for the Options type
type Options struct {
	mock.Mock
}

// Execute provides a mock function with given fields: c
func (_m *Options) Execute(c *client.Client) error {
	ret := _m.Called(c)

	if len(ret) == 0 {
		panic("no return value specified for Execute")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*client.Client) error); ok {
		r0 = rf(c)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewOptions creates a new instance of Options. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOptions(t interface {
	mock.TestingT
	Cleanup(func())
}) *Options {
	mock := &Options{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
