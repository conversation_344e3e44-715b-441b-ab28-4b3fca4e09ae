package mocks

import (
	context "context"

	client "github.com/foxcorp-product/commerce-plan/client"

	mock "github.com/stretchr/testify/mock"
)

// PlanClient is a mock type for the PlanClient type
type PlanClient struct {
	mock.Mock
}

// GetPlanByAppServiceID provides a mock function with given fields: ctx, in
func (_m *PlanClient) GetPlanByAppServiceID(ctx context.Context, in client.V1GetPlanByAppServiceIDInput) (client.Plan, error) {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for GetPlanByAppServiceID")
	}

	var r0 client.Plan
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, client.V1GetPlanByAppServiceIDInput) (client.Plan, error)); ok {
		return rf(ctx, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, client.V1GetPlanByAppServiceIDInput) client.Plan); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Get(0).(client.Plan)
	}

	if rf, ok := ret.Get(1).(func(context.Context, client.V1GetPlanByAppServiceIDInput) error); ok {
		r1 = rf(ctx, in)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewPlanClient creates a new instance of PlanClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewPlanClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *PlanClient {
	mock := &PlanClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
