package mocks

import mock "github.com/stretchr/testify/mock"

// Receipt is a mock type for the Receipt type
type Receipt struct {
	mock.Mock
}

// GetData provides a mock function with given fields:
func (_m *Receipt) GetData() interface{} {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetData")
	}

	var r0 interface{}
	if rf, ok := ret.Get(0).(func() interface{}); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0)
		}
	}

	return r0
}

// UnExpired provides a mock function with given fields:
func (_m *Receipt) UnExpired() (interface{}, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for UnExpired")
	}

	var r0 interface{}
	var r1 error
	if rf, ok := ret.Get(0).(func() (interface{}, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() interface{}); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewReceipt creates a new instance of Receipt. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewReceipt(t interface {
	mock.TestingT
	Cleanup(func())
}) *Receipt {
	mock := &Receipt{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
