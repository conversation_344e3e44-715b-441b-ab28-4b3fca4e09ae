package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"

	stripeproxy "github.com/foxcorp-product/commerce-receiptverify/internal/stripeproxy"
)

// StripeProxy is a mock type for the StripeProxy type
type StripeProxy struct {
	mock.Mock
}

// GetProductByServiceID provides a mock function with given fields: ctx, id
func (_m *StripeProxy) GetProductByServiceID(ctx context.Context, id string) (*stripeproxy.ProductResponse, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetProductByServiceID")
	}

	var r0 *stripeproxy.ProductResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*stripeproxy.ProductResponse, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *stripeproxy.ProductResponse); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stripeproxy.ProductResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewStripeProxy creates a new instance of StripeProxy. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewStripeProxy(t interface {
	mock.TestingT
	Cleanup(func())
}) *StripeProxy {
	mock := &StripeProxy{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
