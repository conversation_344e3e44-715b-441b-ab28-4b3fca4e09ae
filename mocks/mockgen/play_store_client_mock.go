// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/foxcorp-product/commerce-receiptverify/iap/receipt (interfaces: IPlayStoreClient)
//
// Generated by this command:
//
//	mockgen -destination=mocks/mockgen/play_store_client_mock.go github.com/foxcorp-product/commerce-receiptverify/iap/receipt IPlayStoreClient
//

// Package mock_receipt is a generated GoMock package.
package mock_receipt

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	v3 "google.golang.org/api/androidpublisher/v3"
)

// MockIPlayStoreClient is a mock of IPlayStoreClient interface.
type MockIPlayStoreClient struct {
	ctrl     *gomock.Controller
	recorder *MockIPlayStoreClientMockRecorder
	isgomock struct{}
}

// MockIPlayStoreClientMockRecorder is the mock recorder for MockIPlayStoreClient.
type MockIPlayStoreClientMockRecorder struct {
	mock *MockIPlayStoreClient
}

// NewMockIPlayStoreClient creates a new mock instance.
func NewMockIPlayStoreClient(ctrl *gomock.Controller) *MockIPlayStoreClient {
	mock := &MockIPlayStoreClient{ctrl: ctrl}
	mock.recorder = &MockIPlayStoreClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIPlayStoreClient) EXPECT() *MockIPlayStoreClientMockRecorder {
	return m.recorder
}

// GetSubscriptionOffer mocks base method.
func (m *MockIPlayStoreClient) GetSubscriptionOffer(arg0 context.Context, arg1, arg2, arg3, arg4 string) (*v3.SubscriptionOffer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSubscriptionOffer", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*v3.SubscriptionOffer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSubscriptionOffer indicates an expected call of GetSubscriptionOffer.
func (mr *MockIPlayStoreClientMockRecorder) GetSubscriptionOffer(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSubscriptionOffer", reflect.TypeOf((*MockIPlayStoreClient)(nil).GetSubscriptionOffer), arg0, arg1, arg2, arg3, arg4)
}

// VerifyProduct mocks base method.
func (m *MockIPlayStoreClient) VerifyProduct(arg0 context.Context, arg1, arg2, arg3 string) (*v3.ProductPurchase, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyProduct", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*v3.ProductPurchase)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyProduct indicates an expected call of VerifyProduct.
func (mr *MockIPlayStoreClientMockRecorder) VerifyProduct(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyProduct", reflect.TypeOf((*MockIPlayStoreClient)(nil).VerifyProduct), arg0, arg1, arg2, arg3)
}

// VerifySubscriptionV2 mocks base method.
func (m *MockIPlayStoreClient) VerifySubscriptionV2(arg0 context.Context, arg1, arg2 string) (*v3.SubscriptionPurchaseV2, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifySubscriptionV2", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v3.SubscriptionPurchaseV2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifySubscriptionV2 indicates an expected call of VerifySubscriptionV2.
func (mr *MockIPlayStoreClientMockRecorder) VerifySubscriptionV2(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifySubscriptionV2", reflect.TypeOf((*MockIPlayStoreClient)(nil).VerifySubscriptionV2), arg0, arg1, arg2)
}
