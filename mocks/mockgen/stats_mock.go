// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/foxcorp-product/commerce-receiptverify/iap/receipt (interfaces: Stats)
//
// Generated by this command:
//
//	mockgen -destination=mocks/mockgen/stats_mock.go github.com/foxcorp-product/commerce-receiptverify/iap/receipt Stats
//

// Package mock_receipt is a generated GoMock package.
package mock_receipt

import (
	context "context"
	http "net/http"
	reflect "reflect"
	time "time"

	stats "github.com/foxcorp-product/entitlement-sdk/stats"
	gomock "go.uber.org/mock/gomock"
	ddtrace "gopkg.in/DataDog/dd-trace-go.v1/ddtrace"
)

// MockStats is a mock of Stats interface.
type MockStats struct {
	ctrl     *gomock.Controller
	recorder *MockStatsMockRecorder
	isgomock struct{}
}

// MockStatsMockRecorder is the mock recorder for MockStats.
type MockStatsMockRecorder struct {
	mock *MockStats
}

// NewMockStats creates a new mock instance.
func NewMockStats(ctrl *gomock.Controller) *MockStats {
	mock := &MockStats{ctrl: ctrl}
	mock.recorder = &MockStatsMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStats) EXPECT() *MockStatsMockRecorder {
	return m.recorder
}

// Count mocks base method.
func (m *MockStats) Count(name string, value int64, rate float64, tags ...string) error {
	m.ctrl.T.Helper()
	varargs := []any{name, value, rate}
	for _, a := range tags {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Count", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Count indicates an expected call of Count.
func (mr *MockStatsMockRecorder) Count(name, value, rate any, tags ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{name, value, rate}, tags...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Count", reflect.TypeOf((*MockStats)(nil).Count), varargs...)
}

// Flush mocks base method.
func (m *MockStats) Flush() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Flush")
	ret0, _ := ret[0].(error)
	return ret0
}

// Flush indicates an expected call of Flush.
func (mr *MockStatsMockRecorder) Flush() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Flush", reflect.TypeOf((*MockStats)(nil).Flush))
}

// Gauge mocks base method.
func (m *MockStats) Gauge(name string, value, rate float64, tags ...string) error {
	m.ctrl.T.Helper()
	varargs := []any{name, value, rate}
	for _, a := range tags {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Gauge", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Gauge indicates an expected call of Gauge.
func (mr *MockStatsMockRecorder) Gauge(name, value, rate any, tags ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{name, value, rate}, tags...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Gauge", reflect.TypeOf((*MockStats)(nil).Gauge), varargs...)
}

// Incr mocks base method.
func (m *MockStats) Incr(name string, rate float64, tags ...string) error {
	m.ctrl.T.Helper()
	varargs := []any{name, rate}
	for _, a := range tags {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Incr", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Incr indicates an expected call of Incr.
func (mr *MockStatsMockRecorder) Incr(name, rate any, tags ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{name, rate}, tags...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Incr", reflect.TypeOf((*MockStats)(nil).Incr), varargs...)
}

// StartHTTPSpan mocks base method.
func (m *MockStats) StartHTTPSpan(operationName stats.OperationName, resource string, r *http.Request, opts ...ddtrace.StartSpanOption) (stats.Span, *http.Request) {
	m.ctrl.T.Helper()
	varargs := []any{operationName, resource, r}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartHTTPSpan", varargs...)
	ret0, _ := ret[0].(stats.Span)
	ret1, _ := ret[1].(*http.Request)
	return ret0, ret1
}

// StartHTTPSpan indicates an expected call of StartHTTPSpan.
func (mr *MockStatsMockRecorder) StartHTTPSpan(operationName, resource, r any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{operationName, resource, r}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartHTTPSpan", reflect.TypeOf((*MockStats)(nil).StartHTTPSpan), varargs...)
}

// StartMethodSpan mocks base method.
func (m *MockStats) StartMethodSpan(ctx context.Context, resource string, opts ...ddtrace.StartSpanOption) (context.Context, stats.Span) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, resource}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartMethodSpan", varargs...)
	ret0, _ := ret[0].(context.Context)
	ret1, _ := ret[1].(stats.Span)
	return ret0, ret1
}

// StartMethodSpan indicates an expected call of StartMethodSpan.
func (mr *MockStatsMockRecorder) StartMethodSpan(ctx, resource any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, resource}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartMethodSpan", reflect.TypeOf((*MockStats)(nil).StartMethodSpan), varargs...)
}

// StartSpan mocks base method.
func (m *MockStats) StartSpan(ctx context.Context, spanType string, operation stats.OperationName, resource string, opts ...ddtrace.StartSpanOption) (context.Context, stats.Span) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, spanType, operation, resource}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartSpan", varargs...)
	ret0, _ := ret[0].(context.Context)
	ret1, _ := ret[1].(stats.Span)
	return ret0, ret1
}

// StartSpan indicates an expected call of StartSpan.
func (mr *MockStatsMockRecorder) StartSpan(ctx, spanType, operation, resource any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, spanType, operation, resource}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartSpan", reflect.TypeOf((*MockStats)(nil).StartSpan), varargs...)
}

// TimeInMilliseconds mocks base method.
func (m *MockStats) TimeInMilliseconds(name string, value, rate float64, tags ...string) error {
	m.ctrl.T.Helper()
	varargs := []any{name, value, rate}
	for _, a := range tags {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TimeInMilliseconds", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// TimeInMilliseconds indicates an expected call of TimeInMilliseconds.
func (mr *MockStatsMockRecorder) TimeInMilliseconds(name, value, rate any, tags ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{name, value, rate}, tags...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TimeInMilliseconds", reflect.TypeOf((*MockStats)(nil).TimeInMilliseconds), varargs...)
}

// Timing mocks base method.
func (m *MockStats) Timing(name string, value time.Duration, rate float64, tags ...string) error {
	m.ctrl.T.Helper()
	varargs := []any{name, value, rate}
	for _, a := range tags {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Timing", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Timing indicates an expected call of Timing.
func (mr *MockStatsMockRecorder) Timing(name, value, rate any, tags ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{name, value, rate}, tags...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Timing", reflect.TypeOf((*MockStats)(nil).Timing), varargs...)
}

// WrapHTTPClient mocks base method.
func (m *MockStats) WrapHTTPClient(operationName stats.OperationName, resourceProvider func(*http.Request) string, client stats.HTTPClient, opts ...ddtrace.StartSpanOption) (stats.HTTPClient, stats.FinishFunc) {
	m.ctrl.T.Helper()
	varargs := []any{operationName, resourceProvider, client}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "WrapHTTPClient", varargs...)
	ret0, _ := ret[0].(stats.HTTPClient)
	ret1, _ := ret[1].(stats.FinishFunc)
	return ret0, ret1
}

// WrapHTTPClient indicates an expected call of WrapHTTPClient.
func (mr *MockStatsMockRecorder) WrapHTTPClient(operationName, resourceProvider, client any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{operationName, resourceProvider, client}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WrapHTTPClient", reflect.TypeOf((*MockStats)(nil).WrapHTTPClient), varargs...)
}
