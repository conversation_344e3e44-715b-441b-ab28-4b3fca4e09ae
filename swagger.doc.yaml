openapi: 3.0.1
info:
  title: FOX Receipt Verification Service
  description: This service verifies a Receipt
  version: "1.0"
servers:
   - url: https://dev-commerce.fox.com/receiptverify
     description: dev Apigateway
   - url: http://af4fe540a1c0e4046847b3fc9ebc6b2d-e6c1dcb9d0b115d4.elb.us-east-1.amazonaws.com/receiptverify
     description: dev internal url
   - url: https://stage-commerce.fox.com/receiptverify
     description: stage Apigateway
   - url: http://a2a3e3828f9704d358416f3fa9a31948-8cdca1d1cf751b73.elb.us-east-1.amazonaws.com/receiptverify
     description: stage internal url
   - url: https://commerce.fox.com/receiptverify
     description: prod Apigateway
   - url: http://a6f4b3ba849334925a11c1f6daa70dc1-65bab5124bd9ada9.elb.us-east-1.amazonaws.com/receiptverify
     description: prod internal url
paths:
  /v1/receiptverify:
    post:
      description: This endpoint verifies receipt
      parameters:
      - name: X-Api-Key
        in: header
        required: true
        schema:
          type: string
      requestBody:
        description: verify receipt request body.
        content:
          '*/*':
            schema:
              $ref: '#/components/schemas/v1PostReceiptverifyRequest'
        required: false
      responses:
        200:
          description: Receipt is valid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/v1PostReceiptverifyResponse'
        401:
          description: Invalid Headers.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        424:
          description: If call to native backend is failing.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /v1/receiptverify/validate-roku-transaction:
    post:
      description: This endpoint verifies receipt
      parameters:
      - name: X-Api-Key
        in: header
        required: true
        schema:
          type: string
      requestBody:
        description: verify receipt request body.
        content:
          '*/*':
            schema:
              $ref: '#/components/schemas/v1PostReceiptverifyValidateRokuTransactionRequest'
        required: false
      responses:
        200:
          description: Receipt is valid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/v1PostReceiptverifyValidateRokuTransactionResponse'
        401:
          description: Invalid Headers.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        424:
          description: If call to native backend is failing.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /v1/receiptverify/validate-google-transaction:
    post:
      description: This endpoint verifies receipt
      parameters:
      - name: X-Api-Key
        in: header
        required: true
        schema:
          type: string
      requestBody:
        description: verify receipt request body.
        content:
          '*/*':
            schema:
              $ref: '#/components/schemas/v1PostReceiptverifyValidateGoogleTransactionRequest'
        required: false
      responses:
        200:
          description: Receipt is valid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/v1PostReceiptverifyValidateGoogleTransactionResponse'
        401:
          description: Invalid Headers.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        424:
          description: If call to native backend is failing.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /v1/receiptverify/validate-stripe-transaction:
    post:
      description: This endpoint verifies receipt
      parameters:
      - name: X-Api-Key
        in: header
        required: true
        schema:
          type: string
      requestBody:
        description: verify receipt request body.
        content:
          '*/*':
            schema:
              $ref: '#/components/schemas/v1PostReceiptverifyValidateStripeTransactionRequest'
        required: false
      responses:
        200:
          description: Receipt is valid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/v1PostReceiptverifyValidateStripeTransactionResponse'
        401:
          description: Invalid Headers.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        424:
          description: If call to native backend is failing.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /v1/receiptverify/validate-amazon-transaction:
    post:
      description: This endpoint verifies receipt
      parameters:
      - name: X-Api-Key
        in: header
        required: true
        schema:
          type: string
      requestBody:
        description: verify receipt request body.
        content:
          '*/*':
            schema:
              $ref: '#/components/schemas/v1PostReceiptverifyValidateAmazonTransactionRequest'
        required: false
      responses:
        200:
          description: Receipt is valid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/v1PostReceiptverifyValidateAmazonTransactionResponse'
        401:
          description: Invalid Headers.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        424:
          description: If call to native backend is failing.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /v1/receiptverify/validate-samsung-transaction:
    post:
      description: This endpoint verifies receipt
      parameters:
      - name: X-Api-Key
        in: header
        required: true
        schema:
          type: string
      requestBody:
        description: verify receipt request body.
        content:
          '*/*':
            schema:
              $ref: '#/components/schemas/v1PostReceiptverifyValidateSamsungTransactionRequest'
        required: false
      responses:
        200:
          description: Receipt is valid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/v1PostReceiptverifyValidateSamsungTransactionResponse'
        401:
          description: Invalid Headers.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        424:
          description: If call to native backend is failing.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /v1/receiptverify/apple-status:
    post:
      description: This endpoint returns subscription status from receipt
      parameters:
      - name: X-Api-Key
        in: header
        required: true
        schema:
          type: string
      requestBody:
        description: receipt status request body.
        content:
          '*/*':
            schema:
              $ref: '#/components/schemas/v1PostReceiptverifyAppleStatusRequest'
        required: false
      responses:
        200:
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/v1PostReceiptverifyAppleStatusResponse'
        400:
          description: bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: receipt error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /v1/receiptverify/validate-appstore-transaction:
    post:
      description: This endpoint verifies apple signed transaction
      parameters:
      - name: X-Api-Key
        in: header
        schema:
          type: string
      requestBody:
        description: verify transaction request body.
        content:
          '*/*':
            schema:
              required:
              - appServiceId
              - receipt
              type: object
              properties:
                appServiceId:
                  type: string
                receipt:
                  type: string
        required: false
      responses:
        200:
          description: Transaction is valid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/v1PostReceiptverifyResponse'
        400:
          description: Invalid input.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Failed validation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        424:
          description: If call to native backend is failing.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    v1Store:
      type: string
      description: the store
      enum:
      - unknownstore
      - appstore
      - rokustore
      - amazonstore
      - playstore
      - stripe
      - tizen
    v1PostReceiptverifyResponse:
      type: object
      properties:
        valid:
          type: boolean
          description: Receipt is a valid receipt or not
        startDate:
          type: string
          description: the purchase date
        endDate:
          type: string
          description: the expire date
        freeTrial:
          type: boolean
          description: is free trial
        nextPaymentDate:
          type: string
          description: the next payment date
          format: date-time
        lastPaymentTime:
          type: string
          description: the last payment date
          format: date-time
        isFlexibleOfferApplied:
          type: boolean
          description: is flexible offer
        flexibleOfferPrice:
          type: string
          description: the flexible offer price
        transactionId:
          type: string
          description: the transaction id
        store:
          $ref: '#/components/schemas/v1Store'
        plan:
          $ref: '#/components/schemas/v1Plan'
        charged:
          $ref: '#/components/schemas/v1Charged'
    v1PostReceiptverifyValidateRokuTransactionRequest:
      required:
      - appServiceId
      - transactionId
      type: object
      properties:
        appServiceId:
          type: string
          description: the app service id
        transactionId:
          type: string
          description: the transaction id
    v1PostReceiptverifyValidateRokuTransactionResponse:
      type: object
      properties:
        cancelledTransactionIds:
          type: array
          description: the cancelled transaction ids
          items:
            type: string
        valid:
          type: boolean
          description: Receipt is a valid receipt or not
        startDate:
          type: string
          description: the purchase date
          format: date-time
        endDate:
          type: string
          description: the expire date
          format: date-time
        transactionId:
          type: string
          description: the transaction id
        store:
          $ref: '#/components/schemas/v1Store'
    v1PostReceiptverifyValidateAmazonTransactionRequest:
      required:
      - appServiceId
      - receipt
      type: object
      properties:
        appServiceId:
          type: string
          description: the app service id
        receipt:
          type: string
          description: the receipt
        amazonUserId:
          type: string
          description: the Amazon user id
        allowExpired:
          type: boolean
          description: allow canceled/expired receipts
    v1PostReceiptverifyValidateAmazonTransactionResponse:
      $ref: '#/components/schemas/v1IAPResponse'
    v1PostReceiptverifyValidateGoogleTransactionRequest:
      required:
      - packageName
      - productId
      - purchaseToken
      type: object
      properties:
        packageName:
          type: string
          description: the package name
        productId:
          type: string
          description: the product id
        purchaseToken:
          type: string
          description: the purchase token
    v1PostReceiptverifyValidateGoogleTransactionResponse:
      required:
      - expiryDate
      - oneTime
      - orderId
      - productDate
      type: object
      properties:
        orderId:
          type: string
          description: the order id
        productDate:
          type: string
          description: the product date
          format: date-time
        expiryDate:
          type: string
          description: the expiry date
          format: date-time
        autoRenewing:
          type: boolean
          description: is auto renewing
        oneTime:
          type: boolean
          description: is one time
        userInitiatedCancellation:
          type: boolean
          description: if true, it means the cancellation was initiated by the user
        linkedPurchaseToken:
          type: string
          description: If present, it contains the purchaseToken of a previous purchase to which this new purchase is linked. This typically occurs during an upgrade or downgrade flow.
        resumeAtDate:
          type: string
          description: If present, it contains the date when the subscription will resume after the pause period.
        charged:
          $ref: '#/components/schemas/v1Charged'
    v1PostReceiptverifyValidateStripeTransactionRequest:
      required:
      - appServiceId
      - paymentIntentId
      type: object
      properties:
        appServiceId:
          type: string
          description: the app service id
        paymentIntentId:
          type: string
          description: the payment intent id
    v1PostReceiptverifyValidateStripeTransactionResponse:
      required:
      - endDate
      - paymentIntentID
      - startDate
      - store
      - valid
      type: object
      properties:
        valid:
          type: boolean
          description: Receipt is a valid receipt or not
        startDate:
          type: string
          description: the purchase date
          format: date-time
        endDate:
          type: string
          description: the expire date
          format: date-time
        paymentIntentID:
          type: string
          description: the payment intent id
        store:
          $ref: '#/components/schemas/v1Store'
    v1PostReceiptverifyValidateSamsungTransactionRequest:
      required:
      - appServiceId
      - invoiceId
      - platformUserId
      type: object
      properties:
        appServiceId:
          type: string
          description: the app service id
        invoiceId:
          type: string
          description: the invoice id
        platformUserId:
          type: string
          description: the platform (samsung) user id
    v1PostReceiptverifyValidateSamsungTransactionResponse:
      required:
      - charged
      - endDate
      - freeTrial
      - invoiceID
      - nextPaymentDate
      - startDate
      - store
      - valid
      type: object
      properties:
        valid:
          type: boolean
          description: Receipt is a valid receipt or not
        invoiceID:
          type: string
          description: the invoice id
        startDate:
          type: string
          description: the purchase date
          format: date-time
        endDate:
          type: string
          description: the expire date
          format: date-time
        nextPaymentDate:
          type: string
          description: the next payment date
          format: date-time
        freeTrial:
          type: boolean
          description: is free trial
        store:
          $ref: '#/components/schemas/v1Store'
        charged:
          $ref: '#/components/schemas/v1Charged'
    v1IAPResponse:
      required:
      - betaProduct
      - cancelDate
      - cancelReason
      - freeTrialEndDate
      - gracePeriodEndDate
      - parentProductId
      - productId
      - productType
      - purchaseDate
      - quantity
      - receiptId
      - renewalDate
      - term
      - termSku
      - testTransaction
      type: object
      properties:
        receiptId:
          type: string
          description: the receipt id
        productType:
          type: string
          description: the product type
        productId:
          type: string
          description: the product id
        purchaseDate:
          type: number
          description: the purchase date
          format: int64
        renewalDate:
          type: number
          description: the renewal date
          format: int64
        freeTrialEndDate:
          type: number
          description: the free trial end date
          format: int64
        gracePeriodEndDate:
          type: number
          description: the grace period end date
          format: int64
        cancelDate:
          type: number
          description: the cancel date
          format: int64
        cancelReason:
          type: number
          description: the reason for cancel (integer-based enum)
          format: int64
        testTransaction:
          type: boolean
          description: is test transaction
        betaProduct:
          type: boolean
          description: is beta product
        parentProductId:
          type: string
          description: the parent product id
        quantity:
          type: number
          description: the quantity
          format: int64
        term:
          type: string
          description: the term
        termSku:
          type: string
          description: the term sku
    v1Charged:
      required:
      - amount
      - currency
      - date
      type: object
      properties:
        amount:
          type: number
          description: The amount.
          format: double
          example: 9.99
        currency:
          type: string
          description: The currency.
          example: USD
        date:
          type: string
          description: The date.
          format: date-time
          example: 2022-03-02T19:58:14.258Z
      description: the charged information
    v1Plan:
      required:
      - appFamily
      - appId
      - appServiceId
      - createdAt
      - description
      - freeTrial
      - oneTime
      - price
      - productFamily
      - services
      - status
      - tier
      - updatedAt
      type: object
      properties:
        appServiceId:
          type: string
          description: The App service Id.
          example: com.foxsports.qa.apple.tvos.ppv2
        productFamily:
          type: string
          description: Product Family
        description:
          type: string
          description: Description.
          example: PPV 10 description
        deletedAt:
          type: string
          description: Delete Date
          example: 2022-03-02T19:58:14.258Z
        updatedAt:
          type: string
          description: Update Date
          example: 2022-03-02T19:58:14.258Z
        createdAt:
          type: string
          description: Create Date
          example: 2022-03-02T19:58:14.258Z
        tier:
          type: integer
          description: tier value for the current package
          example: 1
        oneTime:
          type: boolean
          description: to differentiate between ppv and recurrent
          example: true
        status:
          type: string
          description: Status of the plan.
          example: ppv10-WEB
        appId:
          type: string
          description: App Id.
          example: foxsports
        appFamily:
          type: string
          description: App family.
          example: standard
        price:
          type: array
          description: Price.
          items:
            required:
            - currencyCode
            - retailPrice
            type: object
            properties:
              currencyCode:
                type: string
                description: Currency Code.
                example: USD
              retailPrice:
                type: number
                description: Retail Price.
                example: 64.99
        services:
          type: array
          items:
            type: object
            properties:
              is_package:
                type: boolean
                description: is package
                example: true
              serviceId:
                type: string
                description: service id
                example: com.foxsports.qa.apple.tvos.ppv2
              serviceName:
                type: string
                description: service name
                example: PPV 10 service name
        freeTrial:
          type: integer
          description: default free trial days
          format: int32
      description: the plan, from plans service
    v1PostReceiptverifyRequest:
      required:
      - appServiceId
      - receipt
      type: object
      properties:
        appServiceId:
          type: string
          description: the app service id
        receipt:
          type: string
          description: the receipt content
        platformUserId:
          type: string
          description: the native platform user id
        allowExpired:
          type: boolean
          description: allow expired receipt
    v1PostReceiptverifyAppleStatusRequest:
      required:
      - appServiceId
      - receipt
      type: object
      properties:
        receipt:
          type: string
        appServiceId:
          type: string
        promoId:
          type: string
        originalTransactionId:
          type: string
        bundleId:
          type: string
    v1PostReceiptverifyAppleStatusResponse:
      type: object
      properties:
        hasUsedTrial:
          type: boolean
        hasUsedPromo:
          type: boolean
        isSubscribed:
          type: boolean
        isChurned:
          type: boolean
    ErrorResponse:
      type: object
      properties:
        error:
          type: object
          properties:
            errorType:
              type: string
              description: Error message
            errorMessage:
              type: string