openapi: 3.0.1
info:
  title: FOX Receipt Verification Service
  description: This service verifies a Receipt
  version: "1.0"
servers:
   - url: https://dev-commerce.fox.com/receiptverify
     description: dev Apigateway
   - url: https://stage-commerce.fox.com/receiptverify
     description: stage Apigateway
   - url: https://commerce.fox.com/receiptverify
     description: prod Apigateway
paths:
  /v1/receiptverify:
    post:
      x-amazon-apigateway-integration:
         responses:
           default:
             statusCode: "200"
         uri: "${nlb_uri}/${service}/v1/receiptverify"
         passthroughBehavior: "when_no_match"
         connectionType: "VPC_LINK"
         connectionId: "${vpclink_id}"
         type: "http_proxy"
         httpMethod: "POST"
      description: This endpoint verifies receipt
      parameters:
      - name: X-Api-Key
        in: header
        required: true
        schema:
          type: string
      requestBody:
        description: verify receipt request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/V1ReceiptVerificationRequest'
        required: false
      responses:
        200:
          description: Receipt is valid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/V1ReceiptVerificationResponse'
        400:
          description: Invalid or missing params.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReceiptVerificationErrorResponse'
        401:
          description: Invalid Headers.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReceiptVerificationErrorResponse'
        424:
          description: If call to native backend is failing.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReceiptVerificationErrorResponse'
  /v1/receiptverify/apple-status:
    post:
      x-amazon-apigateway-integration:
         responses:
           default:
             statusCode: "200"
         uri: "${nlb_uri}/${service}/v1/receiptverify/apple-status"
         passthroughBehavior: "when_no_match"
         connectionType: "VPC_LINK"
         connectionId: "${vpclink_id}"
         type: "http_proxy"
         httpMethod: "POST"
      description: This endpoint returns subscription status from receipt
      parameters:
      - name: X-Api-Key
        in: header
        required: true
        schema:
          type: string
      requestBody:
        description: receipt status request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/V1AppleSubscriptionStatusRequest'
        required: false
      responses:
        200:
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/V1AppleSubscriptionStatusResponse'
        400:
          description: bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReceiptVerificationErrorResponse'
        401:
          description: receipt error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReceiptVerificationErrorResponse'
components:
  schemas:
    V1ReceiptVerificationResponse:
      type: object
      properties:
        valid:
          type: boolean
          description: Receipt is a valid receipt or not
        transactionId:
          type: string
          description: transaction id format e.g. appstore|123456789
        startDate:
          type: string
          description: The time of the original app purchase, in a date-time format
            ISO 8601
        endDate:
          type: string
          description: The time a subscription expires or when it will renew, in a
            date-time format ISO 8601
        lastPaymentTime:
          type: string
          description: the last payment date
          format: date-time
        isFlexibleOfferApplied:
          type: boolean
          description: is flexible offer
        flexibleOfferPrice:
          type: string
          description: the flexible offer price
    V1ReceiptVerificationRequest:
      required:
      - appServiceId
      - receipt
      type: object
      properties:
        receipt:
          type: string
        appServiceId:
          type: string
    V1AppleSubscriptionStatusRequest:
      required:
      - appServiceId
      - receipt
      type: object
      properties:
        receipt:
          type: string
        appServiceId:
          type: string
    V1AppleSubscriptionStatusResponse:
      type: object
      properties:
        hasUsedIntro:
          type: boolean
        hasUsedTrial:
          type: boolean
        hasUsedPromo:
          type: boolean
        isSubscribed:
          type: boolean
        isChurned:
          type: boolean
    ReceiptVerificationErrorResponse:
      type: object
      properties:
        error:
          type: object
          properties:
            errorType:
              type: string
              description: Error message
            errorMessage:
              type: string